import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { Request as ExpressRequest } from 'express';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    create(req: ExpressRequest, createOrderDto: CreateOrderDto): Promise<{
        productPage: {
            id: string;
            name: string;
            slug: string;
        };
        items: ({
            product: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                tags: string[];
                isActive: boolean;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                descriptionAr: string | null;
                descriptionFr: string | null;
                price: import("@prisma/client/runtime/library").Decimal;
                compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
                sku: string | null;
                barcode: string | null;
                weight: number | null;
                imageUrl: string | null;
                stock: number;
            };
            variant: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                isActive: boolean;
                price: import("@prisma/client/runtime/library").Decimal;
                sku: string | null;
                barcode: string | null;
                weight: number | null;
                stock: number;
                productId: string;
            } | null;
        } & {
            id: string;
            name: string;
            price: import("@prisma/client/runtime/library").Decimal;
            productId: string;
            variantId: string | null;
            quantity: number;
            total: import("@prisma/client/runtime/library").Decimal;
            orderId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    }>;
    findAll(productPageId?: string): Promise<({
        productPage: {
            id: string;
            name: string;
            slug: string;
        };
        items: ({
            product: {
                id: string;
                name: string;
                imageUrl: string | null;
            };
            variant: {
                id: string;
                name: string;
            } | null;
        } & {
            id: string;
            name: string;
            price: import("@prisma/client/runtime/library").Decimal;
            productId: string;
            variantId: string | null;
            quantity: number;
            total: import("@prisma/client/runtime/library").Decimal;
            orderId: string;
        })[];
        buyer: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    })[]>;
    findMyOrders(req: ExpressRequest): Promise<({
        productPage: {
            id: string;
            name: string;
            slug: string;
        };
        items: ({
            product: {
                id: string;
                name: string;
                imageUrl: string | null;
            };
            variant: {
                id: string;
                name: string;
            } | null;
        } & {
            id: string;
            name: string;
            price: import("@prisma/client/runtime/library").Decimal;
            productId: string;
            variantId: string | null;
            quantity: number;
            total: import("@prisma/client/runtime/library").Decimal;
            orderId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    })[]>;
    findByProductPage(productPageId: string, req: ExpressRequest): Promise<({
        items: ({
            product: {
                id: string;
                name: string;
                imageUrl: string | null;
            };
            variant: {
                id: string;
                name: string;
            } | null;
        } & {
            id: string;
            name: string;
            price: import("@prisma/client/runtime/library").Decimal;
            productId: string;
            variantId: string | null;
            quantity: number;
            total: import("@prisma/client/runtime/library").Decimal;
            orderId: string;
        })[];
        buyer: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    })[]>;
    findOne(id: string): Promise<{
        productPage: {
            id: string;
            name: string;
            userId: string;
            slug: string;
        };
        items: ({
            product: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                tags: string[];
                isActive: boolean;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                descriptionAr: string | null;
                descriptionFr: string | null;
                price: import("@prisma/client/runtime/library").Decimal;
                compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
                sku: string | null;
                barcode: string | null;
                weight: number | null;
                imageUrl: string | null;
                stock: number;
            };
            variant: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                isActive: boolean;
                price: import("@prisma/client/runtime/library").Decimal;
                sku: string | null;
                barcode: string | null;
                weight: number | null;
                stock: number;
                productId: string;
            } | null;
        } & {
            id: string;
            name: string;
            price: import("@prisma/client/runtime/library").Decimal;
            productId: string;
            variantId: string | null;
            quantity: number;
            total: import("@prisma/client/runtime/library").Decimal;
            orderId: string;
        })[];
        buyer: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    }>;
    update(id: string, updateOrderDto: UpdateOrderDto, req: ExpressRequest): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    }>;
    remove(id: string, req: ExpressRequest): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        productPageId: string;
        customerInfo: import("@prisma/client/runtime/library").JsonValue;
        paymentMethod: string;
        shippingAddress: import("@prisma/client/runtime/library").JsonValue;
        billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
        notes: string | null;
        shippingAmount: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        status: string;
        paymentStatus: string;
        orderNumber: string;
        buyerUserId: string | null;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
    }>;
}
