{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAOM,SAAS,yCAA+C,WAAkB;IAC/E,IAAI,QAAQ;IACZ,MAAM,WAAW,GAAG;IACpB,MAAM,kBAAkB,GAAG,IAAM,MAAM,gBAAgB;IACvD,2FAA2F;IAC3F,MAAM,oBAAoB,GAAG,IAAM,AAAC,MAAc,YAAY;IAC9D,MAAM,OAAO,GAAG,KAAO;IACvB,OAAO;AACT;AAEO,SAAS,0CAAe,KAAY,EAAE,MAAe;IAC1D,OAAO,cAAc,CAAC,OAAO,UAAU;QAAC,OAAO;IAAM;IACrD,OAAO,cAAc,CAAC,OAAO,iBAAiB;QAAC,OAAO;IAAM;AAC9D;AAEO,SAAS,0CAAwD,MAA4C;IAClH,IAAI,WAAW,CAAA,GAAA,aAAK,EAAE;QACpB,WAAW;QACX,UAAU;IACZ;IAEA,mDAAmD;IAEnD,CAAA,GAAA,sBAAc,EAAE;QACd,MAAM,QAAQ,SAAS,OAAO;QAC9B,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,QAAQ,CAAC,UAAU;gBACzB,MAAM,QAAQ,GAAG;YACnB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,eAAe,CAAA,GAAA,qBAAa,EAAE,CAAC;QACjC,mBAAA,6BAAA,OAAS;IACX;IAEA,wDAAwD;IACxD,OAAO,CAAA,GAAA,kBAAU,EAAE,CAAC;QAClB,wGAAwG;QACxG,sGAAsG;QACtG,6FAA6F;QAC7F,qGAAqG;QACrG,IACE,EAAE,MAAM,YAAY,qBACpB,EAAE,MAAM,YAAY,oBACpB,EAAE,MAAM,YAAY,uBACpB,EAAE,MAAM,YAAY,mBACpB;YACA,SAAS,OAAO,CAAC,SAAS,GAAG;YAE7B,IAAI,SAAS,EAAE,MAAM;YACrB,IAAI,gBAA2D,CAAC;gBAC9D,SAAS,OAAO,CAAC,SAAS,GAAG;gBAE7B,IAAI,OAAO,QAAQ,EAAE;oBACnB,uEAAuE;oBACvE,IAAI,QAAQ,yCAA8C;oBAC1D,aAAa;gBACf;gBAEA,qEAAqE;gBACrE,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;oBAC7B,SAAS,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpC,SAAS,OAAO,CAAC,QAAQ,GAAG;gBAC9B;YACF;YAEA,OAAO,gBAAgB,CAAC,YAAY,eAAe;gBAAC,MAAM;YAAI;YAE9D,SAAS,OAAO,CAAC,QAAQ,GAAG,IAAI,iBAAiB;gBAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,IAAI,OAAO,QAAQ,EAAE;wBACjD;qBAAA,6BAAA,SAAS,OAAO,CAAC,QAAQ,cAAzB,iDAAA,2BAA2B,UAAU;oBACrC,IAAI,kBAAkB,WAAW,SAAS,aAAa,GAAG,OAAO,SAAS,aAAa;oBACvF,OAAO,aAAa,CAAC,IAAI,WAAW,QAAQ;wBAAC,eAAe;oBAAe;oBAC3E,OAAO,aAAa,CAAC,IAAI,WAAW,YAAY;wBAAC,SAAS;wBAAM,eAAe;oBAAe;gBAChG;YACF;YAEA,SAAS,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;gBAAC,YAAY;gBAAM,iBAAiB;oBAAC;iBAAW;YAAA;QAC5F;IACF,GAAG;QAAC;KAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,MAA+B;IAC1D,uEAAuE;IACvE,MAAO,UAAU,CAAC,CAAA,GAAA,kBAAU,EAAE,QAC5B,SAAS,OAAO,aAAa;IAG/B,IAAI,SAAS,CAAA,GAAA,qBAAa,EAAE;IAC5B,IAAI,gBAAgB,OAAO,QAAQ,CAAC,aAAa;IACjD,IAAI,CAAC,iBAAiB,kBAAkB,QACtC;IAGF,4CAAmB;IACnB,IAAI,eAAe;IACnB,IAAI,SAAS,CAAC;QACZ,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAChC,EAAE,wBAAwB;IAE9B;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAAc;YAC9C,EAAE,wBAAwB;YAE1B,qEAAqE;YACrE,6CAA6C;YAC7C,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC5B,eAAe;gBACf,CAAA,GAAA,4BAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,EAAE,MAAM,KAAK,UAAU,cACzB,EAAE,wBAAwB;IAE9B;IAEA,IAAI,YAAY,CAAC;QACf,IAAI,EAAE,MAAM,KAAK,UAAU,cAAc;YACvC,EAAE,wBAAwB;YAE1B,IAAI,CAAC,cAAc;gBACjB,eAAe;gBACf,CAAA,GAAA,4BAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,OAAO,gBAAgB,CAAC,QAAQ,QAAQ;IACxC,OAAO,gBAAgB,CAAC,YAAY,YAAY;IAChD,OAAO,gBAAgB,CAAC,WAAW,WAAW;IAC9C,OAAO,gBAAgB,CAAC,SAAS,SAAS;IAE1C,IAAI,UAAU;QACZ,qBAAqB;QACrB,OAAO,mBAAmB,CAAC,QAAQ,QAAQ;QAC3C,OAAO,mBAAmB,CAAC,YAAY,YAAY;QACnD,OAAO,mBAAmB,CAAC,WAAW,WAAW;QACjD,OAAO,mBAAmB,CAAC,SAAS,SAAS;QAC7C,4CAAmB;QACnB,eAAe;IACjB;IAEA,IAAI,MAAM,sBAAsB;IAChC,OAAO;AACT", "sources": ["packages/@react-aria/interactions/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, SyntheticEvent, useCallback, useRef} from 'react';\n\n// Turn a native event into a React synthetic event.\nexport function createSyntheticEvent<E extends SyntheticEvent>(nativeEvent: Event): E {\n  let event = nativeEvent as any as E;\n  event.nativeEvent = nativeEvent;\n  event.isDefaultPrevented = () => event.defaultPrevented;\n  // cancelBubble is technically deprecated in the spec, but still supported in all browsers.\n  event.isPropagationStopped = () => (event as any).cancelBubble;\n  event.persist = () => {};\n  return event;\n}\n\nexport function setEventTarget(event: Event, target: Element): void {\n  Object.defineProperty(event, 'target', {value: target});\n  Object.defineProperty(event, 'currentTarget', {value: target});\n}\n\nexport function useSyntheticBlurEvent<Target extends Element = Element>(onBlur: (e: ReactFocusEvent<Target>) => void): (e: ReactFocusEvent<Target>) => void {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n\n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: ReactFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          let event = createSyntheticEvent<ReactFocusEvent<Target>>(e);\n          dispatchBlur(event);\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null): (() => void) | undefined {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n\n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n"], "names": [], "version": 3, "file": "utils.module.js.map"}