{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAUM,SAAS,0CAAU,KAAmC,EAAE,YAAqB;IAClF,IAAI,MACF,EAAE,EACF,cAAc,KAAK,EACnB,mBAAmB,UAAU,EAC9B,GAAG;IAEJ,sDAAsD;IACtD,kDAAkD;IAClD,KAAK,CAAA,GAAA,yCAAI,EAAE;IACX,IAAI,cAAc,OAAO;QACvB,IAAI,MAAM,IAAI,IAAI;YAAC;eAAO,WAAW,IAAI,GAAG,KAAK,CAAC;SAAO;QACzD,aAAa;eAAI;SAAI,CAAC,IAAI,CAAC;IAC7B,OAAO,IAAI,YACT,aAAa,WAAW,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC;IAGnD,6CAA6C;IAC7C,IAAI,CAAC,SAAS,CAAC,cAAc,cAC3B,QAAQ;IAGV,OAAO;YACL;QACA,cAAc;QACd,mBAAmB;IACrB;AACF", "sources": ["packages/@react-aria/utils/src/useLabels.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {useId} from './useId';\n\n/**\n * Merges aria-label and aria-labelledby into aria-labelledby when both exist.\n * @param props - Aria label props.\n * @param defaultLabel - Default value for aria-label when not present.\n */\nexport function useLabels(props: DOMProps & AriaLabelingProps, defaultLabel?: string): DOMProps & AriaLabelingProps {\n  let {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  } = props;\n\n  // If there is both an aria-label and aria-labelledby,\n  // combine them by pointing to the element itself.\n  id = useId(id);\n  if (labelledBy && label) {\n    let ids = new Set([id, ...labelledBy.trim().split(/\\s+/)]);\n    labelledBy = [...ids].join(' ');\n  } else if (labelledBy) {\n    labelledBy = labelledBy.trim().split(/\\s+/).join(' ');\n  }\n\n  // If no labels are provided, use the default\n  if (!label && !labelledBy && defaultLabel) {\n    label = defaultLabel;\n  }\n\n  return {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  };\n}\n"], "names": [], "version": 3, "file": "useLabels.module.js.map"}