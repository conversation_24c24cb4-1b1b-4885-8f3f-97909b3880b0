"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../common/prisma/prisma.service");
const product_pages_service_1 = require("../product-pages/product-pages.service");
const utils_1 = require("@sharyou/utils");
let OrdersService = class OrdersService {
    prisma;
    productPagesService;
    constructor(prisma, productPagesService) {
        this.prisma = prisma;
        this.productPagesService = productPagesService;
    }
    async create(userId, createOrderDto) {
        const productPage = await this.prisma.productPage.findUnique({
            where: { id: createOrderDto.productPageId },
        });
        if (!productPage) {
            throw new common_1.NotFoundException('Product page not found');
        }
        let subtotal = 0;
        const orderItems = [];
        for (const item of createOrderDto.items) {
            const product = await this.prisma.product.findUnique({
                where: { id: item.productId },
                include: {
                    variants: true,
                },
            });
            if (!product || product.productPageId !== createOrderDto.productPageId) {
                throw new common_1.BadRequestException(`Product ${item.productId} not found or doesn't belong to this store`);
            }
            if (!product.isActive) {
                throw new common_1.BadRequestException(`Product ${product.name} is not available`);
            }
            let price = product.price;
            let variant = null;
            if (item.variantId) {
                variant = product.variants.find(v => v.id === item.variantId);
                if (!variant || !variant.isActive) {
                    throw new common_1.BadRequestException(`Product variant not found or not available`);
                }
                price = variant.price;
                if (variant.stock < item.quantity) {
                    throw new common_1.BadRequestException(`Insufficient stock for variant ${variant.name}`);
                }
            }
            else {
                if (product.stock < item.quantity) {
                    throw new common_1.BadRequestException(`Insufficient stock for product ${product.name}`);
                }
            }
            const itemTotal = Number(price) * item.quantity;
            subtotal += itemTotal;
            orderItems.push({
                productId: item.productId,
                variantId: item.variantId || null,
                name: product.name,
                price: Number(price),
                quantity: item.quantity,
                total: itemTotal,
            });
        }
        const shippingAmount = createOrderDto.shippingAmount || 0;
        const discountAmount = createOrderDto.discountAmount || 0;
        const taxAmount = 0;
        const totalAmount = subtotal + shippingAmount - discountAmount + taxAmount;
        const orderNumber = (0, utils_1.generateOrderNumber)();
        const order = await this.prisma.order.create({
            data: {
                orderNumber,
                productPageId: createOrderDto.productPageId,
                buyerUserId: userId,
                customerInfo: createOrderDto.customerInfo || {},
                totalAmount,
                subtotal,
                taxAmount,
                shippingAmount,
                discountAmount,
                status: 'pending',
                paymentMethod: createOrderDto.paymentMethod,
                paymentStatus: createOrderDto.paymentMethod === 'cod' ? 'pending' : 'pending',
                shippingAddress: createOrderDto.shippingAddress,
                billingAddress: createOrderDto.billingAddress || createOrderDto.shippingAddress,
                notes: createOrderDto.notes,
                items: {
                    create: orderItems,
                },
            },
            include: {
                items: {
                    include: {
                        product: true,
                        variant: true,
                    },
                },
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
            },
        });
        for (const item of createOrderDto.items) {
            if (item.variantId) {
                await this.prisma.productVariant.update({
                    where: { id: item.variantId },
                    data: {
                        stock: {
                            decrement: item.quantity,
                        },
                    },
                });
            }
            else {
                await this.prisma.product.update({
                    where: { id: item.productId },
                    data: {
                        stock: {
                            decrement: item.quantity,
                        },
                    },
                });
            }
        }
        return order;
    }
    async findAll(productPageId) {
        const where = productPageId ? { productPageId } : {};
        return this.prisma.order.findMany({
            where,
            include: {
                items: {
                    include: {
                        product: {
                            select: {
                                id: true,
                                name: true,
                                imageUrl: true,
                            },
                        },
                        variant: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
                buyer: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findByProductPage(productPageId, userId) {
        const hasAccess = await this.productPagesService.checkOwnership(productPageId, userId);
        if (!hasAccess) {
            throw new common_1.ForbiddenException('You can only view orders from your own product pages');
        }
        return this.prisma.order.findMany({
            where: { productPageId },
            include: {
                items: {
                    include: {
                        product: {
                            select: {
                                id: true,
                                name: true,
                                imageUrl: true,
                            },
                        },
                        variant: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                buyer: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findOne(id) {
        const order = await this.prisma.order.findUnique({
            where: { id },
            include: {
                items: {
                    include: {
                        product: true,
                        variant: true,
                    },
                },
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                        userId: true,
                    },
                },
                buyer: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        if (!order) {
            throw new common_1.NotFoundException(`Order with ID ${id} not found`);
        }
        return order;
    }
    async update(id, userId, updateOrderDto) {
        const order = await this.prisma.order.findUnique({
            where: { id },
            include: {
                productPage: {
                    select: {
                        userId: true,
                    },
                },
            },
        });
        if (!order) {
            throw new common_1.NotFoundException(`Order with ID ${id} not found`);
        }
        if (order.productPage.userId !== userId) {
            throw new common_1.ForbiddenException('You can only update orders from your own product pages');
        }
        return this.prisma.order.update({
            where: { id },
            data: updateOrderDto,
            include: {
                items: {
                    include: {
                        product: true,
                        variant: true,
                    },
                },
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
                buyer: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
    }
    async findByUser(userId) {
        return this.prisma.order.findMany({
            where: { buyerUserId: userId },
            include: {
                items: {
                    include: {
                        product: {
                            select: {
                                id: true,
                                name: true,
                                imageUrl: true,
                            },
                        },
                        variant: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
};
exports.OrdersService = OrdersService;
exports.OrdersService = OrdersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        product_pages_service_1.ProductPagesService])
], OrdersService);
//# sourceMappingURL=orders.service.js.map