"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const config_1 = require("@nestjs/config");
let PrismaService = class PrismaService extends client_1.PrismaClient {
    configService;
    constructor(configService) {
        super({
            datasources: {
                db: {
                    url: configService.get('DATABASE_URL'),
                },
            },
        });
        this.configService = configService;
        this.$use(async (params, next) => {
            const tenantModels = ['product', 'order', 'category', 'analyticMetric'];
            if (tenantModels.includes(params.model?.toLowerCase() || '')) {
                if (params.action === 'findMany' || params.action === 'findFirst') {
                    if (!params.args.where?.productPageId) {
                        throw new Error('ProductPageId is required for tenant-isolated operations');
                    }
                }
                if (params.action === 'create' || params.action === 'update' || params.action === 'upsert') {
                    const data = params.args.data;
                    if (!data?.productPageId) {
                        throw new Error('ProductPageId is required for tenant-isolated operations');
                    }
                }
            }
            return next(params);
        });
    }
    async onModuleInit() {
        await this.$connect();
    }
    async onModuleDestroy() {
        await this.$disconnect();
    }
    async findManyWithTenant(model, productPageId, args) {
        return model.findMany({
            ...args,
            where: {
                ...args?.where,
                productPageId,
            },
        });
    }
    async createWithTenant(model, productPageId, data) {
        return model.create({
            data: {
                ...data,
                productPageId,
            },
        });
    }
    async updateWithTenant(model, productPageId, where, data) {
        return model.update({
            where: {
                ...where,
                productPageId,
            },
            data,
        });
    }
    async deleteWithTenant(model, productPageId, where) {
        return model.delete({
            where: {
                ...where,
                productPageId,
            },
        });
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PrismaService);
//# sourceMappingURL=prisma.service.js.map