generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  // Supabase Auth provides basic user data. This table extends it with app-specific profile info.
  id           String        @id @db.Uuid // Matches Supabase Auth.users.id
  email        String        @unique
  firstName    String?
  lastName     String?
  phoneNumber  String? // Validated for Algerian format
  role         String        @default("customer") // 'customer', 'vendor', 'admin'
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  productPages ProductPage[] // A user can own multiple product pages
  orders       Order[]       @relation("BuyerOrders") // Orders made by this user (as a buyer)
  reviews      Review[]
  addresses    Address[] // User's saved addresses for checkout

  @@map("users")
}

model ProductPage {
  id           String           @id @default(uuid())
  userId       String           @db.Uuid // FK to User (owner of this product page)
  slug         String           @unique // Used for subdomain (my-shop.sharyou.dz) or path
  customDomain String?          @unique // Optional: User's own domain (my-brand.com)
  name         String
  description  String?
  logoUrl      String? // Stored in Supabase Storage
  templateData Json // Stores chosen template ID and editable content (JSON B)
  published    Boolean          @default(false)
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  products     Product[]
  orders       Order[]          @relation("StoreOrders")
  metrics      AnalyticMetric[] // Link to analytical data
  categories   Category[]

  @@map("product_pages")
}

model Product {
  id                String            @id @default(uuid())
  productPageId     String            @db.Uuid // FK to ProductPage (the store this product belongs to)
  name              String
  nameAr            String? // Arabic name
  nameFr            String? // French name
  description       String?
  descriptionAr     String? // Arabic description
  descriptionFr     String? // French description
  price             Decimal           @db.Decimal(10, 2) // Stored in DZD
  compareAtPrice    Decimal?          @db.Decimal(10, 2) // Compare at price in DZD
  sku               String?
  barcode           String?
  weight            Float? // Weight in grams
  imageUrl          String? // Primary image stored in Supabase Storage
  stock             Int               @default(0)
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  productPage       ProductPage       @relation(fields: [productPageId], references: [id], onDelete: Cascade)
  orderItems        OrderItem[]
  reviews           Review[]
  images            ProductImage[]
  variants          ProductVariant[]
  productCategories ProductCategory[]
  tags              String[] // Array of tag strings

  @@map("products")
}

model ProductImage {
  id        String   @id @default(uuid())
  productId String   @db.Uuid
  url       String
  altText   String?
  position  Int      @default(0)
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductVariant {
  id         String          @id @default(uuid())
  productId  String          @db.Uuid
  name       String
  price      Decimal         @db.Decimal(10, 2)
  sku        String?
  barcode    String?
  weight     Float?
  stock      Int             @default(0)
  isActive   Boolean         @default(true)
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt
  product    Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]
  options    VariantOption[]

  @@map("product_variants")
}

model VariantOption {
  id        String         @id @default(uuid())
  variantId String         @db.Uuid
  name      String // e.g., "Color", "Size"
  value     String // e.g., "Red", "Large"
  variant   ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("variant_options")
}

model Category {
  id                String            @id @default(uuid())
  productPageId     String            @db.Uuid
  name              String
  nameAr            String?
  nameFr            String?
  slug              String
  description       String?
  parentId          String?           @db.Uuid
  imageUrl          String?
  position          Int               @default(0)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  productPage       ProductPage       @relation(fields: [productPageId], references: [id], onDelete: Cascade)
  parent            Category?         @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children          Category[]        @relation("CategoryHierarchy")
  productCategories ProductCategory[]

  @@unique([productPageId, slug])
  @@map("categories")
}

model ProductCategory {
  id         String   @id @default(uuid())
  productId  String   @db.Uuid
  categoryId String   @db.Uuid
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([productId, categoryId])
  @@map("product_categories")
}

model Order {
  id              String      @id @default(uuid())
  productPageId   String      @db.Uuid // FK to ProductPage (the store where this order was placed)
  orderNumber     String      @unique
  buyerUserId     String?     @db.Uuid // FK to User (the buyer, optional if guest checkout)
  customerInfo    Json // Customer information for guest orders
  totalAmount     Decimal     @db.Decimal(10, 2) // In DZD
  subtotal        Decimal     @db.Decimal(10, 2) // In DZD
  taxAmount       Decimal     @default(0) @db.Decimal(10, 2) // In DZD
  shippingAmount  Decimal     @default(0) @db.Decimal(10, 2) // In DZD
  discountAmount  Decimal     @default(0) @db.Decimal(10, 2) // In DZD
  status          String // 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'
  paymentMethod   String // 'cod', 'cib', 'baridimob', 'bank_transfer'
  paymentStatus   String // 'pending', 'paid', 'failed', 'refunded', 'partially_refunded'
  shippingAddress Json // JSON for detailed Algerian address
  billingAddress  Json? // Optional billing address
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  productPage     ProductPage @relation("StoreOrders", fields: [productPageId], references: [id])
  buyer           User?       @relation("BuyerOrders", fields: [buyerUserId], references: [id])
  items           OrderItem[]

  @@map("orders")
}

model OrderItem {
  id        String          @id @default(uuid())
  orderId   String          @db.Uuid
  productId String          @db.Uuid
  variantId String?         @db.Uuid
  name      String // Product name at time of order
  price     Decimal         @db.Decimal(10, 2) // Price at time of order
  quantity  Int
  total     Decimal         @db.Decimal(10, 2) // price * quantity
  order     Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product         @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model Address {
  id         String   @id @default(uuid())
  userId     String   @db.Uuid
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  wilaya     String // Algerian province
  daira      String // Algerian district
  commune    String // Algerian municipality
  postalCode String
  phone      String
  isDefault  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

model Review {
  id         String   @id @default(uuid())
  productId  String   @db.Uuid
  userId     String   @db.Uuid
  rating     Int // 1-5 stars
  title      String?
  content    String?
  isVerified Boolean  @default(false) // Verified purchase
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([productId, userId]) // One review per user per product
  @@map("reviews")
}

model AnalyticMetric {
  id             String      @id @default(uuid())
  productPageId  String      @db.Uuid
  date           DateTime    @db.Date
  visitors       Int         @default(0)
  pageViews      Int         @default(0)
  orders         Int         @default(0)
  revenue        Decimal     @default(0) @db.Decimal(10, 2) // In DZD
  conversionRate Float       @default(0)
  createdAt      DateTime    @default(now())
  productPage    ProductPage @relation(fields: [productPageId], references: [id], onDelete: Cascade)

  @@unique([productPageId, date])
  @@map("analytic_metrics")
}
