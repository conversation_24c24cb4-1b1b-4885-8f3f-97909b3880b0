(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[286],{449:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},626:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},887:(e,t,r)=>{"use strict";var n=r(9020),i=r(3765),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var s=0|p(n,i),l=o(s),u=l.write(n,i);return u!==s&&(l=l.slice(0,u)),l}if(ArrayBuffer.isView(e)){var c=e;if(x(c,Uint8Array)){var g=new Uint8Array(c);return d(g.buffer,g.byteOffset,g.byteLength)}return h(c)}if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(x(e,ArrayBuffer)||e&&x(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(x(e,SharedArrayBuffer)||e&&x(e.buffer,SharedArrayBuffer)))return d(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var y=e.valueOf&&e.valueOf();if(null!=y&&y!==e)return a.from(y,t,r);var m=function(e){if(a.isBuffer(e)){var t=0|f(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?o(0):h(e):"Buffer"===e.type&&Array.isArray(e.data)?h(e.data):void 0}(e);if(m)return m;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return u(e),o(e<0?0:0|f(e))}function h(e){for(var t=e.length<0?0:0|f(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function d(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),n}function f(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||x(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return A(e).length;default:if(i)return n?-1:O(e).length;t=(""+t).toLowerCase(),i=!0}}function g(e,t,r){var i,s,o,a=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=t;s<r;++s)i+=R[e[s]];return i}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,s=t,o=r,0===s&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(s,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",s=0;s<n.length-1;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}(this,t,r);default:if(a)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),a=!0}}function y(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function m(e,t,r,n,i){var s;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(s=r*=1)!=s&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:w(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return w(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function w(e,t,r,n,i){var s,o=1,a=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,a/=2,l/=2,r/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var c=-1;for(s=r;s<a;s++)if(u(e,s)===u(t,-1===c?0:s-c)){if(-1===c&&(c=s),s-c+1===l)return c*o}else -1!==c&&(s-=s-c),c=-1}else for(r+l>a&&(r=a-l),s=r;s>=0;s--){for(var h=!0,d=0;d<l;d++)if(u(e,s+d)!==u(t,d)){h=!1;break}if(h)return s}return -1}function b(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var s,o,a,l,u=e[i],c=null,h=u>239?4:u>223?3:u>191?2:1;if(i+h<=r)switch(h){case 1:u<128&&(c=u);break;case 2:(192&(s=e[i+1]))==128&&(l=(31&u)<<6|63&s)>127&&(c=l);break;case 3:s=e[i+1],o=e[i+2],(192&s)==128&&(192&o)==128&&(l=(15&u)<<12|(63&s)<<6|63&o)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:s=e[i+1],o=e[i+2],a=e[i+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(l=(15&u)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&l<1114112&&(c=l)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}var d=n,f=d.length;if(f<=4096)return String.fromCharCode.apply(String,d);for(var p="",g=0;g<f;)p+=String.fromCharCode.apply(String,d.slice(g,g+=4096));return p}function v(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function _(e,t,r,n,i,s){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<s)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function k(e,t,r,n,i,s){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function E(e,t,r,n,s){return t*=1,r>>>=0,s||k(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,s){return t*=1,r>>>=0,s||k(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}t.hp=a,t.IS=50,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(u(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},a.allocUnsafe=function(e){return c(e)},a.allocUnsafeSlow=function(e){return c(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(x(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,s=Math.min(r,n);i<s;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=a.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(x(s,Uint8Array))i+s.length>n.length?a.from(s).copy(n,i):Uint8Array.prototype.set.call(n,s,i);else if(a.isBuffer(s))s.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=s.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):g.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.IS;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(e,t,r,n,i){if(x(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var s=i-n,o=r-t,l=Math.min(s,o),u=this.slice(n,i),c=e.slice(t,r),h=0;h<l;++h)if(u[h]!==c[h]){s=u[h],o=c[h];break}return s<o?-1:+(o<s)},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return m(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return m(this,e,t,r,!1)},a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,s,o,a,l,u,c,h,d=this.length-t;if((void 0===r||r>d)&&(r=d),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var f=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=t.length;n>s/2&&(n=s/2);for(var o=0;o<n;++o){var a,l=parseInt(t.substr(2*o,2),16);if((a=l)!=a)break;e[r+o]=l}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,s=r,j(O(e,this.length-i),this,i,s);case"ascii":case"latin1":case"binary":return o=t,a=r,j(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(e),this,o,a);case"base64":return l=t,u=r,j(A(e),this,l,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,h=r,j(function(e,t){for(var r,n,i=[],s=0;s<e.length&&!((t-=2)<0);++s)n=(r=e.charCodeAt(s))>>8,i.push(r%256),i.push(n);return i}(e,this.length-c),this,c,h);default:if(f)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),f=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUintLE=a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=this[e],i=1,s=0;++s<t&&(i*=256);)n+=this[e+s]*i;return n},a.prototype.readUintBE=a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},a.prototype.readUint8=a.prototype.readUInt8=function(e,t){return e>>>=0,t||v(e,1,this.length),this[e]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||v(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||v(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||v(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||v(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=this[e],i=1,s=0;++s<t&&(i*=256);)n+=this[e+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=t,i=1,s=this[e+--n];n>0&&(i*=256);)s+=this[e+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*t)),s},a.prototype.readInt8=function(e,t){return(e>>>=0,t||v(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||v(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||v(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||v(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||v(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||v(e,4,this.length),i.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||v(e,4,this.length),i.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||v(e,8,this.length),i.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||v(e,8,this.length),i.read(this,e,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;_(this,e,t,r,i,0)}var s=1,o=0;for(this[t]=255&e;++o<r&&(s*=256);)this[t+o]=e/s&255;return t+r},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;_(this,e,t,r,i,0)}var s=r-1,o=1;for(this[t+s]=255&e;--s>=0&&(o*=256);)this[t+s]=e/o&255;return t+r},a.prototype.writeUint8=a.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);_(this,e,t,r,i-1,-i)}var s=0,o=1,a=0;for(this[t]=255&e;++s<r&&(o*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/o|0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);_(this,e,t,r,i-1,-i)}var s=r-1,o=1,a=0;for(this[t+s]=255&e;--s>=0&&(o*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/o|0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||_(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return E(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return E(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),i},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,s=e.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(e=s)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=a.isBuffer(e)?e:a.from(e,n),l=o.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%l]}return this};var T=/[^+/0-9A-Za-z-_]/g;function O(e,t){t=t||1/0;for(var r,n=e.length,i=null,s=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&s.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;s.push(r)}else if(r<2048){if((t-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function A(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(T,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function j(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function x(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var R=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},2163:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(7353));class s{constructor(e,{headers:t={},schema:r,fetch:n}){this.url=e,this.headers=t,this.schema=r,this.fetch=n}select(e,{head:t=!1,count:r}={}){let n=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e)).join("");return this.url.searchParams.set("select",s),r&&(this.headers.Prefer=`count=${r}`),new i.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let n=[];if(this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),r||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:n,defaultToNull:s=!0}={}){let o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),n&&o.push(`count=${n}`),s||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new i.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new i.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=s},3235:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(2163)),s=n(r(7353)),o=r(4280);class a{constructor(e,{headers:t={},schema:r,fetch:n}={}){this.url=e,this.headers=Object.assign(Object.assign({},o.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=n}from(e){let t=new URL(`${this.url}/${e}`);return new i.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new a(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:n=!1,count:i}={}){let o,a,l=new URL(`${this.url}/rpc/${e}`);r||n?(o=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(o="POST",a=t);let u=Object.assign({},this.headers);return i&&(u.Prefer=`count=${i}`),new s.default({method:o,url:l,headers:u,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}}t.default=a},3765:(e,t)=>{t.read=function(e,t,r,n,i){var s,o,a=8*i-n-1,l=(1<<a)-1,u=l>>1,c=-7,h=r?i-1:0,d=r?-1:1,f=e[t+h];for(h+=d,s=f&(1<<-c)-1,f>>=-c,c+=a;c>0;s=256*s+e[t+h],h+=d,c-=8);for(o=s&(1<<-c)-1,s>>=-c,c+=n;c>0;o=256*o+e[t+h],h+=d,c-=8);if(0===s)s=1-u;else{if(s===l)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,n),s-=u}return(f?-1:1)*o*Math.pow(2,s-n)},t.write=function(e,t,r,n,i,s){var o,a,l,u=8*s-i-1,c=(1<<u)-1,h=c>>1,d=5960464477539062e-23*(23===i),f=n?0:s-1,p=n?1:-1,g=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(a=+!!isNaN(t),o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),o+h>=1?t+=d/l:t+=d*Math.pow(2,1-h),t*l>=2&&(o++,l/=2),o+h>=c?(a=0,o=c):o+h>=1?(a=(t*l-1)*Math.pow(2,i),o+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),o=0));i>=8;e[r+f]=255&a,f+=p,a/=256,i-=8);for(o=o<<i|a,u+=i;u>0;e[r+f]=255&o,f+=p,o/=256,u-=8);e[r+f-p]|=128*g}},4280:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let n=r(449);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${n.version}`}},4311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>o,Request:()=>a,Response:()=>l,default:()=>s,fetch:()=>i});var n=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let i=n.fetch,s=n.fetch.bind(n),o=n.Headers,a=n.Request,l=n.Response},5175:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(4311)),s=n(r(626));class o{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=i.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,n;let i=null,o=null,a=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let n=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),s=null==(r=e.headers.get("content-range"))?void 0:r.split("/");n&&s&&s.length>1&&(a=parseInt(s[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(i={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,l=406,u="Not Acceptable"):o=1===o.length?o[0]:null)}else{let t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(o=[],i=null,l=200,u="OK")}catch(r){404===e.status&&""===t?(l=204,u="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null==(n=null==i?void 0:i.details)?void 0:n.includes("0 rows"))&&(i=null,l=200,u="OK"),i&&this.shouldThrowOnError)throw new s.default(i)}return{error:i,data:o,count:a,status:l,statusText:u}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,n;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(r=null==e?void 0:e.stack)?r:""}`,hint:"",code:`${null!=(n=null==e?void 0:e.code)?n:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=o},5317:(e,t,r)=>{"use strict";function n(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}r.d(t,{l$:()=>eT,Ay:()=>eO});var i,s=r(7620);let o={data:""},a=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,u=/\/\*[^]*?\*\/|  +/g,c=/\n+/g,h=(e,t)=>{let r="",n="",i="";for(let s in e){let o=e[s];"@"==s[0]?"i"==s[1]?r=s+" "+o+";":n+="f"==s[1]?h(o,s):s+"{"+h(o,"k"==s[1]?"":t)+"}":"object"==typeof o?n+=h(o,t?t.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):s):null!=o&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=h.p?h.p(s,o):s+":"+o+";")}return r+(t&&i?t+"{"+i+"}":i)+n},d={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},p=(e,t,r,n,i)=>{let s=f(e),o=d[s]||(d[s]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(s));if(!d[o]){let t=s!==e?e:(e=>{let t,r,n=[{}];for(;t=l.exec(e.replace(u,""));)t[4]?n.shift():t[3]?(r=t[3].replace(c," ").trim(),n.unshift(n[0][r]=n[0][r]||{})):n[0][t[1]]=t[2].replace(c," ").trim();return n[0]})(e);d[o]=h(i?{["@keyframes "+o]:t}:t,r?"":"."+o)}let a=r&&d.g?d.g:null;return r&&(d.g=d[o]),((e,t,r,n)=>{n?t.data=t.data.replace(n,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(d[o],t,n,a),o},g=(e,t,r)=>e.reduce((e,n,i)=>{let s=t[i];if(s&&s.call){let e=s(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=t?"."+t:e&&"object"==typeof e?e.props?"":h(e,""):!1===e?"":e}return e+n+(null==s?"":s)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return p(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,a(t.target),t.g,t.o,t.k)}y.bind({g:1});let m,w,b,v=y.bind({k:1});function _(e,t){let r=this||{};return function(){let n=arguments;function i(s,o){let a=Object.assign({},s),l=a.className||i.className;r.p=Object.assign({theme:w&&w()},a),r.o=/ *go\d+/.test(l),a.className=y.apply(r,n)+(l?" "+l:""),t&&(a.ref=o);let u=e;return e[0]&&(u=a.as||e,delete a.as),b&&u[0]&&b(a),m(u,a)}return t?t(i):i}}function k(){let e=n(["\nfrom {\n  transform: scale(0) rotate(45deg);\n	opacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}"]);return k=function(){return e},e}function E(){let e=n(["\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]);return E=function(){return e},e}function S(){let e=n(["\nfrom {\n  transform: scale(0) rotate(90deg);\n	opacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n	opacity: 1;\n}"]);return S=function(){return e},e}function T(){let e=n(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ",";\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n"]);return T=function(){return e},e}function O(){let e=n(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]);return O=function(){return e},e}function A(){let e=n(["\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ",";\n  border-right-color: ",";\n  animation: "," 1s linear infinite;\n"]);return A=function(){return e},e}function j(){let e=n(["\nfrom {\n  transform: scale(0) rotate(45deg);\n	opacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n	opacity: 1;\n}"]);return j=function(){return e},e}function x(){let e=n(["\n0% {\n	height: 0;\n	width: 0;\n	opacity: 0;\n}\n40% {\n  height: 0;\n	width: 6px;\n	opacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}"]);return x=function(){return e},e}function R(){let e=n(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: "," 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ",";\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n"]);return R=function(){return e},e}function C(){let e=n(["\n  position: absolute;\n"]);return C=function(){return e},e}function P(){let e=n(["\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n"]);return P=function(){return e},e}function I(){let e=n(["\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]);return I=function(){return e},e}function U(){let e=n(["\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: "," 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n"]);return U=function(){return e},e}function $(){let e=n(["\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n"]);return $=function(){return e},e}function L(){let e=n(["\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n"]);return L=function(){return e},e}function B(){let e=n(["\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n"]);return B=function(){return e},e}var N=e=>"function"==typeof e,D=(e,t)=>N(e)?e(t):e,q=(()=>{let e=0;return()=>(++e).toString()})(),M=(()=>{let e;return()=>{if(void 0===e&&"u">typeof window){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),F=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return F(e,{type:+!!e.toasts.find(e=>e.id===r.id),toast:r});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+i}))}}},z=[],J={toasts:[],pausedAt:void 0},W=e=>{J=F(J,e),z.forEach(e=>{e(J)})},H={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},K=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,s.useState)(J),n=(0,s.useRef)(J);(0,s.useEffect)(()=>(n.current!==J&&r(J),z.push(r),()=>{let e=z.indexOf(r);e>-1&&z.splice(e,1)}),[]);let i=t.toasts.map(t=>{var r,n,i;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(n=e[t.type])?void 0:n.duration)||(null==e?void 0:e.duration)||H[t.type],style:{...e.style,...null==(i=e[t.type])?void 0:i.style,...t.style}}});return{...t,toasts:i}},G=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",r=arguments.length>2?arguments[2]:void 0;return{createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||q()}},V=e=>(t,r)=>{let n=G(t,e,r);return W({type:2,toast:n}),n.id},X=(e,t)=>V("blank")(e,t);X.error=V("error"),X.success=V("success"),X.loading=V("loading"),X.custom=V("custom"),X.dismiss=e=>{W({type:3,toastId:e})},X.remove=e=>W({type:4,toastId:e}),X.promise=(e,t,r)=>{let n=X.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let i=t.success?D(t.success,e):void 0;return i?X.success(i,{id:n,...r,...null==r?void 0:r.success}):X.dismiss(n),e}).catch(e=>{let i=t.error?D(t.error,e):void 0;i?X.error(i,{id:n,...r,...null==r?void 0:r.error}):X.dismiss(n)}),e};var Y=(e,t)=>{W({type:1,toast:{id:e,height:t}})},Q=()=>{W({type:5,time:Date.now()})},Z=new Map,ee=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(Z.has(e))return;let r=setTimeout(()=>{Z.delete(e),W({type:4,toastId:e})},t);Z.set(e,r)},et=e=>{let{toasts:t,pausedAt:r}=K(e);(0,s.useEffect)(()=>{if(r)return;let e=Date.now(),n=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&X.dismiss(t.id);return}return setTimeout(()=>X.dismiss(t.id),r)});return()=>{n.forEach(e=>e&&clearTimeout(e))}},[t,r]);let n=(0,s.useCallback)(()=>{r&&W({type:6,time:Date.now()})},[r]),i=(0,s.useCallback)((e,r)=>{let{reverseOrder:n=!1,gutter:i=8,defaultPosition:s}=r||{},o=t.filter(t=>(t.position||s)===(e.position||s)&&t.height),a=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<a&&e.visible).length;return o.filter(e=>e.visible).slice(...n?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+i,0)},[t]);return(0,s.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)ee(e.id,e.removeDelay);else{let t=Z.get(e.id);t&&(clearTimeout(t),Z.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:Y,startPause:Q,endPause:n,calculateOffset:i}}},er=v(k()),en=v(E()),ei=v(S()),es=_("div")(T(),e=>e.primary||"#ff4b4b",er,en,e=>e.secondary||"#fff",ei),eo=v(O()),ea=_("div")(A(),e=>e.secondary||"#e0e0e0",e=>e.primary||"#616161",eo),el=v(j()),eu=v(x()),ec=_("div")(R(),e=>e.primary||"#61d345",el,eu,e=>e.secondary||"#fff"),eh=_("div")(C()),ed=_("div")(P()),ef=v(I()),ep=_("div")(U(),ef),eg=e=>{let{toast:t}=e,{icon:r,type:n,iconTheme:i}=t;return void 0!==r?"string"==typeof r?s.createElement(ep,null,r):r:"blank"===n?null:s.createElement(ed,null,s.createElement(ea,{...i}),"loading"!==n&&s.createElement(eh,null,"error"===n?s.createElement(es,{...i}):s.createElement(ec,{...i})))},ey=e=>"\n0% {transform: translate3d(0,".concat(-200*e,"%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n"),em=e=>"\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,".concat(-150*e,"%,-1px) scale(.6); opacity:0;}\n"),ew=_("div")($()),eb=_("div")(L()),ev=(e,t)=>{let r=e.includes("top")?1:-1,[n,i]=M()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[ey(r),em(r)];return{animation:t?"".concat(v(n)," 0.35s cubic-bezier(.21,1.02,.73,1) forwards"):"".concat(v(i)," 0.4s forwards cubic-bezier(.06,.71,.55,1)")}},e_=s.memo(e=>{let{toast:t,position:r,style:n,children:i}=e,o=t.height?ev(t.position||r||"top-center",t.visible):{opacity:0},a=s.createElement(eg,{toast:t}),l=s.createElement(eb,{...t.ariaProps},D(t.message,t));return s.createElement(ew,{className:t.className,style:{...o,...n,...t.style}},"function"==typeof i?i({icon:a,message:l}):s.createElement(s.Fragment,null,a,l))});i=s.createElement,h.p=void 0,m=i,w=void 0,b=void 0;var ek=e=>{let{id:t,className:r,style:n,onHeightUpdate:i,children:o}=e,a=s.useCallback(e=>{if(e){let r=()=>{i(t,e.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[t,i]);return s.createElement("div",{ref:a,className:r,style:n},o)},eE=(e,t)=>{let r=e.includes("top"),n=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:M()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:"translateY(".concat(t*(r?1:-1),"px)"),...r?{top:0}:{bottom:0},...n}},eS=y(B()),eT=e=>{let{reverseOrder:t,position:r="top-center",toastOptions:n,gutter:i,children:o,containerStyle:a,containerClassName:l}=e,{toasts:u,handlers:c}=et(n);return s.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...a},className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},u.map(e=>{let n=e.position||r,a=eE(n,c.calculateOffset(e,{reverseOrder:t,gutter:i,defaultPosition:r}));return s.createElement(ek,{id:e.id,key:e.id,onHeightUpdate:c.updateHeight,className:e.visible?eS:"",style:a},"custom"===e.type?D(e.message,e):o?o(e):s.createElement(e_,{toast:e,position:n}))}))},eO=X},5461:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let i=n(r(3235));t.PostgrestClient=i.default;let s=n(r(2163));t.PostgrestQueryBuilder=s.default;let o=n(r(7353));t.PostgrestFilterBuilder=o.default;let a=n(r(9503));t.PostgrestTransformBuilder=a.default;let l=n(r(5175));t.PostgrestBuilder=l.default;let u=n(r(626));t.PostgrestError=u.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:o.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:l.default,PostgrestError:u.default}},6154:(e,t,r)=>{"use strict";let n,i;r.d(t,{createBrowserClient:()=>tX});let s=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,4311)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class o extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class a extends o{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class l extends o{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class u extends o{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(W||(W={}));class c{constructor(e,{headers:t={},customFetch:r,region:n=W.Any}={}){this.url=e,this.headers=t,this.region=n,this.fetch=s(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,n,i,s,o;return n=this,i=void 0,s=void 0,o=function*(){try{let n,i,{headers:s,method:o,body:c}=t,h={},{region:d}=t;d||(d=this.region),d&&"any"!==d&&(h["x-region"]=d),c&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&c instanceof Blob||c instanceof ArrayBuffer?(h["Content-Type"]="application/octet-stream",n=c):"string"==typeof c?(h["Content-Type"]="text/plain",n=c):"undefined"!=typeof FormData&&c instanceof FormData?n=c:(h["Content-Type"]="application/json",n=JSON.stringify(c)));let f=yield this.fetch(`${this.url}/${e}`,{method:o||"POST",headers:Object.assign(Object.assign(Object.assign({},h),this.headers),s),body:n}).catch(e=>{throw new a(e)}),p=f.headers.get("x-relay-error");if(p&&"true"===p)throw new l(f);if(!f.ok)throw new u(f);let g=(null!=(r=f.headers.get("Content-Type"))?r:"text/plain").split(";")[0].trim();return{data:"application/json"===g?yield f.json():"application/octet-stream"===g?yield f.blob():"text/event-stream"===g?f:"multipart/form-data"===g?yield f.formData():yield f.text(),error:null}}catch(e){return{data:null,error:e}}},new(s||(s=Promise))(function(e,t){function r(e){try{l(o.next(e))}catch(e){t(e)}}function a(e){try{l(o.throw(e))}catch(e){t(e)}}function l(t){var n;t.done?e(t.value):((n=t.value)instanceof s?n:new s(function(e){e(n)})).then(r,a)}l((o=o.apply(n,i||[])).next())})}}let{PostgrestClient:h,PostgrestQueryBuilder:d,PostgrestFilterBuilder:f,PostgrestTransformBuilder:p,PostgrestBuilder:g,PostgrestError:y}=r(5461),m="undefined"==typeof window?r(9647):window.WebSocket,w={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(H||(H={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(K||(K={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(G||(G={})),(V||(V={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(X||(X={}));class b{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let n=t.getUint8(1),i=t.getUint8(2),s=this.HEADER_LENGTH+2,o=r.decode(e.slice(s,s+n));s+=n;let a=r.decode(e.slice(s,s+i));return s+=i,{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(s,e.byteLength)))}}}class v{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(Y||(Y={}));let _=(e,t,r={})=>{var n;let i=null!=(n=r.skipTypes)?n:[];return Object.keys(t).reduce((r,n)=>(r[n]=k(n,e,t,i),r),{})},k=(e,t,r,n)=>{let i=t.find(t=>t.name===e),s=null==i?void 0:i.type,o=r[e];return s&&!n.includes(s)?E(s,o):S(o)},E=(e,t)=>{if("_"===e.charAt(0))return j(t,e.slice(1,e.length));switch(e){case Y.bool:return T(t);case Y.float4:case Y.float8:case Y.int2:case Y.int4:case Y.int8:case Y.numeric:case Y.oid:return O(t);case Y.json:case Y.jsonb:return A(t);case Y.timestamp:return x(t);case Y.abstime:case Y.date:case Y.daterange:case Y.int4range:case Y.int8range:case Y.money:case Y.reltime:case Y.text:case Y.time:case Y.timestamptz:case Y.timetz:case Y.tsrange:case Y.tstzrange:default:return S(t)}},S=e=>e,T=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},O=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},A=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},j=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,n=e[r];if("{"===e[0]&&"}"===n){let n,i=e.slice(1,r);try{n=JSON.parse("["+i+"]")}catch(e){n=i?i.split(","):[]}return n.map(e=>E(t,e))}return e},x=e=>"string"==typeof e?e.replace(" ","T"):e,R=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class C{constructor(e,t,r={},n=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(Q||(Q={}));class P{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:n}=this.caller;this.joinRef=this.channel._joinRef(),this.state=P.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=P.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],n()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:n}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=P.syncDiff(this.state,e,t,r),n())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,n){let i=this.cloneDeep(e),s=this.transformState(t),o={},a={};return this.map(i,(e,t)=>{s[e]||(a[e]=t)}),this.map(s,(e,t)=>{let r=i[e];if(r){let n=t.map(e=>e.presence_ref),i=r.map(e=>e.presence_ref),s=t.filter(e=>0>i.indexOf(e.presence_ref)),l=r.filter(e=>0>n.indexOf(e.presence_ref));s.length>0&&(o[e]=s),l.length>0&&(a[e]=l)}else o[e]=t}),this.syncDiff(i,{joins:o,leaves:a},r,n)}static syncDiff(e,t,r,n){let{joins:i,leaves:s}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),n||(n=()=>{}),this.map(i,(t,n)=>{var i;let s=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(n),s.length>0){let r=e[t].map(e=>e.presence_ref),n=s.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...n)}r(t,s,n)}),this.map(s,(t,r)=>{let i=e[t];if(!i)return;let s=r.map(e=>e.presence_ref);i=i.filter(e=>0>s.indexOf(e.presence_ref)),e[t]=i,n(t,i,r),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let n=e[r];return"metas"in n?t[r]=n.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=n,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(Z||(Z={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(ee||(ee={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(et||(et={}));class I{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=K.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new C(this,G.join,this.params,this.timeout),this.rejoinTimer=new v(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=K.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=K.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=K.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=K.errored,this.rejoinTimer.scheduleTimeout())}),this._on(G.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new P(this),this.broadcastEndpointURL=R(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,n;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:i,presence:s,private:o}}=this.params;this._onError(t=>null==e?void 0:e(et.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(et.CLOSED));let a={},l={broadcast:i,presence:s,postgres_changes:null!=(n=null==(r=this.bindings.postgres_changes)?void 0:r.map(e=>e.filter))?n:[],private:o};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(et.SUBSCRIBED);return}{let n=this.bindings.postgres_changes,i=null!=(r=null==n?void 0:n.length)?r:0,s=[];for(let r=0;r<i;r++){let i=n[r],{filter:{event:o,schema:a,table:l,filter:u}}=i,c=t&&t[r];if(c&&c.event===o&&c.schema===a&&c.table===l&&c.filter===u)s.push(Object.assign(Object.assign({},i),{id:c.id}));else{this.unsubscribe(),this.state=K.errored,null==e||e(et.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=s,e&&e(et.SUBSCRIBED);return}}).receive("error",t=>{this.state=K.errored,null==e||e(et.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(et.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,n;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var n,i,s;let o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(s=null==(i=null==(n=this.params)?void 0:n.config)?void 0:i.broadcast)?void 0:s.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{let{event:i,payload:s}=e,o={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:s,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,o,null!=(r=t.timeout)?r:this.timeout);return await (null==(n=e.body)?void 0:n.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=K.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(G.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{let n=new C(this,G.leave,{},e);n.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),n.send(),this._canPush()||n.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let n=new AbortController,i=setTimeout(()=>n.abort(),r),s=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:n.signal}));return clearTimeout(i),s}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new C(this,e,t,r);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var n,i;let s=e.toLocaleLowerCase(),{close:o,error:a,leave:l,join:u}=G;if(r&&[o,a,l,u].indexOf(s)>=0&&r!==this._joinRef())return;let c=this._onMessage(s,t,r);if(t&&!c)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(s)?null==(n=this.bindings.postgres_changes)||n.filter(e=>{var t,r,n;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(n=null==(r=e.filter)?void 0:r.event)?void 0:n.toLocaleLowerCase())===s}).map(e=>e.callback(c,r)):null==(i=this.bindings[s])||i.filter(e=>{var r,n,i,o,a,l;if(!["broadcast","presence","postgres_changes"].includes(s))return e.type.toLocaleLowerCase()===s;if("id"in e){let s=e.id,o=null==(r=e.filter)?void 0:r.event;return s&&(null==(n=t.ids)?void 0:n.includes(s))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let r=null==(a=null==(o=null==e?void 0:e.filter)?void 0:o.event)?void 0:a.toLocaleLowerCase();return"*"===r||r===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof c&&"ids"in c){let e=c.data,{schema:t,table:r,commit_timestamp:n,type:i,errors:s}=e;c=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:n,eventType:i,new:{},old:{},errors:s}),this._getPayloadRecords(e))}e.callback(c,r)})}_isClosed(){return this.state===K.closed}_isJoined(){return this.state===K.joined}_isJoining(){return this.state===K.joining}_isLeaving(){return this.state===K.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let n=e.toLocaleLowerCase(),i={type:n,filter:t,callback:r};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var n;return!((null==(n=e.type)?void 0:n.toLocaleLowerCase())===r&&I.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(G.close,{},e)}_onError(e){this._on(G.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=K.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=_(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=_(e.columns,e.old_record)),t}}let U=()=>{},$=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class L{constructor(e,t){var n;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=w,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=U,this.ref=0,this.logger=U,this.conn=null,this.sendBuffer=[],this.serializer=new b,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,4311)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${V.websocket}`,this.httpEndpoint=R(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let i=null==(n=null==t?void 0:t.params)?void 0:n.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new v(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=m),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new B(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case H.connecting:return X.Connecting;case H.open:return X.Open;case H.closing:return X.Closing;default:return X.Closed}}isConnected(){return this.connectionState()===X.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,n=this.getChannels().find(e=>e.topic===r);if(n)return n;{let r=new I(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:n,ref:i}=e,s=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${r} (${i})`,n),this.isConnected()?s():this.sendBuffer.push(s)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(G.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:n,ref:i}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,n),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,n,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(G.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",n=new URLSearchParams(t);return`${e}${r}${n}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([$],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class B{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=H.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class N extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function D(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class q extends N{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class M extends N{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let F=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,4311)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},z=()=>(function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,4311))).Response:Response}),J=e=>{if(Array.isArray(e))return e.map(e=>J(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=J(r)}),t};var W,H,K,G,V,X,Y,Q,Z,ee,et,er=function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})};let en=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ei=(e,t,r)=>er(void 0,void 0,void 0,function*(){e instanceof(yield z())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new q(en(r),e.status||500))}).catch(e=>{t(new M(en(e),e))}):t(new M(en(e),e))}),es=(e,t,r,n)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n&&(i.body=JSON.stringify(n)),Object.assign(Object.assign({},i),r))};function eo(e,t,r,n,i,s){return er(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,es(t,n,i,s)).then(e=>{if(!e.ok)throw e;return(null==n?void 0:n.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>ei(e,a,n))})})}function ea(e,t,r,n){return er(this,void 0,void 0,function*(){return eo(e,"GET",t,r,n)})}function el(e,t,r,n,i){return er(this,void 0,void 0,function*(){return eo(e,"POST",t,n,i,r)})}function eu(e,t,r,n,i){return er(this,void 0,void 0,function*(){return eo(e,"DELETE",t,n,i,r)})}var ec=r(887).hp,eh=function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})};let ed={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ef={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ep{constructor(e,t={},r,n){this.url=e,this.headers=t,this.bucketId=r,this.fetch=F(n)}uploadOrUpdate(e,t,r,n){return eh(this,void 0,void 0,function*(){try{let i,s=Object.assign(Object.assign({},ef),n),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(s.upsert)}),a=s.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((i=new FormData).append("cacheControl",s.cacheControl),a&&i.append("metadata",this.encodeMetadata(a)),i.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((i=r).append("cacheControl",s.cacheControl),a&&i.append("metadata",this.encodeMetadata(a))):(i=r,o["cache-control"]=`max-age=${s.cacheControl}`,o["content-type"]=s.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==n?void 0:n.headers)&&(o=Object.assign(Object.assign({},o),n.headers));let l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:i,headers:o},(null==s?void 0:s.duplex)?{duplex:s.duplex}:{})),h=yield c.json();if(c.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(D(e))return{data:null,error:e};throw e}})}upload(e,t,r){return eh(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,n){return eh(this,void 0,void 0,function*(){let i=this._removeEmptyFolders(e),s=this._getFinalPath(i),o=new URL(this.url+`/object/upload/sign/${s}`);o.searchParams.set("token",t);try{let e,t=Object.assign({upsert:ef.upsert},n),s=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,s["cache-control"]=`max-age=${t.cacheControl}`,s["content-type"]=t.contentType);let a=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:s}),l=yield a.json();if(a.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return eh(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),n=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(n["x-upsert"]="true");let i=yield el(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:n}),s=new URL(this.url+i.url),o=s.searchParams.get("token");if(!o)throw new N("No token returned by API");return{data:{signedUrl:s.toString(),path:e,token:o},error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}update(e,t,r){return eh(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return eh(this,void 0,void 0,function*(){try{return{data:yield el(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}copy(e,t,r){return eh(this,void 0,void 0,function*(){try{return{data:{path:(yield el(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return eh(this,void 0,void 0,function*(){try{let n=this._getFinalPath(e),i=yield el(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i={signedUrl:encodeURI(`${this.url}${i.signedURL}${s}`)},error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return eh(this,void 0,void 0,function*(){try{let n=yield el(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}download(e,t){return eh(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),n=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=n?`?${n}`:"";try{let t=this._getFinalPath(e),n=yield ea(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield n.blob(),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}info(e){return eh(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield ea(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:J(e),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}exists(e){return eh(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,n){return er(this,void 0,void 0,function*(){return eo(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(D(e)&&e instanceof M){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),n=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&n.push(i);let s=void 0!==(null==t?void 0:t.transform),o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&n.push(o);let a=n.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${s?"render/image":"object"}/public/${r}${a}`)}}}remove(e){return eh(this,void 0,void 0,function*(){try{return{data:yield eu(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}list(e,t,r){return eh(this,void 0,void 0,function*(){try{let n=Object.assign(Object.assign(Object.assign({},ed),t),{prefix:e||""});return{data:yield el(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},r),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ec?ec.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let eg={"X-Client-Info":"storage-js/2.7.1"};var ey=function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})};class em{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},eg),t),this.fetch=F(r)}listBuckets(){return ey(this,void 0,void 0,function*(){try{return{data:yield ea(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}getBucket(e){return ey(this,void 0,void 0,function*(){try{return{data:yield ea(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return ey(this,void 0,void 0,function*(){try{return{data:yield el(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return ey(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,n,i){return er(this,void 0,void 0,function*(){return eo(e,"PUT",t,n,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}emptyBucket(e){return ey(this,void 0,void 0,function*(){try{return{data:yield el(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}deleteBucket(e){return ey(this,void 0,void 0,function*(){try{return{data:yield eu(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(D(e))return{data:null,error:e};throw e}})}}class ew extends em{constructor(e,t={},r){super(e,t,r)}from(e){return new ep(this.url,this.headers,e,this.fetch)}}let eb="";eb="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let ev={headers:{"X-Client-Info":`supabase-js-${eb}/2.50.0`}},e_={schema:"public"},ek={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eE={};var eS=r(4311);let eT=e=>{let t;return t=e||("undefined"==typeof fetch?eS.default:fetch),(...e)=>t(...e)},eO=()=>"undefined"==typeof Headers?eS.Headers:Headers,eA=(e,t,r)=>{let n=eT(r),i=eO();return(r,s)=>(function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var o;let a=null!=(o=yield t())?o:e,l=new i(null==s?void 0:s.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),n(r,Object.assign(Object.assign({},s),{headers:l}))})},ej="2.70.0",ex={"X-Client-Info":`gotrue-js/${ej}`},eR="X-Supabase-Api-Version",eC={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},eP=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class eI extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function eU(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class e$ extends eI{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class eL extends eI{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class eB extends eI{constructor(e,t,r,n){super(e,r,n),this.name=t,this.status=r}}class eN extends eB{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class eD extends eB{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class eq extends eB{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class eM extends eB{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eF extends eB{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ez extends eB{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function eJ(e){return eU(e)&&"AuthRetryableFetchError"===e.name}class eW extends eB{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class eH extends eB{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let eK="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),eG=" 	\n\r=".split(""),eV=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<eG.length;t+=1)e[eG[t].charCodeAt(0)]=-2;for(let t=0;t<eK.length;t+=1)e[eK[t].charCodeAt(0)]=t;return e})();function eX(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(eK[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(eK[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function eY(e,t,r){let n=eV[e];if(n>-1)for(t.queue=t.queue<<6|n,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===n)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function eQ(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},s=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,n,r)};for(let t=0;t<e.length;t+=1)eY(e.charCodeAt(t),i,s);return t.join("")}let eZ=()=>"undefined"!=typeof window&&"undefined"!=typeof document,e0={tested:!1,writable:!1},e1=()=>{if(!eZ())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(e0.tested)return e0.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),e0.tested=!0,e0.writable=!0}catch(e){e0.tested=!0,e0.writable=!1}return e0.writable},e2=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,4311)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},e5=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,e6=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},e3=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},e8=async(e,t)=>{await e.removeItem(t)};class e4{constructor(){this.promise=new e4.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function e9(e){let t=e.split(".");if(3!==t.length)throw new eH("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!eP.test(t[e]))throw new eH("JWT not in base64url format");return{header:JSON.parse(eQ(t[0])),payload:JSON.parse(eQ(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},n=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)eY(e.charCodeAt(t),r,n);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function e7(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function te(e){return("0"+e.toString(16)).substr(-2)}async function tt(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function tr(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await tt(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function tn(e,t,r=!1){let n=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let n=0;n<56;n++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,te).join("")}(),i=n;r&&(i+="/PASSWORD_RECOVERY"),await e6(e,`${t}-code-verifier`,i);let s=await tr(n),o=n===s?"plain":"s256";return[s,o]}e4.promiseConstructor=Promise;let ti=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,ts=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function to(e){if(!ts.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var ta=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r};let tl=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tu=[502,503,504];async function tc(e){var t;let r,n;if(!e5(e))throw new ez(tl(e),0);if(tu.includes(e.status))throw new ez(tl(e),e.status);try{r=await e.json()}catch(e){throw new eL(tl(e),e)}let i=function(e){let t=e.headers.get(eR);if(!t||!t.match(ti))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(i&&i.getTime()>=eC["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?n=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(n=r.error_code),n){if("weak_password"===n)throw new eW(tl(r),e.status,(null==(t=r.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===n)throw new eN}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new eW(tl(r),e.status,r.weak_password.reasons);throw new e$(tl(r),e.status||500,n)}let th=(e,t,r,n)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(n),Object.assign(Object.assign({},i),r))};async function td(e,t,r,n){var i;let s=Object.assign({},null==n?void 0:n.headers);s[eR]||(s[eR]=eC["2024-01-01"].name),(null==n?void 0:n.jwt)&&(s.Authorization=`Bearer ${n.jwt}`);let o=null!=(i=null==n?void 0:n.query)?i:{};(null==n?void 0:n.redirectTo)&&(o.redirect_to=n.redirectTo);let a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await tf(e,t,r+a,{headers:s,noResolveJson:null==n?void 0:n.noResolveJson},{},null==n?void 0:n.body);return(null==n?void 0:n.xform)?null==n?void 0:n.xform(l):{data:Object.assign({},l),error:null}}async function tf(e,t,r,n,i,s){let o,a=th(t,n,i,s);try{o=await e(r,Object.assign({},a))}catch(e){throw console.error(e),new ez(tl(e),0)}if(o.ok||await tc(o),null==n?void 0:n.noResolveJson)return o;try{return await o.json()}catch(e){await tc(e)}}function tp(e){var t,r,n;let i=null;(n=e).access_token&&n.refresh_token&&n.expires_in&&(i=Object.assign({},e),e.expires_at||(i.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:i,user:null!=(t=e.user)?t:e},error:null}}function tg(e){let t=tp(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function ty(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function tm(e){return{data:e,error:null}}function tw(e){let{action_link:t,email_otp:r,hashed_token:n,redirect_to:i,verification_type:s}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:n,redirect_to:i,verification_type:s},user:Object.assign({},ta(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function tb(e){return e}let tv=["global","local","others"];var t_=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r};class tk{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=e2(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=tv[0]){if(0>tv.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${tv.join(", ")}`);try{return await td(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(eU(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await td(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:ty})}catch(e){if(eU(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=t_(e,["options"]),n=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(n.new_email=null==r?void 0:r.newEmail,delete n.newEmail),await td(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:tw,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(eU(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await td(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:ty})}catch(e){if(eU(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,n,i,s,o,a;try{let l={nextPage:null,lastPage:0,total:0},u=await td(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(r=null==(t=null==e?void 0:e.page)?void 0:t.toString())?r:"",per_page:null!=(i=null==(n=null==e?void 0:e.perPage)?void 0:n.toString())?i:""},xform:tb});if(u.error)throw u.error;let c=await u.json(),h=null!=(s=u.headers.get("x-total-count"))?s:0,d=null!=(a=null==(o=u.headers.get("link"))?void 0:o.split(","))?a:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(e){if(eU(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){to(e);try{return await td(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:ty})}catch(e){if(eU(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){to(e);try{return await td(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:ty})}catch(e){if(eU(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){to(e);try{return await td(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:ty})}catch(e){if(eU(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){to(e.userId);try{let{data:t,error:r}=await td(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(eU(e))return{data:null,error:e};throw e}}async _deleteFactor(e){to(e.userId),to(e.id);try{return{data:await td(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(eU(e))return{data:null,error:e};throw e}}}let tE={getItem:e=>e1()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{e1()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{e1()&&globalThis.localStorage.removeItem(e)}};function tS(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let tT={debug:!!(globalThis&&e1()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class tO extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class tA extends tO{}async function tj(e,t,r){tT.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let n=new globalThis.AbortController;return t>0&&setTimeout(()=>{n.abort(),tT.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},async n=>{if(n){tT.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,n.name);try{return await r()}finally{tT.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,n.name)}}if(0===t)throw tT.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new tA(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(tT.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let tx={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:ex,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function tR(e,t,r){return await r()}class tC{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=tC.nextInstanceID,tC.nextInstanceID+=1,this.instanceID>0&&eZ()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let n=Object.assign(Object.assign({},tx),e);if(this.logDebugMessages=!!n.debug,"function"==typeof n.debug&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new tk({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=e2(n.fetch),this.lock=n.lock||tR,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:eZ()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=tj:this.lock=tR,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:e1()?this.storage=tE:(this.memoryStorage={},this.storage=tS(this.memoryStorage)):(this.memoryStorage={},this.storage=tS(this.memoryStorage)),eZ()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(r=this.broadcastChannel)||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ej}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),eZ()&&this.detectSessionInUrl&&"none"!==r){let{data:n,error:i}=await this._getSessionFromURL(t,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),eU(i)&&"AuthImplicitGrantRedirectError"===i.name){let t=null==(e=i.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:s,redirectType:o}=n;return this._debug("#_initialize()","detected session in URL",s,"redirect type",o),await this._saveSession(s),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",s):await this._notifyAllSubscribers("SIGNED_IN",s)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(eU(e))return{error:e};return{error:new eL("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,n;try{let{data:i,error:s}=await td(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(r=null==(t=null==e?void 0:e.options)?void 0:t.data)?r:{},gotrue_meta_security:{captcha_token:null==(n=null==e?void 0:e.options)?void 0:n.captchaToken}},xform:tp});if(s||!i)return{data:{user:null,session:null},error:s};let o=i.session,a=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:a,session:o},error:null}}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,n;try{let i;if("email"in e){let{email:r,password:n,options:s}=e,o=null,a=null;"pkce"===this.flowType&&([o,a]=await tn(this.storage,this.storageKey)),i=await td(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==s?void 0:s.emailRedirectTo,body:{email:r,password:n,data:null!=(t=null==s?void 0:s.data)?t:{},gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:o,code_challenge_method:a},xform:tp})}else if("phone"in e){let{phone:t,password:s,options:o}=e;i=await td(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:s,data:null!=(r=null==o?void 0:o.data)?r:{},channel:null!=(n=null==o?void 0:o.channel)?n:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:tp})}else throw new eq("You must provide either an email or phone number and a password");let{data:s,error:o}=i;if(o||!s)return{data:{user:null,session:null},error:o};let a=s.session,l=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:n,options:i}=e;t=await td(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:tg})}else if("phone"in e){let{phone:r,password:n,options:i}=e;t=await td(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:tg})}else throw new eq("You must provide either an email or phone number and a password");let{data:r,error:n}=t;if(n)return{data:{user:null,session:null},error:n};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new eD};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:n}}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,n,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(n=e.options)?void 0:n.queryParams,skipBrowserRedirect:null==(i=e.options)?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,n,i,s,o,a,l,u,c,h,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{let h,{chain:d,wallet:g,statement:y,options:m}=e;if(eZ())if("object"==typeof g)h=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))h=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof g||!(null==m?void 0:m.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");h=g}let w=new URL(null!=(t=null==m?void 0:m.url)?t:window.location.href);if("signIn"in h&&h.signIn){let e,t=await h.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==m?void 0:m.signInWithSolana),{version:"1",domain:w.host,uri:w.href}),y?{statement:y}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in h)||"function"!=typeof h.signMessage||!("publicKey"in h)||"object"!=typeof h||!h.publicKey||!("toBase58"in h.publicKey)||"function"!=typeof h.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${w.host} wants you to sign in with your Solana account:`,h.publicKey.toBase58(),...y?["",y,""]:[""],"Version: 1",`URI: ${w.href}`,`Issued At: ${null!=(n=null==(r=null==m?void 0:m.signInWithSolana)?void 0:r.issuedAt)?n:new Date().toISOString()}`,...(null==(i=null==m?void 0:m.signInWithSolana)?void 0:i.notBefore)?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...(null==(s=null==m?void 0:m.signInWithSolana)?void 0:s.expirationTime)?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...(null==(o=null==m?void 0:m.signInWithSolana)?void 0:o.chainId)?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...(null==(a=null==m?void 0:m.signInWithSolana)?void 0:a.nonce)?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...(null==(l=null==m?void 0:m.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...(null==(c=null==(u=null==m?void 0:m.signInWithSolana)?void 0:u.resources)?void 0:c.length)?["Resources",...m.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await h.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:r}=await td(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:function(e){let t=[],r={queue:0,queuedBits:0},n=e=>{t.push(e)};return e.forEach(e=>eX(e,r,n)),eX(null,r,n),t.join("")}(p)},(null==(h=e.options)?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null==(d=e.options)?void 0:d.captchaToken}}:null),xform:tp});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new eD};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await e3(this.storage,`${this.storageKey}-code-verifier`),[r,n]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await td(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:tp});if(await e8(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new eD};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=n?n:null}),error:i}}catch(e){if(eU(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:n,access_token:i,nonce:s}=e,{data:o,error:a}=await td(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:n,access_token:i,nonce:s,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:tp});if(a)return{data:{user:null,session:null},error:a};if(!o||!o.session||!o.user)return{data:{user:null,session:null},error:new eD};return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:o,error:a}}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,n,i,s;try{if("email"in e){let{email:n,options:i}=e,s=null,o=null;"pkce"===this.flowType&&([s,o]=await tn(this.storage,this.storageKey));let{error:a}=await td(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:n,data:null!=(t=null==i?void 0:i.data)?t:{},create_user:null==(r=null==i?void 0:i.shouldCreateUser)||r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:s,code_challenge_method:o},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){let{phone:t,options:r}=e,{data:o,error:a}=await td(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(n=null==r?void 0:r.data)?n:{},create_user:null==(i=null==r?void 0:r.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!=(s=null==r?void 0:r.channel)?s:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new eq("You must provide either an email or phone number.")}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let n,i;"options"in e&&(n=null==(t=e.options)?void 0:t.redirectTo,i=null==(r=e.options)?void 0:r.captchaToken);let{data:s,error:o}=await td(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:n,xform:tp});if(o)throw o;if(!s)throw Error("An error occurred on token verification.");let a=s.session,l=s.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,n;try{let i=null,s=null;return"pkce"===this.flowType&&([i,s]=await tn(this.storage,this.storageKey)),await td(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(r=null==(t=e.options)?void 0:t.redirectTo)?r:void 0}),(null==(n=null==e?void 0:e.options)?void 0:n.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:s}),headers:this.headers,xform:tm})}catch(e){if(eU(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new eN;let{error:n}=await td(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:n}})}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:n,options:i}=e,{error:s}=await td(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){let{phone:r,type:n,options:i}=e,{data:s,error:o}=await td(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==s?void 0:s.message_id},error:o}}throw new eq("You must provide either an email or phone number and a type")}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await e3(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,n)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,n))})}return{data:{session:e},error:null}}let{session:n,error:i}=await this._callRefreshToken(e.refresh_token);if(i)return{data:{session:null},error:i};return{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await td(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:ty});return await this._useSession(async e=>{var t,r,n;let{data:i,error:s}=e;if(s)throw s;return(null==(t=i.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await td(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(n=null==(r=i.session)?void 0:r.access_token)?n:void 0,xform:ty}):{data:{user:null},error:new eN}})}catch(e){if(eU(e))return eU(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await e8(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:n,error:i}=r;if(i)throw i;if(!n.session)throw new eN;let s=n.session,o=null,a=null;"pkce"===this.flowType&&null!=e.email&&([o,a]=await tn(this.storage,this.storageKey));let{data:l,error:u}=await td(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:a}),jwt:s.access_token,xform:ty});if(u)throw u;return s.user=l.user,await this._saveSession(s),await this._notifyAllSubscribers("USER_UPDATED",s),{data:{user:s.user},error:null}})}catch(e){if(eU(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new eN;let t=Date.now()/1e3,r=t,n=!0,i=null,{payload:s}=e9(e.access_token);if(s.exp&&(n=(r=s.exp)<=t),n){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:n,error:s}=await this._getUser(e.access_token);if(s)throw s;i={access_token:e.access_token,refresh_token:e.refresh_token,user:n.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(eU(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:n,error:i}=t;if(i)throw i;e=null!=(r=n.session)?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new eN;let{session:n,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(eU(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!eZ())throw new eM("No browser detected.");if(e.error||e.error_description||e.error_code)throw new eM(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new eF("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new eM("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new eF("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let n=new URL(window.location.href);return n.searchParams.delete("code"),window.history.replaceState(window.history.state,"",n.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:n,access_token:i,refresh_token:s,expires_in:o,expires_at:a,token_type:l}=e;if(!i||!o||!s||!l)throw new eM("No session defined in URL");let u=Math.round(Date.now()/1e3),c=parseInt(o),h=u+c;a&&(h=parseInt(a));let d=h-u;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${c}s`);let f=h-c;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,u);let{data:p,error:g}=await this._getUser(i);if(g)throw g;let y={provider_token:r,provider_refresh_token:n,access_token:i,expires_in:c,expires_at:h,refresh_token:s,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:y,redirectType:e.type},error:null}}catch(e){if(eU(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await e3(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:n,error:i}=t;if(i)return{error:i};let s=null==(r=n.session)?void 0:r.access_token;if(s){let{error:t}=await this.admin.signOut(s,e);if(t&&!(eU(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await e8(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,n;try{let{data:{session:n},error:i}=t;if(i)throw i;await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",n)),this._debug("INITIAL_SESSION","callback id",e,"session",n)}catch(t){await (null==(n=this.stateChangeEmitters.get(e))?void 0:n.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,n=null;"pkce"===this.flowType&&([r,n]=await tn(this.storage,this.storageKey,!0));try{return await td(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:n,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(eU(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(eU(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:n}=await this._useSession(async t=>{var r,n,i,s,o;let{data:a,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(r=e.options)?void 0:r.redirectTo,scopes:null==(n=e.options)?void 0:n.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:!0});return await td(this.fetch,"GET",u,{headers:this.headers,jwt:null!=(o=null==(s=a.session)?void 0:s.access_token)?o:void 0})});if(n)throw n;return!eZ()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(eU(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,n;let{data:i,error:s}=t;if(s)throw s;return await td(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(n=null==(r=i.session)?void 0:r.access_token)?n:void 0})})}catch(e){if(eU(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,n;let i=Date.now();return await (r=async r=>(r>0&&await e7(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await td(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:tp})),n=(e,t)=>{let r=200*Math.pow(2,e);return t&&eJ(t)&&Date.now()+r-i<3e4},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{let t=await r(i);if(!n(i,null,t))return void e(t)}catch(e){if(!n(i,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),eU(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),eZ()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await e3(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let n=(null!=(e=r.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${n?"":" not"} expired with margin of 90000s`),n){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),eJ(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new eN;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let n=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new e4;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new eN;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let n={session:t.session,error:null};return this.refreshingDeferred.resolve(n),n}catch(e){if(this._debug(n,"error",e),eU(e)){let r={session:null,error:e};return eJ(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(r),r}throw null==(r=this.refreshingDeferred)||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(n,"end")}}async _notifyAllSubscribers(e,t,r=!0){let n=`#_notifyAllSubscribers(${e})`;this._debug(n,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let n=[],i=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){n.push(e)}});if(await Promise.all(i),n.length>0){for(let e=0;e<n.length;e+=1)console.error(n[e]);throw n[0]}}finally{this._debug(n,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await e6(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await e8(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&eZ()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let n=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),n<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof tO)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!eZ()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let n=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&n.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&n.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await tn(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});n.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);n.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&n.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${n.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:n,error:i}=t;return i?{data:null,error:i}:await td(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token})})}catch(e){if(eU(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,n;let{data:i,error:s}=t;if(s)return{data:null,error:s};let o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:l}=await td(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(n=null==a?void 0:a.totp)?void 0:n.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(e){if(eU(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:n,error:i}=t;if(i)return{data:null,error:i};let{data:s,error:o}=await td(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+s.expires_in},s)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",s),{data:s,error:o})})}catch(e){if(eU(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:n,error:i}=t;return i?{data:null,error:i}:await td(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token})})}catch(e){if(eU(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],n=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:n,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:n},error:i}=e;if(i)return{data:null,error:i};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:s}=e9(n.access_token),o=null;s.aal&&(o=s.aal);let a=o;return(null!=(r=null==(t=n.user.factors)?void 0:t.filter(e=>"verified"===e.status))?r:[]).length>0&&(a="aal2"),{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:s.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:n,error:i}=await td(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!n.keys||0===n.keys.length)throw new eH("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),!(r=n.keys.find(t=>t.kid===e)))throw new eH("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let n=e;if(!n){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};n=e.session.access_token}let{header:i,payload:s,signature:o,raw:{header:a,payload:l}}=e9(n);var r=s.exp;if(!r)throw Error("Missing exp claim");if(r<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(n);if(e)throw e;return{data:{claims:s,header:i,signature:o},error:null}}let u=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),c=await this.fetchJwk(i.kid,t),h=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,h,o,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){let t=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(n,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${a}.${l}`)))throw new eH("Invalid JWT signature");return{data:{claims:s,header:i,signature:o},error:null}}catch(e){if(eU(e))return{data:null,error:e};throw e}}}tC.nextInstanceID=0;let tP=tC;class tI extends tP{constructor(e){super(e)}}class tU{constructor(e,t,r){var n,i,s;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let o=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",o),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",o),this.storageUrl=new URL("storage/v1",o),this.functionsUrl=new URL("functions/v1",o);let a=`sb-${o.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,n;let{db:i,auth:s,realtime:o,global:a}=e,{db:l,auth:u,realtime:c,global:h}=t,d={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),s),realtime:Object.assign(Object.assign({},c),o),global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},null!=(r=null==h?void 0:h.headers)?r:{}),null!=(n=null==a?void 0:a.headers)?n:{})}),accessToken:()=>{var e,t,r,n;return e=this,t=void 0,n=function*(){return""},new(r=void 0,r=Promise)(function(i,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:e_,realtime:eE,auth:Object.assign(Object.assign({},ek),{storageKey:a}),global:ev});this.storageKey=null!=(n=l.auth.storageKey)?n:"",this.headers=null!=(i=l.global.headers)?i:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(s=l.auth)?s:{},this.headers,l.global.fetch),this.fetch=eA(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new h(new URL("rest/v1",o).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new c(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ew(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,n,i,s;return r=this,n=void 0,i=void 0,s=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!=(t=null==(e=r.session)?void 0:e.access_token)?t:null},new(i||(i=Promise))(function(e,t){function o(e){try{l(s.next(e))}catch(e){t(e)}}function a(e){try{l(s.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof i?r:new i(function(e){e(r)})).then(o,a)}l((s=s.apply(r,n||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:n,storageKey:i,flowType:s,lock:o,debug:a},l,u){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new tI({url:this.authUrl.href,headers:Object.assign(Object.assign({},c),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:n,flowType:s,lock:o,debug:a,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new L(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let t$=(e,t,r)=>new tU(e,t,r);var tL=r(9526);function tB(){return"undefined"!=typeof window&&void 0!==window.document}let tN={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},tD=/^(.*)[.](0|[1-9][0-9]*)$/;function tq(e,t){if(e===t)return!0;let r=e.match(tD);return!!r&&r[1]===t}function tM(e,t,r){let n=r??3180,i=encodeURIComponent(t);if(i.length<=n)return[{name:e,value:t}];let s=[];for(;i.length>0;){let e=i.slice(0,n),t=e.lastIndexOf("%");t>n-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}s.push(r),i=i.slice(e.length)}return s.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function tF(e,t){let r=await t(e);if(r)return r;let n=[];for(let r=0;;r++){let i=`${e}.${r}`,s=await t(i);if(!s)break;n.push(s)}return n.length>0?n.join(""):null}let tz="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),tJ=" 	\n\r=".split(""),tW=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<tJ.length;t+=1)e[tJ[t].charCodeAt(0)]=-2;for(let t=0;t<tz.length;t+=1)e[tz[t].charCodeAt(0)]=t;return e})();function tH(e){let t=[],r=0,n=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){let t=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(n,t)}}(e,e=>{for(r=r<<8|e,n+=8;n>=6;){let e=r>>n-6&63;t.push(tz[e]),n-=6}}),n>0)for(r<<=6-n,n=6;n>=6;){let e=r>>n-6&63;t.push(tz[e]),n-=6}return t.join("")}function tK(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},i=0,s=0;for(let t=0;t<e.length;t+=1){let o=tW[e.charCodeAt(t)];if(o>-1)for(i=i<<6|o,s+=6;s>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(i>>s-8&255,n,r),s-=8;else if(-2===o)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let tG="base64-";async function tV({getAll:e,setAll:t,setItems:r,removedItems:n},i){let s=i.cookieEncoding,o=i.cookieOptions??null,a=await e([...r?Object.keys(r):[],...n?Object.keys(n):[]]),l=a?.map(({name:e})=>e)||[],u=Object.keys(n).flatMap(e=>l.filter(t=>tq(t,e))),c=Object.keys(r).flatMap(e=>{let t=new Set(l.filter(t=>tq(t,e))),n=r[e];"base64url"===s&&(n=tG+tH(n));let i=tM(e,n);return i.forEach(e=>{t.delete(e.name)}),u.push(...t),i}),h={...tN,...o,maxAge:0},d={...tN,...o,maxAge:tN.maxAge};delete h.name,delete d.name,await t([...u.map(e=>({name:e,value:"",options:h})),...c.map(({name:e,value:t})=>({name:e,value:t,options:d}))])}function tX(e,t,r){let n=r?.isSingleton===!0||(!r||!("isSingleton"in r))&&tB();if(n&&i)return i;if(!e||!t)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:s}=function(e,t){let r,n,i=e.cookies??null,s=e.cookieEncoding,o={},a={};if(i)if("get"in i){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let n=await i.get(t[e]);(n||"string"==typeof n)&&r.push({name:t[e],value:n})}return r};if(r=async t=>await e(t),"set"in i&&"remove"in i)n=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:n,options:s}=e[t];n?await i.set(r,n,s):await i.remove(r,s)}};else if(t)n=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in i)if(r=async()=>await i.getAll(),"setAll"in i)n=i.setAll;else if(t)n=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${tB()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!t&&tB()){let e=()=>{let e=(0,tL.qg)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]??""}))};r=()=>e(),n=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,tL.lK)(e,t,r)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],n=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:r,setAll:n,setItems:o,removedItems:a,storage:{isServer:!0,getItem:async e=>{if("string"==typeof o[e])return o[e];if(a[e])return null;let t=await r([e]),n=await tF(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!n)return null;let i=n;return"string"==typeof n&&n.startsWith(tG)&&(i=tK(n.substring(tG.length))),i},setItem:async(t,i)=>{t.endsWith("-code-verifier")&&await tV({getAll:r,setAll:n,setItems:{[t]:i},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:s}),o[t]=i,delete a[t]},removeItem:async e=>{delete o[e],a[e]=!0}}}:{getAll:r,setAll:n,setItems:o,removedItems:a,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),n=await tF(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!n)return null;let i=n;return n.startsWith(tG)&&(i=tK(n.substring(tG.length))),i},setItem:async(t,i)=>{let o=await r([t]),a=new Set((o?.map(({name:e})=>e)||[]).filter(e=>tq(e,t))),l=i;"base64url"===s&&(l=tG+tH(i));let u=tM(t,l);u.forEach(({name:e})=>{a.delete(e)});let c={...tN,...e?.cookieOptions,maxAge:0},h={...tN,...e?.cookieOptions,maxAge:tN.maxAge};delete c.name,delete h.name;let d=[...[...a].map(e=>({name:e,value:"",options:c})),...u.map(({name:e,value:t})=>({name:e,value:t,options:h}))];d.length>0&&await n(d)},removeItem:async t=>{let i=await r([t]),s=(i?.map(({name:e})=>e)||[]).filter(e=>tq(e,t)),o={...tN,...e?.cookieOptions,maxAge:0};delete o.name,s.length>0&&await n(s.map(e=>({name:e,value:"",options:o})))}}}}({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!1),o=t$(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":"supabase-ssr/0.6.1 createBrowserClient"}},auth:{...r?.auth,...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:tB(),detectSessionInUrl:tB(),persistSession:!0,storage:s}});return n&&(i=o),o}},7353:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(9503));class s extends i.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:n}={}){let i="";"plain"===n?i="pl":"phrase"===n?i="ph":"websearch"===n&&(i="w");let s=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${i}fts${s}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let n=r?`${r}.or`:"or";return this.url.searchParams.append(n,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=s},9020:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,s=l(e),o=s[0],a=s[1],u=new i((o+a)*3/4-a),c=0,h=a>0?o-4:o;for(r=0;r<h;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,u[c++]=255&t),1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,s=[],o=0,a=n-i;o<a;o+=16383)s.push(function(e,t,n){for(var i,s=[],o=t;o<n;o+=3)i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),s.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return s.join("")}(e,o,o+16383>a?a:o+16383));return 1===i?s.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&s.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),s.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},9329:(e,t,r)=>{"use strict";let n;r.d(t,{A:()=>tu});var i,s,o,a={};function l(e,t){return function(){return e.apply(t,arguments)}}r.r(a),r.d(a,{hasBrowserEnv:()=>ed,hasStandardBrowserEnv:()=>ep,hasStandardBrowserWebWorkerEnv:()=>eg,navigator:()=>ef,origin:()=>ey});var u=r(4338);let{toString:c}=Object.prototype,{getPrototypeOf:h}=Object,{iterator:d,toStringTag:f}=Symbol,p=(e=>t=>{let r=c.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),g=e=>(e=e.toLowerCase(),t=>p(t)===e),y=e=>t=>typeof t===e,{isArray:m}=Array,w=y("undefined"),b=g("ArrayBuffer"),v=y("string"),_=y("function"),k=y("number"),E=e=>null!==e&&"object"==typeof e,S=e=>{if("object"!==p(e))return!1;let t=h(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(f in e)&&!(d in e)},T=g("Date"),O=g("File"),A=g("Blob"),j=g("FileList"),x=g("URLSearchParams"),[R,C,P,I]=["ReadableStream","Request","Response","Headers"].map(g);function U(e,t,{allOwnKeys:r=!1}={}){let n,i;if(null!=e)if("object"!=typeof e&&(e=[e]),m(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let i,s=r?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length;for(n=0;n<o;n++)i=s[n],t.call(null,e[i],i,e)}}function $(e,t){let r;t=t.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(t===(r=n[i]).toLowerCase())return r;return null}let L="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,B=e=>!w(e)&&e!==L,N=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&h(Uint8Array)),D=g("HTMLFormElement"),q=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),M=g("RegExp"),F=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),n={};U(r,(r,i)=>{let s;!1!==(s=t(r,i,e))&&(n[i]=s||r)}),Object.defineProperties(e,n)},z=g("AsyncFunction"),J=(i="function"==typeof setImmediate,s=_(L.postMessage),i?setImmediate:s?((e,t)=>(L.addEventListener("message",({source:r,data:n})=>{r===L&&n===e&&t.length&&t.shift()()},!1),r=>{t.push(r),L.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),W="undefined"!=typeof queueMicrotask?queueMicrotask.bind(L):void 0!==u&&u.nextTick||J,H={isArray:m,isArrayBuffer:b,isBuffer:function(e){return null!==e&&!w(e)&&null!==e.constructor&&!w(e.constructor)&&_(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||_(e.append)&&("formdata"===(t=p(e))||"object"===t&&_(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&b(e.buffer)},isString:v,isNumber:k,isBoolean:e=>!0===e||!1===e,isObject:E,isPlainObject:S,isReadableStream:R,isRequest:C,isResponse:P,isHeaders:I,isUndefined:w,isDate:T,isFile:O,isBlob:A,isRegExp:M,isFunction:_,isStream:e=>E(e)&&_(e.pipe),isURLSearchParams:x,isTypedArray:N,isFileList:j,forEach:U,merge:function e(){let{caseless:t}=B(this)&&this||{},r={},n=(n,i)=>{let s=t&&$(r,i)||i;S(r[s])&&S(n)?r[s]=e(r[s],n):S(n)?r[s]=e({},n):m(n)?r[s]=n.slice():r[s]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&U(arguments[e],n);return r},extend:(e,t,r,{allOwnKeys:n}={})=>(U(t,(t,n)=>{r&&_(t)?e[n]=l(t,r):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,n)=>{let i,s,o,a={};if(t=t||{},null==e)return t;do{for(s=(i=Object.getOwnPropertyNames(e)).length;s-- >0;)o=i[s],(!n||n(o,e,t))&&!a[o]&&(t[o]=e[o],a[o]=!0);e=!1!==r&&h(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:p,kindOfTest:g,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;let n=e.indexOf(t,r);return -1!==n&&n===r},toArray:e=>{if(!e)return null;if(m(e))return e;let t=e.length;if(!k(t))return null;let r=Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{let r,n=(e&&e[d]).call(e);for(;(r=n.next())&&!r.done;){let n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let r,n=[];for(;null!==(r=e.exec(t));)n.push(r);return n},isHTMLForm:D,hasOwnProperty:q,hasOwnProp:q,reduceDescriptors:F,freezeMethods:e=>{F(e,(t,r)=>{if(_(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(_(e[r])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(e,t)=>{let r={};return(m(e)?e:String(e).split(t)).forEach(e=>{r[e]=!0}),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,r){return t.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e*=1)?e:t,findKey:$,global:L,isContextDefined:B,isSpecCompliantForm:function(e){return!!(e&&_(e.append)&&"FormData"===e[f]&&e[d])},toJSONObject:e=>{let t=Array(10),r=(e,n)=>{if(E(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let i=m(e)?[]:{};return U(e,(e,t)=>{let s=r(e,n+1);w(s)||(i[t]=s)}),t[n]=void 0,i}}return e};return r(e,0)},isAsyncFn:z,isThenable:e=>e&&(E(e)||_(e))&&_(e.then)&&_(e.catch),setImmediate:J,asap:W,isIterable:e=>null!=e&&_(e[d])};function K(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}H.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:H.toJSONObject(this.config),code:this.code,status:this.status}}});let G=K.prototype,V={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{V[e]={value:e}}),Object.defineProperties(K,V),Object.defineProperty(G,"isAxiosError",{value:!0}),K.from=(e,t,r,n,i,s)=>{let o=Object.create(G);return H.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),K.call(o,e.message,t,r,n,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};var X=r(887).hp;function Y(e){return H.isPlainObject(e)||H.isArray(e)}function Q(e){return H.endsWith(e,"[]")?e.slice(0,-2):e}function Z(e,t,r){return e?e.concat(t).map(function(e,t){return e=Q(e),!r&&t?"["+e+"]":e}).join(r?".":""):t}let ee=H.toFlatObject(H,{},null,function(e){return/^is[A-Z]/.test(e)}),et=function(e,t,r){if(!H.isObject(e))throw TypeError("target must be an object");t=t||new FormData;let n=(r=H.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!H.isUndefined(t[e])})).metaTokens,i=r.visitor||u,s=r.dots,o=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&H.isSpecCompliantForm(t);if(!H.isFunction(i))throw TypeError("visitor must be a function");function l(e){if(null===e)return"";if(H.isDate(e))return e.toISOString();if(!a&&H.isBlob(e))throw new K("Blob is not supported. Use a Buffer instead.");return H.isArrayBuffer(e)||H.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):X.from(e):e}function u(e,r,i){let a=e;if(e&&!i&&"object"==typeof e)if(H.endsWith(r,"{}"))r=n?r:r.slice(0,-2),e=JSON.stringify(e);else{var u;if(H.isArray(e)&&(u=e,H.isArray(u)&&!u.some(Y))||(H.isFileList(e)||H.endsWith(r,"[]"))&&(a=H.toArray(e)))return r=Q(r),a.forEach(function(e,n){H.isUndefined(e)||null===e||t.append(!0===o?Z([r],n,s):null===o?r:r+"[]",l(e))}),!1}return!!Y(e)||(t.append(Z(i,r,s),l(e)),!1)}let c=[],h=Object.assign(ee,{defaultVisitor:u,convertValue:l,isVisitable:Y});if(!H.isObject(e))throw TypeError("data must be an object");return!function e(r,n){if(!H.isUndefined(r)){if(-1!==c.indexOf(r))throw Error("Circular reference detected in "+n.join("."));c.push(r),H.forEach(r,function(r,s){!0===(!(H.isUndefined(r)||null===r)&&i.call(t,r,H.isString(s)?s.trim():s,n,h))&&e(r,n?n.concat(s):[s])}),c.pop()}}(e),t};function er(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function en(e,t){this._pairs=[],e&&et(e,this,t)}let ei=en.prototype;function es(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eo(e,t,r){let n;if(!t)return e;let i=r&&r.encode||es;H.isFunction(r)&&(r={serialize:r});let s=r&&r.serialize;if(n=s?s(t,r):H.isURLSearchParams(t)?t.toString():new en(t,r).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}ei.append=function(e,t){this._pairs.push([e,t])},ei.toString=function(e){let t=e?function(t){return e.call(this,t,er)}:er;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class ea{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){H.forEach(this.handlers,function(t){null!==t&&e(t)})}}let el={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},eu="undefined"!=typeof URLSearchParams?URLSearchParams:en,ec="undefined"!=typeof FormData?FormData:null,eh="undefined"!=typeof Blob?Blob:null,ed="undefined"!=typeof window&&"undefined"!=typeof document,ef="object"==typeof navigator&&navigator||void 0,ep=ed&&(!ef||0>["ReactNative","NativeScript","NS"].indexOf(ef.product)),eg="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ey=ed&&window.location.href||"http://localhost",em={...a,isBrowser:!0,classes:{URLSearchParams:eu,FormData:ec,Blob:eh},protocols:["http","https","file","blob","url","data"]},ew=function(e){if(H.isFormData(e)&&H.isFunction(e.entries)){let t={};return H.forEachEntry(e,(e,r)=>{!function e(t,r,n,i){let s=t[i++];if("__proto__"===s)return!0;let o=Number.isFinite(+s),a=i>=t.length;return(s=!s&&H.isArray(n)?n.length:s,a)?H.hasOwnProp(n,s)?n[s]=[n[s],r]:n[s]=r:(n[s]&&H.isObject(n[s])||(n[s]=[]),e(t,r,n[s],i)&&H.isArray(n[s])&&(n[s]=function(e){let t,r,n={},i=Object.keys(e),s=i.length;for(t=0;t<s;t++)n[r=i[t]]=e[r];return n}(n[s]))),!o}(H.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),r,t,0)}),t}return null},eb={transitional:el,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let r,n=t.getContentType()||"",i=n.indexOf("application/json")>-1,s=H.isObject(e);if(s&&H.isHTMLForm(e)&&(e=new FormData(e)),H.isFormData(e))return i?JSON.stringify(ew(e)):e;if(H.isArrayBuffer(e)||H.isBuffer(e)||H.isStream(e)||H.isFile(e)||H.isBlob(e)||H.isReadableStream(e))return e;if(H.isArrayBufferView(e))return e.buffer;if(H.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1){var o,a;return(o=e,a=this.formSerializer,et(o,new em.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,n){return em.isNode&&H.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},a))).toString()}if((r=H.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return et(r?{"files[]":e}:e,t&&new t,this.formSerializer)}}if(s||i){t.setContentType("application/json",!1);var l=e;if(H.isString(l))try{return(0,JSON.parse)(l),H.trim(l)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(l)}return e}],transformResponse:[function(e){let t=this.transitional||eb.transitional,r=t&&t.forcedJSONParsing,n="json"===this.responseType;if(H.isResponse(e)||H.isReadableStream(e))return e;if(e&&H.isString(e)&&(r&&!this.responseType||n)){let r=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!r&&n){if("SyntaxError"===e.name)throw K.from(e,K.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:em.classes.FormData,Blob:em.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};H.forEach(["delete","get","head","post","put","patch"],e=>{eb.headers[e]={}});let ev=H.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),e_=e=>{let t,r,n,i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),r=e.substring(n+1).trim(),!t||i[t]&&ev[t]||("set-cookie"===t?i[t]?i[t].push(r):i[t]=[r]:i[t]=i[t]?i[t]+", "+r:r)}),i},ek=Symbol("internals");function eE(e){return e&&String(e).trim().toLowerCase()}function eS(e){return!1===e||null==e?e:H.isArray(e)?e.map(eS):String(e)}let eT=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eO(e,t,r,n,i){if(H.isFunction(n))return n.call(this,t,r);if(i&&(t=r),H.isString(t)){if(H.isString(n))return -1!==t.indexOf(n);if(H.isRegExp(n))return n.test(t)}}class eA{constructor(e){e&&this.set(e)}set(e,t,r){let n=this;function i(e,t,r){let i=eE(t);if(!i)throw Error("header name must be a non-empty string");let s=H.findKey(n,i);s&&void 0!==n[s]&&!0!==r&&(void 0!==r||!1===n[s])||(n[s||t]=eS(e))}let s=(e,t)=>H.forEach(e,(e,r)=>i(e,r,t));if(H.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(H.isString(e)&&(e=e.trim())&&!eT(e))s(e_(e),t);else if(H.isObject(e)&&H.isIterable(e)){let r={},n,i;for(let t of e){if(!H.isArray(t))throw TypeError("Object iterator must return a key-value pair");r[i=t[0]]=(n=r[i])?H.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(r,t)}else null!=e&&i(t,e,r);return this}get(e,t){if(e=eE(e)){let r=H.findKey(this,e);if(r){let e=this[r];if(!t)return e;if(!0===t){let t,r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)r[t[1]]=t[2];return r}if(H.isFunction(t))return t.call(this,e,r);if(H.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=eE(e)){let r=H.findKey(this,e);return!!(r&&void 0!==this[r]&&(!t||eO(this,this[r],r,t)))}return!1}delete(e,t){let r=this,n=!1;function i(e){if(e=eE(e)){let i=H.findKey(r,e);i&&(!t||eO(r,r[i],i,t))&&(delete r[i],n=!0)}}return H.isArray(e)?e.forEach(i):i(e),n}clear(e){let t=Object.keys(this),r=t.length,n=!1;for(;r--;){let i=t[r];(!e||eO(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let t=this,r={};return H.forEach(this,(n,i)=>{let s=H.findKey(r,i);if(s){t[s]=eS(n),delete t[i];return}let o=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r):String(i).trim();o!==i&&delete t[i],t[o]=eS(n),r[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return H.forEach(this,(r,n)=>{null!=r&&!1!==r&&(t[n]=e&&H.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(e=>r.set(e)),r}static accessor(e){let t=(this[ek]=this[ek]={accessors:{}}).accessors,r=this.prototype;function n(e){let n=eE(e);if(!t[n]){let i=H.toCamelCase(" "+e);["get","set","has"].forEach(t=>{Object.defineProperty(r,t+i,{value:function(r,n,i){return this[t].call(this,e,r,n,i)},configurable:!0})}),t[n]=!0}}return H.isArray(e)?e.forEach(n):n(e),this}}function ej(e,t){let r=this||eb,n=t||r,i=eA.from(n.headers),s=n.data;return H.forEach(e,function(e){s=e.call(r,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function ex(e){return!!(e&&e.__CANCEL__)}function eR(e,t,r){K.call(this,null==e?"canceled":e,K.ERR_CANCELED,t,r),this.name="CanceledError"}function eC(e,t,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new K("Request failed with status code "+r.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}eA.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),H.reduceDescriptors(eA.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}}),H.freezeMethods(eA),H.inherits(eR,K,{__CANCEL__:!0});let eP=function(e,t){let r,n=Array(e=e||10),i=Array(e),s=0,o=0;return t=void 0!==t?t:1e3,function(a){let l=Date.now(),u=i[o];r||(r=l),n[s]=a,i[s]=l;let c=o,h=0;for(;c!==s;)h+=n[c++],c%=e;if((s=(s+1)%e)===o&&(o=(o+1)%e),l-r<t)return;let d=u&&l-u;return d?Math.round(1e3*h/d):void 0}},eI=function(e,t){let r,n,i=0,s=1e3/t,o=(t,s=Date.now())=>{i=s,r=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),a=t-i;a>=s?o(e,t):(r=e,n||(n=setTimeout(()=>{n=null,o(r)},s-a)))},()=>r&&o(r)]},eU=(e,t,r=3)=>{let n=0,i=eP(50,250);return eI(r=>{let s=r.loaded,o=r.lengthComputable?r.total:void 0,a=s-n,l=i(a);n=s,e({loaded:s,total:o,progress:o?s/o:void 0,bytes:a,rate:l||void 0,estimated:l&&o&&s<=o?(o-s)/l:void 0,event:r,lengthComputable:null!=o,[t?"download":"upload"]:!0})},r)},e$=(e,t)=>{let r=null!=e;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},eL=e=>(...t)=>H.asap(()=>e(...t)),eB=em.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,em.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(em.origin),em.navigator&&/(msie|trident)/i.test(em.navigator.userAgent)):()=>!0,eN=em.hasStandardBrowserEnv?{write(e,t,r,n,i,s){let o=[e+"="+encodeURIComponent(t)];H.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),H.isString(n)&&o.push("path="+n),H.isString(i)&&o.push("domain="+i),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function eD(e,t,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(n||!1==r)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}let eq=e=>e instanceof eA?{...e}:e;function eM(e,t){t=t||{};let r={};function n(e,t,r,n){return H.isPlainObject(e)&&H.isPlainObject(t)?H.merge.call({caseless:n},e,t):H.isPlainObject(t)?H.merge({},t):H.isArray(t)?t.slice():t}function i(e,t,r,i){return H.isUndefined(t)?H.isUndefined(e)?void 0:n(void 0,e,r,i):n(e,t,r,i)}function s(e,t){if(!H.isUndefined(t))return n(void 0,t)}function o(e,t){return H.isUndefined(t)?H.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function a(r,i,s){return s in t?n(r,i):s in e?n(void 0,r):void 0}let l={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(e,t,r)=>i(eq(e),eq(t),r,!0)};return H.forEach(Object.keys(Object.assign({},e,t)),function(n){let s=l[n]||i,o=s(e[n],t[n],n);H.isUndefined(o)&&s!==a||(r[n]=o)}),r}let eF=e=>{let t,r=eM({},e),{data:n,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:l}=r;if(r.headers=a=eA.from(a),r.url=eo(eD(r.baseURL,r.url,r.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),H.isFormData(n)){if(em.hasStandardBrowserEnv||em.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(t=a.getContentType())){let[e,...r]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...r].join("; "))}}if(em.hasStandardBrowserEnv&&(i&&H.isFunction(i)&&(i=i(r)),i||!1!==i&&eB(r.url))){let e=s&&o&&eN.read(o);e&&a.set(s,e)}return r},ez="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,r){let n,i,s,o,a,l=eF(e),u=l.data,c=eA.from(l.headers).normalize(),{responseType:h,onUploadProgress:d,onDownloadProgress:f}=l;function p(){o&&o(),a&&a(),l.cancelToken&&l.cancelToken.unsubscribe(n),l.signal&&l.signal.removeEventListener("abort",n)}let g=new XMLHttpRequest;function y(){if(!g)return;let n=eA.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());eC(function(e){t(e),p()},function(e){r(e),p()},{data:h&&"text"!==h&&"json"!==h?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:n,config:e,request:g}),g=null}g.open(l.method.toUpperCase(),l.url,!0),g.timeout=l.timeout,"onloadend"in g?g.onloadend=y:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(y)},g.onabort=function(){g&&(r(new K("Request aborted",K.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new K("Network Error",K.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let t=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded",n=l.transitional||el;l.timeoutErrorMessage&&(t=l.timeoutErrorMessage),r(new K(t,n.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,g)),g=null},void 0===u&&c.setContentType(null),"setRequestHeader"in g&&H.forEach(c.toJSON(),function(e,t){g.setRequestHeader(t,e)}),H.isUndefined(l.withCredentials)||(g.withCredentials=!!l.withCredentials),h&&"json"!==h&&(g.responseType=l.responseType),f&&([s,a]=eU(f,!0),g.addEventListener("progress",s)),d&&g.upload&&([i,o]=eU(d),g.upload.addEventListener("progress",i),g.upload.addEventListener("loadend",o)),(l.cancelToken||l.signal)&&(n=t=>{g&&(r(!t||t.type?new eR(null,e,g):t),g.abort(),g=null)},l.cancelToken&&l.cancelToken.subscribe(n),l.signal&&(l.signal.aborted?n():l.signal.addEventListener("abort",n)));let m=function(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(l.url);if(m&&-1===em.protocols.indexOf(m))return void r(new K("Unsupported protocol "+m+":",K.ERR_BAD_REQUEST,e));g.send(u||null)})},eJ=(e,t)=>{let{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,n=new AbortController,i=function(e){if(!r){r=!0,o();let t=e instanceof Error?e:this.reason;n.abort(t instanceof K?t:new eR(t instanceof Error?t.message:t))}},s=t&&setTimeout(()=>{s=null,i(new K(`timeout ${t} of ms exceeded`,K.ETIMEDOUT))},t),o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:a}=n;return a.unsubscribe=()=>H.asap(o),a}},eW=function*(e,t){let r,n=e.byteLength;if(!t||n<t)return void(yield e);let i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},eH=async function*(e,t){for await(let r of eK(e))yield*eW(r,t)},eK=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},eG=(e,t,r,n)=>{let i,s=eH(e,t),o=0,a=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:t,value:n}=await s.next();if(t){a(),e.close();return}let i=n.byteLength;if(r){let e=o+=i;r(e)}e.enqueue(new Uint8Array(n))}catch(e){throw a(e),e}},cancel:e=>(a(e),s.return())},{highWaterMark:2})},eV="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,eX=eV&&"function"==typeof ReadableStream,eY=eV&&("function"==typeof TextEncoder?(n=new TextEncoder,e=>n.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),eQ=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},eZ=eX&&eQ(()=>{let e=!1,t=new Request(em.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),e0=eX&&eQ(()=>H.isReadableStream(new Response("").body)),e1={stream:e0&&(e=>e.body)};eV&&(o=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{e1[e]||(e1[e]=H.isFunction(o[e])?t=>t[e]():(t,r)=>{throw new K(`Response type '${e}' is not supported`,K.ERR_NOT_SUPPORT,r)})}));let e2=async e=>{if(null==e)return 0;if(H.isBlob(e))return e.size;if(H.isSpecCompliantForm(e)){let t=new Request(em.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return H.isArrayBufferView(e)||H.isArrayBuffer(e)?e.byteLength:(H.isURLSearchParams(e)&&(e+=""),H.isString(e))?(await eY(e)).byteLength:void 0},e5=async(e,t)=>{let r=H.toFiniteNumber(e.getContentLength());return null==r?e2(t):r},e6={http:null,xhr:ez,fetch:eV&&(async e=>{let t,r,{url:n,method:i,data:s,signal:o,cancelToken:a,timeout:l,onDownloadProgress:u,onUploadProgress:c,responseType:h,headers:d,withCredentials:f="same-origin",fetchOptions:p}=eF(e);h=h?(h+"").toLowerCase():"text";let g=eJ([o,a&&a.toAbortSignal()],l),y=g&&g.unsubscribe&&(()=>{g.unsubscribe()});try{if(c&&eZ&&"get"!==i&&"head"!==i&&0!==(r=await e5(d,s))){let e,t=new Request(n,{method:"POST",body:s,duplex:"half"});if(H.isFormData(s)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,n]=e$(r,eU(eL(c)));s=eG(t.body,65536,e,n)}}H.isString(f)||(f=f?"include":"omit");let o="credentials"in Request.prototype;t=new Request(n,{...p,signal:g,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:s,duplex:"half",credentials:o?f:void 0});let a=await fetch(t),l=e0&&("stream"===h||"response"===h);if(e0&&(u||l&&y)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});let t=H.toFiniteNumber(a.headers.get("content-length")),[r,n]=u&&e$(t,eU(eL(u),!0))||[];a=new Response(eG(a.body,65536,r,()=>{n&&n(),y&&y()}),e)}h=h||"text";let m=await e1[H.findKey(e1,h)||"text"](a,e);return!l&&y&&y(),await new Promise((r,n)=>{eC(r,n,{data:m,headers:eA.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:t})})}catch(r){if(y&&y(),r&&"TypeError"===r.name&&/Load failed|fetch/i.test(r.message))throw Object.assign(new K("Network Error",K.ERR_NETWORK,e,t),{cause:r.cause||r});throw K.from(r,r&&r.code,e,t)}})};H.forEach(e6,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let e3=e=>`- ${e}`,e8=e=>H.isFunction(e)||null===e||!1===e,e4={getAdapter:e=>{let t,r,{length:n}=e=H.isArray(e)?e:[e],i={};for(let s=0;s<n;s++){let n;if(r=t=e[s],!e8(t)&&void 0===(r=e6[(n=String(t)).toLowerCase()]))throw new K(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+s]=r}if(!r){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new K("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(e3).join("\n"):" "+e3(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r}};function e9(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eR(null,e)}function e7(e){return e9(e),e.headers=eA.from(e.headers),e.data=ej.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),e4.getAdapter(e.adapter||eb.adapter)(e).then(function(t){return e9(e),t.data=ej.call(e,e.transformResponse,t),t.headers=eA.from(t.headers),t},function(t){return!ex(t)&&(e9(e),t&&t.response&&(t.response.data=ej.call(e,e.transformResponse,t.response),t.response.headers=eA.from(t.response.headers))),Promise.reject(t)})}let te="1.9.0",tt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tt[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});let tr={};tt.transitional=function(e,t,r){function n(e,t){return"[Axios v"+te+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,i,s)=>{if(!1===e)throw new K(n(i," has been removed"+(t?" in "+t:"")),K.ERR_DEPRECATED);return t&&!tr[i]&&(tr[i]=!0,console.warn(n(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,i,s)}},tt.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};let tn={assertOptions:function(e,t,r){if("object"!=typeof e)throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let s=n[i],o=t[s];if(o){let t=e[s],r=void 0===t||o(t,s,e);if(!0!==r)throw new K("option "+s+" must be "+r,K.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new K("Unknown option "+s,K.ERR_BAD_OPTION)}},validators:tt},ti=tn.validators;class ts{constructor(e){this.defaults=e||{},this.interceptors={request:new ea,response:new ea}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let r=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}catch(e){}}throw e}}_request(e,t){let r,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:s,headers:o}=t=eM(this.defaults,t);void 0!==i&&tn.assertOptions(i,{silentJSONParsing:ti.transitional(ti.boolean),forcedJSONParsing:ti.transitional(ti.boolean),clarifyTimeoutError:ti.transitional(ti.boolean)},!1),null!=s&&(H.isFunction(s)?t.paramsSerializer={serialize:s}:tn.assertOptions(s,{encode:ti.function,serialize:ti.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tn.assertOptions(t,{baseUrl:ti.spelling("baseURL"),withXsrfToken:ti.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=o&&H.merge(o.common,o[t.method]);o&&H.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=eA.concat(a,o);let l=[],u=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(u=u&&e.synchronous,l.unshift(e.fulfilled,e.rejected))});let c=[];this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let h=0;if(!u){let e=[e7.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,c),n=e.length,r=Promise.resolve(t);h<n;)r=r.then(e[h++],e[h++]);return r}n=l.length;let d=t;for(h=0;h<n;){let e=l[h++],t=l[h++];try{d=e(d)}catch(e){t.call(this,e);break}}try{r=e7.call(this,d)}catch(e){return Promise.reject(e)}for(h=0,n=c.length;h<n;)r=r.then(c[h++],c[h++]);return r}getUri(e){return eo(eD((e=eM(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}H.forEach(["delete","get","head","options"],function(e){ts.prototype[e]=function(t,r){return this.request(eM(r||{},{method:e,url:t,data:(r||{}).data}))}}),H.forEach(["post","put","patch"],function(e){function t(t){return function(r,n,i){return this.request(eM(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}ts.prototype[e]=t(),ts.prototype[e+"Form"]=t(!0)});class to{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let r=this;this.promise.then(e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null}),this.promise.then=e=>{let t,n=new Promise(e=>{r.subscribe(e),t=e}).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e(function(e,n,i){r.reason||(r.reason=new eR(e,n,i),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason)return void e(this.reason);this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new to(function(t){e=t}),cancel:e}}}let ta={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ta).forEach(([e,t])=>{ta[t]=e});let tl=function e(t){let r=new ts(t),n=l(ts.prototype.request,r);return H.extend(n,ts.prototype,r,{allOwnKeys:!0}),H.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return e(eM(t,r))},n}(eb);tl.Axios=ts,tl.CanceledError=eR,tl.CancelToken=to,tl.isCancel=ex,tl.VERSION=te,tl.toFormData=et,tl.AxiosError=K,tl.Cancel=tl.CanceledError,tl.all=function(e){return Promise.all(e)},tl.spread=function(e){return function(t){return e.apply(null,t)}},tl.isAxiosError=function(e){return H.isObject(e)&&!0===e.isAxiosError},tl.mergeConfig=eM,tl.AxiosHeaders=eA,tl.formToJSON=e=>ew(H.isHTMLForm(e)?new FormData(e):e),tl.getAdapter=e4.getAdapter,tl.HttpStatusCode=ta,tl.default=tl;let tu=tl},9503:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(5175));class s extends i.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:n,referencedTable:i=n}={}){let s=i?`${i}.order`:"order",o=this.url.searchParams.get(s);return this.url.searchParams.set(s,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(n,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:n=r}={}){let i=void 0===n?"offset":`${n}.offset`,s=void 0===n?"limit":`${n}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(s,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:n=!1,wal:i=!1,format:s="text"}={}){var o;let a=[e?"analyze":null,t?"verbose":null,r?"settings":null,n?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!=(o=this.headers.Accept)?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${s}; for="${l}"; options=${a};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=s},9526:(e,t)=>{"use strict";t.qg=function(e,t){let r=new a,n=e.length;if(n<2)return r;let i=t?.decode||c,s=0;do{let t=e.indexOf("=",s);if(-1===t)break;let o=e.indexOf(";",s),a=-1===o?n:o;if(t>a){s=e.lastIndexOf(";",t-1)+1;continue}let c=l(e,s,t),h=u(e,t,c),d=e.slice(c,h);if(void 0===r[d]){let n=l(e,t+1,a),s=u(e,a,n),o=i(e.slice(n,s));r[d]=o}s=a+1}while(s<n);return r},t.lK=function(e,t,a){let l=a?.encode||encodeURIComponent;if(!r.test(e))throw TypeError(`argument name is invalid: ${e}`);let u=l(t);if(!n.test(u))throw TypeError(`argument val is invalid: ${t}`);let c=e+"="+u;if(!a)return c;if(void 0!==a.maxAge){if(!Number.isInteger(a.maxAge))throw TypeError(`option maxAge is invalid: ${a.maxAge}`);c+="; Max-Age="+a.maxAge}if(a.domain){if(!i.test(a.domain))throw TypeError(`option domain is invalid: ${a.domain}`);c+="; Domain="+a.domain}if(a.path){if(!s.test(a.path))throw TypeError(`option path is invalid: ${a.path}`);c+="; Path="+a.path}if(a.expires){var h;if(h=a.expires,"[object Date]"!==o.call(h)||!Number.isFinite(a.expires.valueOf()))throw TypeError(`option expires is invalid: ${a.expires}`);c+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.partitioned&&(c+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${a.priority}`)}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${a.sameSite}`)}return c};let r=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,n=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,a=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function u(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},9647:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}}}]);