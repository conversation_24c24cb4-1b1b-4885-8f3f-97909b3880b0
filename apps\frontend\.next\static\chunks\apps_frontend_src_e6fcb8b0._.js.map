{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency: string = 'DZD'): string {\n  return new Intl.NumberFormat('ar-DZ', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-DZ', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-DZ', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date));\n}\n\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700',\n      secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n      outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n      ghost: 'hover:bg-gray-100',\n      danger: 'bg-red-600 text-white hover:bg-red-700',\n    };\n\n    const sizes = {\n      sm: 'h-9 px-3 text-sm',\n      md: 'h-10 px-4 py-2',\n      lg: 'h-11 px-8',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,0IAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/Button';\nimport {\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { useState } from 'react';\n\nexport function Header() {\n  const { user, signOut } = useAuth();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">ش</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">شاريو</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link href=\"/features\" className=\"text-gray-600 hover:text-gray-900\">\n              المميزات\n            </Link>\n            <Link href=\"/pricing\" className=\"text-gray-600 hover:text-gray-900\">\n              الأسعار\n            </Link>\n            <Link href=\"/templates\" className=\"text-gray-600 hover:text-gray-900\">\n              القوالب\n            </Link>\n            <Link href=\"/help\" className=\"text-gray-600 hover:text-gray-900\">\n              المساعدة\n            </Link>\n          </nav>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/dashboard\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    <UserIcon className=\"w-4 h-4 ml-2\" />\n                    لوحة التحكم\n                  </Button>\n                </Link>\n                <Button variant=\"outline\" size=\"sm\" onClick={signOut}>\n                  تسجيل الخروج\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link href=\"/auth/login\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    تسجيل الدخول\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button size=\"sm\">\n                    إنشاء حساب\n                  </Button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <button\n              className=\"md:hidden p-2\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"w-6 h-6\" />\n              ) : (\n                <Bars3Icon className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link \n                href=\"/features\" \n                className=\"text-gray-600 hover:text-gray-900 py-2\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                المميزات\n              </Link>\n              <Link \n                href=\"/pricing\" \n                className=\"text-gray-600 hover:text-gray-900 py-2\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                الأسعار\n              </Link>\n              <Link \n                href=\"/templates\" \n                className=\"text-gray-600 hover:text-gray-900 py-2\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                القوالب\n              </Link>\n              <Link \n                href=\"/help\" \n                className=\"text-gray-600 hover:text-gray-900 py-2\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                المساعدة\n              </Link>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAKA;;;AAVA;;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAoC;;;;;;8CAGrE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAoC;;;;;;8CAGpE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAoC;;;;;;8CAGtE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAoC;;;;;;;;;;;;sCAMnE,6LAAC;4BAAI,WAAU;;gCACZ,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,yJAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;;kEAC3B,6LAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIzC,6LAAC,yJAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;sDAAS;;;;;;;;;;;yDAKxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,yJAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAAK;;;;;;;;;;;sDAIpC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,yJAAA,CAAA,SAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;8CAQxB,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAEjC,+BACC,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAnHgB;;QACY,sJAAA,CAAA,UAAO;;;KADnB", "debugId": null}}]}