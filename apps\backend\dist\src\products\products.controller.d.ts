import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    create(req: any, createProductDto: CreateProductDto): Promise<any>;
    findAll(productPageId?: string): Promise<any>;
    findByProductPage(productPageId: string, isActive?: string): Promise<any>;
    search(productPageId: string, query: string): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, req: any, updateProductDto: UpdateProductDto): Promise<any>;
    remove(id: string, req: any): Promise<any>;
}
