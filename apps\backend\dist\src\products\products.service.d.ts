import { PrismaService } from '../common/prisma/prisma.service';
import { ProductPagesService } from '../product-pages/product-pages.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
export declare class ProductsService {
    private prisma;
    private productPagesService;
    constructor(prisma: PrismaService, productPagesService: ProductPagesService);
    create(userId: string, createProductDto: CreateProductDto): Promise<any>;
    findAll(productPageId?: string): Promise<any>;
    findByProductPage(productPageId: string, isActive?: boolean): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, userId: string, updateProductDto: UpdateProductDto): Promise<any>;
    remove(id: string, userId: string): Promise<any>;
    search(productPageId: string, query: string): Promise<any>;
}
