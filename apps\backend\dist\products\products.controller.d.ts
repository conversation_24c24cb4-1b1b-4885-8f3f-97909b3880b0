import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Request as ExpressRequest } from 'express';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    create(req: ExpressRequest, createProductDto: CreateProductDto): Promise<{
        productPage: {
            id: string;
            name: string;
            slug: string;
        };
        images: {
            id: string;
            createdAt: Date;
            position: number;
            productId: string;
            url: string;
            altText: string | null;
        }[];
        variants: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            isActive: boolean;
            price: import("@prisma/client/runtime/library").Decimal;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            stock: number;
            productId: string;
        }[];
        productCategories: ({
            category: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                slug: string;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                imageUrl: string | null;
                position: number;
                parentId: string | null;
            };
        } & {
            id: string;
            productId: string;
            categoryId: string;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    }>;
    findAll(productPageId?: string): Promise<({
        productPage: {
            id: string;
            name: string;
            slug: string;
        };
        _count: {
            reviews: number;
            orderItems: number;
        };
        images: {
            id: string;
            createdAt: Date;
            position: number;
            productId: string;
            url: string;
            altText: string | null;
        }[];
        variants: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            isActive: boolean;
            price: import("@prisma/client/runtime/library").Decimal;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            stock: number;
            productId: string;
        }[];
        productCategories: ({
            category: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                slug: string;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                imageUrl: string | null;
                position: number;
                parentId: string | null;
            };
        } & {
            id: string;
            productId: string;
            categoryId: string;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    })[]>;
    findByProductPage(productPageId: string, isActive?: string): Promise<({
        reviews: ({
            user: {
                id: string;
                firstName: string | null;
                lastName: string | null;
            };
        } & {
            title: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            content: string | null;
            productId: string;
            rating: number;
            isVerified: boolean;
        })[];
        _count: {
            reviews: number;
            orderItems: number;
        };
        images: {
            id: string;
            createdAt: Date;
            position: number;
            productId: string;
            url: string;
            altText: string | null;
        }[];
        variants: ({
            options: {
                id: string;
                name: string;
                variantId: string;
                value: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            isActive: boolean;
            price: import("@prisma/client/runtime/library").Decimal;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            stock: number;
            productId: string;
        })[];
        productCategories: ({
            category: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                slug: string;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                imageUrl: string | null;
                position: number;
                parentId: string | null;
            };
        } & {
            id: string;
            productId: string;
            categoryId: string;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    })[]>;
    search(productPageId: string, query: string): Promise<({
        _count: {
            reviews: number;
        };
        images: {
            id: string;
            createdAt: Date;
            position: number;
            productId: string;
            url: string;
            altText: string | null;
        }[];
        variants: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            isActive: boolean;
            price: import("@prisma/client/runtime/library").Decimal;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            stock: number;
            productId: string;
        }[];
        productCategories: ({
            category: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                slug: string;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                imageUrl: string | null;
                position: number;
                parentId: string | null;
            };
        } & {
            id: string;
            productId: string;
            categoryId: string;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    })[]>;
    findOne(id: string): Promise<{
        productPage: {
            id: string;
            name: string;
            userId: string;
            slug: string;
        };
        reviews: ({
            user: {
                id: string;
                firstName: string | null;
                lastName: string | null;
            };
        } & {
            title: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            content: string | null;
            productId: string;
            rating: number;
            isVerified: boolean;
        })[];
        _count: {
            reviews: number;
            orderItems: number;
        };
        images: {
            id: string;
            createdAt: Date;
            position: number;
            productId: string;
            url: string;
            altText: string | null;
        }[];
        variants: ({
            options: {
                id: string;
                name: string;
                variantId: string;
                value: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            isActive: boolean;
            price: import("@prisma/client/runtime/library").Decimal;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            stock: number;
            productId: string;
        })[];
        productCategories: ({
            category: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                slug: string;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                imageUrl: string | null;
                position: number;
                parentId: string | null;
            };
        } & {
            id: string;
            productId: string;
            categoryId: string;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    }>;
    update(id: string, req: ExpressRequest, updateProductDto: UpdateProductDto): Promise<{
        productPage: {
            id: string;
            name: string;
            slug: string;
        };
        images: {
            id: string;
            createdAt: Date;
            position: number;
            productId: string;
            url: string;
            altText: string | null;
        }[];
        variants: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            isActive: boolean;
            price: import("@prisma/client/runtime/library").Decimal;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            stock: number;
            productId: string;
        }[];
        productCategories: ({
            category: {
                description: string | null;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                slug: string;
                productPageId: string;
                nameAr: string | null;
                nameFr: string | null;
                imageUrl: string | null;
                position: number;
                parentId: string | null;
            };
        } & {
            id: string;
            productId: string;
            categoryId: string;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    }>;
    remove(id: string, req: ExpressRequest): Promise<{
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        tags: string[];
        isActive: boolean;
        productPageId: string;
        nameAr: string | null;
        nameFr: string | null;
        descriptionAr: string | null;
        descriptionFr: string | null;
        price: import("@prisma/client/runtime/library").Decimal;
        compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
        sku: string | null;
        barcode: string | null;
        weight: number | null;
        imageUrl: string | null;
        stock: number;
    }>;
}
