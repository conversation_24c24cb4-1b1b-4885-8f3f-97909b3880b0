"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[562],{3791:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},3928:(e,t,r)=>{r.d(t,{cn:()=>n});var a=r(2987),s=r(607);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},4562:(e,t,r)=>{r.d(t,{Header:()=>u});var a=r(4568),s=r(7261),n=r.n(s),l=r(9344),i=r(9080),o=r(3791),c=r(7620);let h=c.forwardRef(function(e,t){let{title:r,titleId:a,...s}=e;return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},s),r?c.createElement("title",{id:a},r):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),d=c.forwardRef(function(e,t){let{title:r,titleId:a,...s}=e;return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},s),r?c.createElement("title",{id:a},r):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});function u(){let{user:e,signOut:t}=(0,l.A)(),[r,s]=(0,c.useState)(!1);return(0,a.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"ش"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"شاريو"})]})}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,a.jsx)(n(),{href:"/features",className:"text-gray-600 hover:text-gray-900",children:"المميزات"}),(0,a.jsx)(n(),{href:"/pricing",className:"text-gray-600 hover:text-gray-900",children:"الأسعار"}),(0,a.jsx)(n(),{href:"/templates",className:"text-gray-600 hover:text-gray-900",children:"القوالب"}),(0,a.jsx)(n(),{href:"/help",className:"text-gray-600 hover:text-gray-900",children:"المساعدة"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e?(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(n(),{href:"/dashboard",children:(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 ml-2"}),"لوحة التحكم"]})}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:t,children:"تسجيل الخروج"})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n(),{href:"/auth/login",children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:"تسجيل الدخول"})}),(0,a.jsx)(n(),{href:"/auth/register",children:(0,a.jsx)(i.$,{size:"sm",children:"إنشاء حساب"})})]}),(0,a.jsx)("button",{className:"md:hidden p-2",onClick:()=>s(!r),children:r?(0,a.jsx)(h,{className:"w-6 h-6"}):(0,a.jsx)(d,{className:"w-6 h-6"})})]})]}),r&&(0,a.jsx)("div",{className:"md:hidden py-4 border-t",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,a.jsx)(n(),{href:"/features",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>s(!1),children:"المميزات"}),(0,a.jsx)(n(),{href:"/pricing",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>s(!1),children:"الأسعار"}),(0,a.jsx)(n(),{href:"/templates",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>s(!1),children:"القوالب"}),(0,a.jsx)(n(),{href:"/help",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>s(!1),children:"المساعدة"})]})})]})})}},9080:(e,t,r)=>{r.d(t,{$:()=>l});var a=r(4568),s=r(7620),n=r(3928);let l=s.forwardRef((e,t)=>{let{className:r,variant:s="primary",size:l="md",loading:i,children:o,disabled:c,...h}=e;return(0,a.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[l],r),ref:t,disabled:c||i,...h,children:[i&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});l.displayName="Button"},9344:(e,t,r)=>{r.d(t,{O:()=>u,A:()=>m});var a=r(4568),s=r(7620);let n=(0,r(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),l=async()=>{let{error:e}=await n.auth.signOut();return{error:e}},i=async()=>{let{data:{user:e},error:t}=await n.auth.getUser();return{user:e,error:t}},o=r(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let c={validateToken:e=>o.post("/auth/validate",{token:e})};var h=r(5317);let d=(0,s.createContext)(void 0);function u(e){let{children:t}=e,[r,o]=(0,s.useState)(null),[u,m]=(0,s.useState)(!0),x=async()=>{try{let{user:e}=await i();if(o(e),e){let{data:{session:e}}=await n.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await c.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),o(null),localStorage.removeItem("auth_token")}};(0,s.useEffect)(()=>{x().finally(()=>m(!1));let{data:{subscription:e}}=n.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){o(t.user);try{let e=await c.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(o(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let g=async(e,t)=>{try{var r;let{data:a,error:s}=await n.auth.signInWithPassword({email:e,password:t});if(s)throw s;if(null==(r=a.session)?void 0:r.access_token){let e=await c.validateToken(a.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}h.Ay.success("تم تسجيل الدخول بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الدخول";throw h.Ay.error(e),t}},f=async(e,t,r)=>{try{let{error:a}=await n.auth.signUp({email:e,password:t,options:{data:r}});if(a)throw a;h.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(t){let e=t instanceof Error?t.message:"فشل في إنشاء الحساب";throw h.Ay.error(e),t}},v=async()=>{try{await l(),localStorage.removeItem("auth_token"),h.Ay.success("تم تسجيل الخروج بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الخروج";throw h.Ay.error(e),t}};return(0,a.jsx)(d.Provider,{value:{user:r,loading:u,signIn:g,signUp:f,signOut:v,refreshUser:x},children:t})}function m(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}}]);