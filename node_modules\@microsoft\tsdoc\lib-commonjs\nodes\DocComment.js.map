{"version": 3, "file": "DocComment.js", "sourceRoot": "", "sources": ["../../src/nodes/DocComment.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,qCAA0E;AAC1E,2CAA0C;AAC1C,4EAA2E;AAG3E,2DAA0D;AAC1D,yDAAwD;AACxD,2DAA0D;AAO1D;;;GAGG;AACH;IAAgC,8BAAO;IA0ErC;;;OAGG;IACH,oBAAmB,UAAiC;QAClD,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,KAAI,CAAC,cAAc,GAAG,IAAI,uBAAU,CAAC,EAAE,aAAa,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAC5E,KAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,KAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,KAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,KAAI,CAAC,MAAM,GAAG,IAAI,uCAAkB,CAAC,EAAE,aAAa,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAC5E,KAAI,CAAC,UAAU,GAAG,IAAI,uCAAkB,CAAC,EAAE,aAAa,EAAE,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAChF,KAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAE9B,KAAI,CAAC,cAAc,GAAG,IAAI,+CAAsB,EAAE,CAAC;QAEnD,KAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,KAAI,CAAC,aAAa,GAAG,EAAE,CAAC;;IAC1B,CAAC;IAGD,sBAAW,4BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,OAAO,CAAC;QAC7B,CAAC;;;OAAA;IAKD,sBAAW,iCAAS;QAHpB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;;;OAAA;IAKD,sBAAW,oCAAY;QAHvB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAED;;;OAGG;IACI,oCAAe,GAAtB,UAAuB,KAAe;QACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,sCAAiB,GAAxB,UAAyB,KAAe;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,gBAAgB;IACN,oCAAe,GAAzB;QACE;YACE,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC/C,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;YACvD,IAAI,CAAC,YAAY;WACd,IAAI,CAAC,YAAY,SACjB,IAAI,CAAC,SAAS;YACjB,IAAI,CAAC,aAAa;mBACf,IAAI,CAAC,cAAc,CAAC,KAAK,QAC5B;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACI,gCAAW,GAAlB;QACE,IAAM,aAAa,GAAkB,IAAI,6BAAa,EAAE,CAAC;QACzD,IAAM,OAAO,GAAiB,IAAI,2BAAY,EAAE,CAAC;QACjD,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,aAAa,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IACH,iBAAC;AAAD,CAAC,AApKD,CAAgC,iBAAO,GAoKtC;AApKY,gCAAU", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { DocNode, DocNodeKind, type IDocNodeParameters } from './DocNode';\r\nimport { DocSection } from './DocSection';\r\nimport { StandardModifierTagSet } from '../details/StandardModifierTagSet';\r\nimport type { DocBlock } from './DocBlock';\r\nimport type { DocInheritDocTag } from './DocInheritDocTag';\r\nimport { StringBuilder } from '../emitters/StringBuilder';\r\nimport { TSDocEmitter } from '../emitters/TSDocEmitter';\r\nimport { DocParamCollection } from './DocParamCollection';\r\n\r\n/**\r\n * Constructor parameters for {@link DocComment}.\r\n */\r\nexport interface IDocCommentParameters extends IDocNodeParameters {}\r\n\r\n/**\r\n * Represents an entire documentation comment conforming to the TSDoc structure.\r\n * This is the root of the DocNode tree.\r\n */\r\nexport class DocComment extends DocNode {\r\n  /**\r\n   * The main documentation for an API item is separated into a brief \"summary\" section,\r\n   * optionally followed by an `@remarks` block containing additional details.\r\n   *\r\n   * @remarks\r\n   * The summary section should be brief. On a documentation web site, it will be shown\r\n   * on a page that lists summaries for many different API items.  On a detail page for\r\n   * a single item, the summary will be shown followed by the remarks section (if any).\r\n   */\r\n  public summarySection: DocSection;\r\n\r\n  /**\r\n   * The main documentation for an API item is separated into a brief \"summary\" section\r\n   * optionally followed by an `@remarks` block containing additional details.\r\n   *\r\n   * @remarks\r\n   * Unlike the summary, the remarks block may contain lengthy documentation content.\r\n   * The remarks should not restate information from the summary, since the summary section\r\n   * will always be displayed wherever the remarks section appears.  Other sections\r\n   * (e.g. an `@example` block) will be shown after the remarks section.\r\n   */\r\n  public remarksBlock: DocBlock | undefined;\r\n\r\n  /**\r\n   * The `@privateRemarks` tag starts a block of additional commentary that is not meant\r\n   * for an external audience.  A documentation tool must omit this content from an\r\n   * API reference web site.  It should also be omitted when generating a normalized\r\n   * *.d.ts file intended for third-party developers.\r\n   *\r\n   * @remarks\r\n   * A similar effect could be accomplished by enclosing content inside CommonMark\r\n   * `<!-- -->` comments, or by moving the content into a separate `//` TypeScript comment.\r\n   * However, the `@privateRemarks` tag is a more formal convention.\r\n   */\r\n  public privateRemarks: DocBlock | undefined;\r\n\r\n  /**\r\n   * If present, this block indicates that an API item is no loner supported and may be\r\n   * removed in a future release.  The `@deprecated` tag must be followed by a sentence\r\n   * describing the recommended alternative.  Deprecation recursively applies to members\r\n   * of a container.  For example, if a class is deprecated, then so are all of its members.\r\n   */\r\n  public deprecatedBlock: DocBlock | undefined;\r\n\r\n  /**\r\n   * The collection of parsed `@param` blocks for this doc comment.\r\n   */\r\n  public readonly params: DocParamCollection;\r\n\r\n  /**\r\n   * The collection of parsed `@typeParam` blocks for this doc comment.\r\n   */\r\n  public readonly typeParams: DocParamCollection;\r\n\r\n  /**\r\n   * The `@returns` block for this doc comment, or undefined if there is not one.\r\n   */\r\n  public returnsBlock: DocBlock | undefined;\r\n\r\n  /**\r\n   * If this doc comment contains an `@inheritDoc` tag, it will be extracted and associated\r\n   * with the DocComment.\r\n   */\r\n  public inheritDocTag: DocInheritDocTag | undefined;\r\n\r\n  /**\r\n   * The modifier tags for this DocComment.\r\n   */\r\n  public readonly modifierTagSet: StandardModifierTagSet;\r\n\r\n  private _seeBlocks: DocBlock[];\r\n  private _customBlocks: DocBlock[];\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocCommentParameters) {\r\n    super(parameters);\r\n\r\n    this.summarySection = new DocSection({ configuration: this.configuration });\r\n    this.remarksBlock = undefined;\r\n    this.privateRemarks = undefined;\r\n    this.deprecatedBlock = undefined;\r\n    this.params = new DocParamCollection({ configuration: this.configuration });\r\n    this.typeParams = new DocParamCollection({ configuration: this.configuration });\r\n    this.returnsBlock = undefined;\r\n\r\n    this.modifierTagSet = new StandardModifierTagSet();\r\n\r\n    this._seeBlocks = [];\r\n    this._customBlocks = [];\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.Comment;\r\n  }\r\n\r\n  /**\r\n   * The collection of all `@see` DockBlockTag nodes belonging to this doc comment.\r\n   */\r\n  public get seeBlocks(): ReadonlyArray<DocBlock> {\r\n    return this._seeBlocks;\r\n  }\r\n\r\n  /**\r\n   * The collection of all DocBlock nodes belonging to this doc comment.\r\n   */\r\n  public get customBlocks(): ReadonlyArray<DocBlock> {\r\n    return this._customBlocks;\r\n  }\r\n\r\n  /**\r\n   * Append an item to the seeBlocks collection.\r\n   * @internal\r\n   */\r\n  public _appendSeeBlock(block: DocBlock): void {\r\n    this._seeBlocks.push(block);\r\n  }\r\n\r\n  /**\r\n   * Append an item to the customBlocks collection.\r\n   */\r\n  public appendCustomBlock(block: DocBlock): void {\r\n    this._customBlocks.push(block);\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this.summarySection,\r\n      this.remarksBlock,\r\n      this.privateRemarks,\r\n      this.deprecatedBlock,\r\n      this.params.count > 0 ? this.params : undefined,\r\n      this.typeParams.count > 0 ? this.typeParams : undefined,\r\n      this.returnsBlock,\r\n      ...this.customBlocks,\r\n      ...this.seeBlocks,\r\n      this.inheritDocTag,\r\n      ...this.modifierTagSet.nodes\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Generates a doc comment corresponding to the `DocComment` tree.  The output is in a normalized form,\r\n   * and may ignore formatting/spacing from the original input.\r\n   *\r\n   * @remarks\r\n   * After parsing a string, and possibly modifying the result, `emitAsTsdoc()` can be used to render the result\r\n   * as a doc comment in a normalized format.  It can also be used to emit a `DocComment` tree that was constructed\r\n   * manually.\r\n   *\r\n   * This method is provided as convenience for simple use cases.  To customize the output, or if you need\r\n   * to render into a `StringBuilder`, use the {@link TSDocEmitter} class instead.\r\n   */\r\n  public emitAsTsdoc(): string {\r\n    const stringBuilder: StringBuilder = new StringBuilder();\r\n    const emitter: TSDocEmitter = new TSDocEmitter();\r\n    emitter.renderComment(stringBuilder, this);\r\n    return stringBuilder.toString();\r\n  }\r\n}\r\n"]}