import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../supabase.service';
import { UsersService } from '../../users/users.service';
declare const JwtStrategy_base: new (...args: any) => any;
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    private supabaseService;
    private usersService;
    constructor(configService: ConfigService, supabaseService: SupabaseService, usersService: UsersService);
    validate(req: any, payload: any): Promise<{
        id: any;
        email: any;
        firstName: any;
        lastName: any;
        role: any;
    }>;
}
export {};
