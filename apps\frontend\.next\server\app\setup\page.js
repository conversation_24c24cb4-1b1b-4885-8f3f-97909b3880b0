(()=>{var e={};e.id=620,e.ids=[620],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30880:(e,s,r)=>{Promise.resolve().then(r.bind(r,9782)),Promise.resolve().then(r.t.bind(r,49989,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59068:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(38828),a=r(67756),l=r(58618),n=r(61365),i=r.n(n),d=r(43128);let c=i().forwardRef(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,d.cn)("rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm",e),...r,children:s}));c.displayName="Card";let o=i().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let x=i().forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));x.displayName="CardTitle";let m=i().forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,d.cn)("text-sm text-gray-500",e),...s}));m.displayName="CardDescription";let p=i().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("p-6 pt-0",e),...s}));p.displayName="CardContent",i().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter";var u=r(53847);let h=n.forwardRef(function({title:e,titleId:s,...r},t){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},r),e?n.createElement("title",{id:s},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}),j=n.forwardRef(function({title:e,titleId:s,...r},t){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},r),e?n.createElement("title",{id:s},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),f=n.forwardRef(function({title:e,titleId:s,...r},t){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},r),e?n.createElement("title",{id:s},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});function g(){return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(a.Header,{}),(0,t.jsxs)("div",{className:"max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"إعداد منصة شاريو"}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:"اتبع هذه الخطوات لإعداد منصة شاريو بالكامل"})]}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)(c,{children:[(0,t.jsxs)(o,{children:[(0,t.jsxs)(x,{className:"flex items-center",children:[(0,t.jsx)(h,{className:"w-6 h-6 text-yellow-500 ml-2"}),"الحالة الحالية"]}),(0,t.jsx)(m,{children:"أنت حالياً في وضع التجربة. بعض المميزات محدودة."})]}),(0,t.jsx)(p,{children:(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(h,{className:"h-5 w-5 text-yellow-400"}),(0,t.jsxs)("div",{className:"mr-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"وضع التجربة نشط"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,t.jsx)("p",{children:"لتفعيل جميع المميزات مثل التسجيل الحقيقي، قاعدة البيانات، والمدفوعات، يجب إعداد Supabase."})})]})]})})})]}),(0,t.jsxs)(c,{children:[(0,t.jsxs)(o,{children:[(0,t.jsxs)(x,{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold ml-3",children:"1"}),"إنشاء مشروع Supabase"]}),(0,t.jsx)(m,{children:"إنشاء قاعدة بيانات مجانية وخدمات المصادقة"})]}),(0,t.jsxs)(p,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(j,{className:"w-5 h-5 text-green-500 mt-0.5 ml-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"اذهب إلى Supabase"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"قم بزيارة supabase.com وأنشئ حساب مجاني"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(j,{className:"w-5 h-5 text-green-500 mt-0.5 ml-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"أنشئ مشروع جديد"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:'اختر اسم "Sharyou" ومنطقة أوروبا الغربية'})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(j,{className:"w-5 h-5 text-green-500 mt-0.5 ml-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"احصل على المفاتيح"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"من الإعدادات → API، انسخ URL والمفاتيح"})]})]})]}),(0,t.jsx)("a",{href:"https://supabase.com",target:"_blank",rel:"noopener noreferrer",children:(0,t.jsx)(u.$,{className:"w-full",children:"فتح Supabase"})})]})]}),(0,t.jsxs)(c,{children:[(0,t.jsxs)(o,{children:[(0,t.jsxs)(x,{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold ml-3",children:"2"}),"تكوين متغيرات البيئة"]}),(0,t.jsx)(m,{children:"إضافة مفاتيح Supabase إلى ملفات التكوين"})]}),(0,t.jsxs)(p,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-md p-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Frontend (.env.local):"}),(0,t.jsx)("pre",{className:"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto",children:`NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key`})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-md p-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Backend (.env):"}),(0,t.jsx)("pre",{className:"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto",children:`DATABASE_URL="your-supabase-connection-string"
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"`})]})]})]}),(0,t.jsxs)(c,{children:[(0,t.jsxs)(o,{children:[(0,t.jsxs)(x,{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold ml-3",children:"3"}),"إعداد قاعدة البيانات"]}),(0,t.jsx)(m,{children:"تشغيل أوامر إنشاء الجداول والبيانات التجريبية"})]}),(0,t.jsx)(p,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"bg-gray-50 rounded-md p-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"الأوامر المطلوبة:"}),(0,t.jsx)("pre",{className:"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto",children:`cd apps/backend
npm run db:generate
npm run db:push
npm run db:seed`})]})})]}),(0,t.jsxs)(c,{children:[(0,t.jsxs)(o,{children:[(0,t.jsxs)(x,{className:"flex items-center",children:[(0,t.jsx)("span",{className:"w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-bold ml-3",children:"4"}),"تشغيل المنصة"]}),(0,t.jsx)(m,{children:"بدء تشغيل الخادم والواجهة الأمامية"})]}),(0,t.jsxs)(p,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-md p-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"تشغيل كلا الخادمين:"}),(0,t.jsx)("pre",{className:"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto",children:"npm run dev"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-md",children:[(0,t.jsx)("h5",{className:"font-medium text-blue-900",children:"الواجهة الأمامية"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"http://localhost:3000"})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-md",children:[(0,t.jsx)("h5",{className:"font-medium text-green-900",children:"API الخلفية"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"http://localhost:3001/api"})]})]})]})]}),(0,t.jsxs)(c,{children:[(0,t.jsx)(o,{children:(0,t.jsxs)(x,{className:"flex items-center",children:[(0,t.jsx)(f,{className:"w-6 h-6 text-gray-500 ml-2"}),"تحتاج مساعدة؟"]})}),(0,t.jsx)(p,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"إذا واجهت أي مشاكل في الإعداد، تحقق من:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"صحة متغيرات البيئة"}),(0,t.jsx)("li",{children:"اتصال الإنترنت"}),(0,t.jsx)("li",{children:"أن المنافذ 3000 و 3001 متاحة"}),(0,t.jsx)("li",{children:"تثبيت جميع التبعيات"})]}),(0,t.jsx)("div",{className:"mt-4 p-4 bg-blue-50 rounded-md",children:(0,t.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,t.jsx)("strong",{children:"ملاحظة:"})," يمكنك استخدام المنصة في وضع التجربة حتى لو لم تكمل الإعداد. ستتمكن من رؤية جميع الواجهات والتصميم."]})})]})})]})]})]}),(0,t.jsx)(l.w,{})]})}},61944:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(24332),a=r(48819),l=r(67851),n=r.n(l),i=r(97540),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59068)),"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,20685)),"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/setup/page",pathname:"/setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67328:(e,s,r)=>{Promise.resolve().then(r.bind(r,67756)),Promise.resolve().then(r.t.bind(r,42671,23))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[191,667,661,453,703],()=>r(61944));module.exports=t})();