{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/lib/supabase.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey);\n\nexport const signUp = async (email: string, password: string, metadata?: any) => {\n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: metadata,\n    },\n  });\n  return { data, error };\n};\n\nexport const signIn = async (email: string, password: string) => {\n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password,\n  });\n  return { data, error };\n};\n\nexport const signOut = async () => {\n  const { error } = await supabase.auth.signOut();\n  return { error };\n};\n\nexport const getCurrentUser = async () => {\n  const { data: { user }, error } = await supabase.auth.getUser();\n  return { user, error };\n};\n\nexport const getSession = async () => {\n  const { data: { session }, error } = await supabase.auth.getSession();\n  return { session, error };\n};\n"], "names": [], "mappings": ";;;;;;;;AAEoB;AAFpB;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAElD,MAAM,SAAS,OAAO,OAAe,UAAkB;IAC5D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;QACR;IACF;IACA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,SAAS,OAAO,OAAe;IAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IACA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,UAAU;IACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7C,OAAO;QAAE;IAAM;AACjB;AAEO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7D,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,aAAa;IACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IACnE,OAAO;QAAE;QAAS;IAAM;AAC1B", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';\n\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('auth_token');\n      window.location.href = '/auth/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// API endpoints\nexport const authApi = {\n  validateToken: (token: string) => api.post('/auth/validate', { token }),\n  getProfile: () => api.get('/auth/profile'),\n};\n\nexport const usersApi = {\n  getProfile: () => api.get('/users/me'),\n  updateProfile: (data: any) => api.patch('/users/me', data),\n  getProductPages: () => api.get('/users/me/product-pages'),\n};\n\nexport const productPagesApi = {\n  create: (data: any) => api.post('/product-pages', data),\n  getAll: () => api.get('/product-pages'),\n  getBySlug: (slug: string) => api.get(`/product-pages/by-slug/${slug}`),\n  getByDomain: (domain: string) => api.get(`/product-pages/by-domain?domain=${domain}`),\n  getMyPages: () => api.get('/product-pages/my-pages'),\n  getById: (id: string) => api.get(`/product-pages/${id}`),\n  update: (id: string, data: any) => api.patch(`/product-pages/${id}`, data),\n  delete: (id: string) => api.delete(`/product-pages/${id}`),\n};\n\nexport const productsApi = {\n  create: (data: any) => api.post('/products', data),\n  getAll: (productPageId?: string) => api.get('/products', { params: { productPageId } }),\n  getByProductPage: (productPageId: string, isActive?: boolean) => \n    api.get(`/products/by-product-page/${productPageId}`, { params: { isActive } }),\n  search: (productPageId: string, query: string) => \n    api.get(`/products/search/${productPageId}`, { params: { q: query } }),\n  getById: (id: string) => api.get(`/products/${id}`),\n  update: (id: string, data: any) => api.patch(`/products/${id}`, data),\n  delete: (id: string) => api.delete(`/products/${id}`),\n};\n\nexport const ordersApi = {\n  create: (data: any) => api.post('/orders', data),\n  getAll: (productPageId?: string) => api.get('/orders', { params: { productPageId } }),\n  getMyOrders: () => api.get('/orders/my-orders'),\n  getByProductPage: (productPageId: string) => api.get(`/orders/by-product-page/${productPageId}`),\n  getById: (id: string) => api.get(`/orders/${id}`),\n  update: (id: string, data: any) => api.patch(`/orders/${id}`, data),\n};\n\nexport const analyticsApi = {\n  getDashboard: (productPageId: string) => api.get(`/analytics/dashboard/${productPageId}`),\n  getRevenueChart: (productPageId: string, days?: number) => \n    api.get(`/analytics/revenue-chart/${productPageId}`, { params: { days } }),\n  getOrderStatus: (productPageId: string) => api.get(`/analytics/order-status/${productPageId}`),\n  recordPageView: (productPageId: string) => api.post(`/analytics/page-view/${productPageId}`),\n  recordVisitor: (productPageId: string) => api.post(`/analytics/visitor/${productPageId}`),\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,iEAAmC;AAEjD,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6CAA6C;AAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,eAAe,CAAC,QAAkB,IAAI,IAAI,CAAC,kBAAkB;YAAE;QAAM;IACrE,YAAY,IAAM,IAAI,GAAG,CAAC;AAC5B;AAEO,MAAM,WAAW;IACtB,YAAY,IAAM,IAAI,GAAG,CAAC;IAC1B,eAAe,CAAC,OAAc,IAAI,KAAK,CAAC,aAAa;IACrD,iBAAiB,IAAM,IAAI,GAAG,CAAC;AACjC;AAEO,MAAM,kBAAkB;IAC7B,QAAQ,CAAC,OAAc,IAAI,IAAI,CAAC,kBAAkB;IAClD,QAAQ,IAAM,IAAI,GAAG,CAAC;IACtB,WAAW,CAAC,OAAiB,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM;IACrE,aAAa,CAAC,SAAmB,IAAI,GAAG,CAAC,CAAC,gCAAgC,EAAE,QAAQ;IACpF,YAAY,IAAM,IAAI,GAAG,CAAC;IAC1B,SAAS,CAAC,KAAe,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;IACvD,QAAQ,CAAC,IAAY,OAAc,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;IACrE,QAAQ,CAAC,KAAe,IAAI,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;AAC3D;AAEO,MAAM,cAAc;IACzB,QAAQ,CAAC,OAAc,IAAI,IAAI,CAAC,aAAa;IAC7C,QAAQ,CAAC,gBAA2B,IAAI,GAAG,CAAC,aAAa;YAAE,QAAQ;gBAAE;YAAc;QAAE;IACrF,kBAAkB,CAAC,eAAuB,WACxC,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,eAAe,EAAE;YAAE,QAAQ;gBAAE;YAAS;QAAE;IAC/E,QAAQ,CAAC,eAAuB,QAC9B,IAAI,GAAG,CAAC,CAAC,iBAAiB,EAAE,eAAe,EAAE;YAAE,QAAQ;gBAAE,GAAG;YAAM;QAAE;IACtE,SAAS,CAAC,KAAe,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IAClD,QAAQ,CAAC,IAAY,OAAc,IAAI,KAAK,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAChE,QAAQ,CAAC,KAAe,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;AACtD;AAEO,MAAM,YAAY;IACvB,QAAQ,CAAC,OAAc,IAAI,IAAI,CAAC,WAAW;IAC3C,QAAQ,CAAC,gBAA2B,IAAI,GAAG,CAAC,WAAW;YAAE,QAAQ;gBAAE;YAAc;QAAE;IACnF,aAAa,IAAM,IAAI,GAAG,CAAC;IAC3B,kBAAkB,CAAC,gBAA0B,IAAI,GAAG,CAAC,CAAC,wBAAwB,EAAE,eAAe;IAC/F,SAAS,CAAC,KAAe,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI;IAChD,QAAQ,CAAC,IAAY,OAAc,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE;AAChE;AAEO,MAAM,eAAe;IAC1B,cAAc,CAAC,gBAA0B,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,eAAe;IACxF,iBAAiB,CAAC,eAAuB,OACvC,IAAI,GAAG,CAAC,CAAC,yBAAyB,EAAE,eAAe,EAAE;YAAE,QAAQ;gBAAE;YAAK;QAAE;IAC1E,gBAAgB,CAAC,gBAA0B,IAAI,GAAG,CAAC,CAAC,wBAAwB,EAAE,eAAe;IAC7F,gBAAgB,CAAC,gBAA0B,IAAI,IAAI,CAAC,CAAC,qBAAqB,EAAE,eAAe;IAC3F,eAAe,CAAC,gBAA0B,IAAI,IAAI,CAAC,CAAC,mBAAmB,EAAE,eAAe;AAC1F", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User } from '@supabase/supabase-js';\nimport { supabase, getCurrentUser, signOut as supabaseSignOut } from '@/lib/supabase';\nimport { authApi } from '@/lib/api';\nimport toast from 'react-hot-toast';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, metadata?: any) => Promise<void>;\n  signOut: () => Promise<void>;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  const refreshUser = async () => {\n    try {\n      const { user: currentUser } = await getCurrentUser();\n      setUser(currentUser);\n      \n      if (currentUser) {\n        // Get the session to extract the JWT token\n        const { data: { session } } = await supabase.auth.getSession();\n        if (session?.access_token) {\n          // Validate token with our backend and sync user data\n          try {\n            const response = await authApi.validateToken(session.access_token);\n            localStorage.setItem('auth_token', response.data.access_token);\n          } catch (error) {\n            console.error('Failed to validate token with backend:', error);\n          }\n        }\n      } else {\n        localStorage.removeItem('auth_token');\n      }\n    } catch (error) {\n      console.error('Error refreshing user:', error);\n      setUser(null);\n      localStorage.removeItem('auth_token');\n    }\n  };\n\n  useEffect(() => {\n    // Get initial session\n    refreshUser().finally(() => setLoading(false));\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          setUser(session.user);\n          try {\n            const response = await authApi.validateToken(session.access_token);\n            localStorage.setItem('auth_token', response.data.access_token);\n          } catch (error) {\n            console.error('Failed to validate token with backend:', error);\n          }\n        } else if (event === 'SIGNED_OUT') {\n          setUser(null);\n          localStorage.removeItem('auth_token');\n        }\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) throw error;\n\n      if (data.session?.access_token) {\n        const response = await authApi.validateToken(data.session.access_token);\n        localStorage.setItem('auth_token', response.data.access_token);\n      }\n\n      toast.success('تم تسجيل الدخول بنجاح');\n    } catch (error: any) {\n      toast.error(error.message || 'فشل في تسجيل الدخول');\n      throw error;\n    }\n  };\n\n  const signUp = async (email: string, password: string, metadata?: any) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: metadata,\n        },\n      });\n\n      if (error) throw error;\n\n      toast.success('تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني');\n    } catch (error: any) {\n      toast.error(error.message || 'فشل في إنشاء الحساب');\n      throw error;\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      await supabaseSignOut();\n      localStorage.removeItem('auth_token');\n      toast.success('تم تسجيل الخروج بنجاح');\n    } catch (error: any) {\n      toast.error(error.message || 'فشل في تسجيل الخروج');\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    refreshUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;;;AANA;;;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD;YACjD,QAAQ;YAER,IAAI,aAAa;gBACf,2CAA2C;gBAC3C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,6IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAC5D,IAAI,SAAS,cAAc;oBACzB,qDAAqD;oBACrD,IAAI;wBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAO,CAAC,aAAa,CAAC,QAAQ,YAAY;wBACjE,aAAa,OAAO,CAAC,cAAc,SAAS,IAAI,CAAC,YAAY;oBAC/D,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0CAA0C;oBAC1D;gBACF;YACF,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,QAAQ;YACR,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,cAAc,OAAO;0CAAC,IAAM,WAAW;;YAEvC,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,6IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,IAAI,UAAU,eAAe,SAAS,MAAM;wBAC1C,QAAQ,QAAQ,IAAI;wBACpB,IAAI;4BACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAO,CAAC,aAAa,CAAC,QAAQ,YAAY;4BACjE,aAAa,OAAO,CAAC,cAAc,SAAS,IAAI,CAAC,YAAY;wBAC/D,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,0CAA0C;wBAC1D;oBACF,OAAO,IAAI,UAAU,cAAc;wBACjC,QAAQ;wBACR,aAAa,UAAU,CAAC;oBAC1B;gBACF;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,6IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,IAAI,KAAK,OAAO,EAAE,cAAc;gBAC9B,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAO,CAAC,aAAa,CAAC,KAAK,OAAO,CAAC,YAAY;gBACtE,aAAa,OAAO,CAAC,cAAc,SAAS,IAAI,CAAC,YAAY;YAC/D;YAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,6IAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,CAAA,GAAA,6IAAA,CAAA,UAAe,AAAD;YACpB,aAAa,UAAU,CAAC;YACxB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAzHgB;KAAA;AA2HT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/providers/Providers.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport { useState } from 'react';\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 60 * 1000, // 1 minute\n            retry: 1,\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <AuthProvider>\n        {children}\n        <Toaster\n          position=\"top-right\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff',\n            },\n            success: {\n              duration: 3000,\n              iconTheme: {\n                primary: '#4ade80',\n                secondary: '#fff',\n              },\n            },\n            error: {\n              duration: 4000,\n              iconTheme: {\n                primary: '#ef4444',\n                secondary: '#fff',\n              },\n            },\n          }}\n        />\n      </AuthProvider>\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS,UAAU,EAAE,QAAQ,EAAiC;;IACnE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;8BAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,OAAO;oBACT;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,6LAAC,sJAAA,CAAA,eAAY;;gBACV;8BACD,6LAAC,0JAAA,CAAA,UAAO;oBACN,UAAS;oBACT,cAAc;wBACZ,UAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,OAAO;wBACT;wBACA,SAAS;4BACP,UAAU;4BACV,WAAW;gCACT,SAAS;gCACT,WAAW;4BACb;wBACF;wBACA,OAAO;4BACL,UAAU;4BACV,WAAW;gCACT,SAAS;gCACT,WAAW;4BACb;wBACF;oBACF;;;;;;;;;;;;;;;;;AAKV;GA5CgB;KAAA", "debugId": null}}]}