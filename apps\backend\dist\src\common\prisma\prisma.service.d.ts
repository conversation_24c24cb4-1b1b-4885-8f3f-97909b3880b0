import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';
export declare class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
    private configService;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    findManyWithTenant<T>(model: any, productPageId: string, args?: any): Promise<T[]>;
    createWithTenant<T>(model: any, productPageId: string, data: any): Promise<T>;
    updateWithTenant<T>(model: any, productPageId: string, where: any, data: any): Promise<T>;
    deleteWithTenant<T>(model: any, productPageId: string, where: any): Promise<T>;
}
