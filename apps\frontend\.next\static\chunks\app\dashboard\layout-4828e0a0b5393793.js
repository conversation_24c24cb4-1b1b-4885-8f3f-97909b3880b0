(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{329:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},516:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},2942:(e,t,r)=>{"use strict";var a=r(2418);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},3791:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},3928:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2987),s=r(607);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},4689:(e,t,r)=>{Promise.resolve().then(r.bind(r,7192))},7192:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(4568),s=r(9344),n=r(2942),l=r(7620),i=r(7261),o=r.n(i),c=r(9080);let d=l.forwardRef(function(e,t){let{title:r,titleId:a,...s}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},s),r?l.createElement("title",{id:a},r):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))});var u=r(9088),h=r(329);let m=l.forwardRef(function(e,t){let{title:r,titleId:a,...s}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},s),r?l.createElement("title",{id:a},r):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});var f=r(516),v=r(3791);function g(e){var t;let{children:r}=e,{user:i,loading:g,signOut:x}=(0,s.A)(),w=(0,n.useRouter)();if((0,l.useEffect)(()=>{g||i||w.push("/auth/login")},[i,g,w]),g)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})});if(!i)return null;let b=[{name:"لوحة التحكم",href:"/dashboard",icon:d},{name:"متاجري",href:"/dashboard/stores",icon:u.A},{name:"التحليلات",href:"/dashboard/analytics",icon:h.A},{name:"الإعدادات",href:"/dashboard/settings",icon:m}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"flex items-center justify-center h-16 px-4 border-b",children:(0,a.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"ش"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"شاريو"})]})}),(0,a.jsxs)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[b.map(e=>(0,a.jsxs)(o(),{href:e.href,className:"flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900",children:[(0,a.jsx)(e.icon,{className:"w-5 h-5 ml-3"}),e.name]},e.name)),(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsx)(o(),{href:"/dashboard/stores/new",children:(0,a.jsxs)(c.$,{className:"w-full justify-start",size:"sm",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 ml-2"}),"إنشاء متجر جديد"]})})})]}),(0,a.jsxs)("div",{className:"p-4 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:(null==(t=i.user_metadata)?void 0:t.firstName)||i.email}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:i.email})]})]}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",className:"w-full",onClick:x,children:"تسجيل الخروج"})]})]})}),(0,a.jsx)("div",{className:"pr-64",children:(0,a.jsx)("main",{className:"py-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r})})})]})}},9080:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(4568),s=r(7620),n=r(3928);let l=s.forwardRef((e,t)=>{let{className:r,variant:s="primary",size:l="md",loading:i,children:o,disabled:c,...d}=e;return(0,a.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[l],r),ref:t,disabled:c||i,...d,children:[i&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});l.displayName="Button"},9088:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(7620);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))})},9344:(e,t,r)=>{"use strict";r.d(t,{O:()=>h,A:()=>m});var a=r(4568),s=r(7620);let n=(0,r(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),l=async()=>{let{error:e}=await n.auth.signOut();return{error:e}},i=async()=>{let{data:{user:e},error:t}=await n.auth.getUser();return{user:e,error:t}},o=r(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let c={validateToken:e=>o.post("/auth/validate",{token:e})};var d=r(5317);let u=(0,s.createContext)(void 0);function h(e){let{children:t}=e,[r,o]=(0,s.useState)(null),[h,m]=(0,s.useState)(!0),f=async()=>{try{let{user:e}=await i();if(o(e),e){let{data:{session:e}}=await n.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await c.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),o(null),localStorage.removeItem("auth_token")}};(0,s.useEffect)(()=>{f().finally(()=>m(!1));let{data:{subscription:e}}=n.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){o(t.user);try{let e=await c.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(o(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let v=async(e,t)=>{try{var r;let{data:a,error:s}=await n.auth.signInWithPassword({email:e,password:t});if(s)throw s;if(null==(r=a.session)?void 0:r.access_token){let e=await c.validateToken(a.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الدخول"),e}},g=async(e,t,r)=>{try{let{data:a,error:s}=await n.auth.signUp({email:e,password:t,options:{data:r}});if(s)throw s;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(e){throw d.Ay.error(e.message||"فشل في إنشاء الحساب"),e}},x=async()=>{try{await l(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الخروج"),e}};return(0,a.jsx)(u.Provider,{value:{user:r,loading:h,signIn:v,signUp:g,signOut:x,refreshUser:f},children:t})}function m(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[286,661,587,315,358],()=>t(4689)),_N_E=e.O()}]);