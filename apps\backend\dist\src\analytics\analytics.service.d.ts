import { PrismaService } from '../common/prisma/prisma.service';
import { ProductPagesService } from '../product-pages/product-pages.service';
export declare class AnalyticsService {
    private prisma;
    private productPagesService;
    constructor(prisma: PrismaService, productPagesService: ProductPagesService);
    getDashboardMetrics(productPageId: string, userId: string): Promise<{
        overview: {
            totalProducts: any;
            totalOrders: any;
            totalRevenue: number;
            ordersLast30Days: any;
            ordersLast7Days: any;
            revenueLast30Days: number;
            revenueLast7Days: number;
        };
        topProducts: any[];
        recentOrders: any;
    }>;
    getRevenueChart(productPageId: string, userId: string, days?: number): Promise<{
        date: string;
        revenue: any;
    }[]>;
    getOrderStatusDistribution(productPageId: string, userId: string): Promise<any>;
    recordPageView(productPageId: string): Promise<void>;
    recordVisitor(productPageId: string): Promise<void>;
}
