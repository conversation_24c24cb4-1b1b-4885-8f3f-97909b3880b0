{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,0DAAsD;AACtD,6DAAyD;AAGlD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAE/C;IACA;IACA;IAHV,YACU,aAA4B,EAC5B,eAAgC,EAChC,YAA0B;QAElC,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC;YAC5C,iBAAiB,EAAE,IAAI;SACxB,CAAC,CAAC;QATK,kBAAa,GAAb,aAAa,CAAe;QAC5B,oBAAe,GAAf,eAAe,CAAiB;QAChC,iBAAY,GAAZ,YAAY,CAAc;IAQpC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAQ,EAAE,OAAY;QACnC,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,yBAAU,CAAC,2BAA2B,EAAE,CAAC,GAAG,CAAC,CAAC;YAE5D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,8BAAqB,CAAC,mBAAmB,CAAC,CAAC;YACvD,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEnE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,8BAAqB,CAAC,eAAe,CAAC,CAAC;YACnD,CAAC;YAGD,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAE7D,IAAI,CAAC,IAAI,EAAE,CAAC;gBAEV,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBACpC,EAAE,EAAE,YAAY,CAAC,EAAE;oBACnB,KAAK,EAAE,YAAY,CAAC,KAAM;oBAC1B,SAAS,EAAE,YAAY,CAAC,aAAa,EAAE,SAAS;oBAChD,QAAQ,EAAE,YAAY,CAAC,aAAa,EAAE,QAAQ;oBAC9C,WAAW,EAAE,YAAY,CAAC,aAAa,EAAE,WAAW;iBACrD,CAAC,CAAC;YACL,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,8BAAqB,CAAC,uBAAuB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;CACF,CAAA;AAvDY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,sBAAa;QACX,kCAAe;QAClB,4BAAY;GAJzB,WAAW,CAuDvB"}