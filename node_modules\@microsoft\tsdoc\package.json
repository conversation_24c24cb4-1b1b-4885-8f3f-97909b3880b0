{"name": "@microsoft/tsdoc", "version": "0.15.1", "description": "A parser for the TypeScript doc comment syntax", "keywords": ["TypeScript", "documentation", "doc", "comments", "JSDoc", "parser", "standard"], "repository": {"type": "git", "url": "https://github.com/microsoft/tsdoc", "directory": "tsdoc"}, "homepage": "https://tsdoc.org/", "main": "lib-commonjs/index.js", "module": "lib/index.js", "typings": "lib/index.d.ts", "license": "MIT", "devDependencies": {"@rushstack/heft-web-rig": "~0.24.11", "@rushstack/heft": "^0.66.13", "@types/heft-jest": "1.0.3", "eslint": "~8.57.0", "eslint-plugin-header": "~3.1.1"}, "scripts": {"build": "heft test --clean", "watch": "heft test --clean --watch", "_phase:build": "heft run --only build -- --clean", "_phase:test": "heft run --only test -- --clean"}}