{"version": 3, "file": "DocInlineTag.js", "sourceRoot": "", "sources": ["../../src/nodes/DocInlineTag.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEjD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAGL,gBAAgB,EACjB,MAAM,oBAAoB,CAAC;AAgB5B;;;;;;;;;GASG;AACH;IAAkC,gCAAgB;IAIhD;;;OAGG;IACH,sBAAmB,UAAmE;QACpF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBACjC,KAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC;oBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,oBAAoB;oBAC7C,OAAO,EAAE,UAAU,CAAC,iBAAiB;iBACtC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC;QAC3C,CAAC;;IACH,CAAC;IAGD,sBAAW,8BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,SAAS,CAAC;QAC/B,CAAC;;;OAAA;IAQD,sBAAW,oCAAU;QANrB;;;;;WAKG;aACH;YACE,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAED,gBAAgB;IACN,8CAAuB,GAAjC;QACE,WAAW;QACX,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACnC,CAAC;IACH,mBAAC;AAAD,CAAC,AAnDD,CAAkC,gBAAgB,GAmDjD", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport {\r\n  type IDocInlineTagBaseParameters,\r\n  type IDocInlineTagBaseParsedParameters,\r\n  DocInlineTagBase\r\n} from './DocInlineTagBase';\r\n\r\n/**\r\n * Constructor parameters for {@link DocInlineTag}.\r\n */\r\nexport interface IDocInlineTagParameters extends IDocInlineTagBaseParameters {\r\n  tagContent: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocInlineTag}.\r\n */\r\nexport interface IDocInlineTagParsedParameters extends IDocInlineTagBaseParsedParameters {\r\n  tagContentExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a generic TSDoc inline tag, including custom tags.\r\n *\r\n * @remarks\r\n * NOTE: Certain tags such as `{@link}` and `{@inheritDoc}` have specialized structures and parser rules,\r\n * and thus are represented using {@link DocLinkTag} or {@link DocInheritDocTag} instead.  However, if the\r\n * specialized parser rule encounters a syntax error, but the outer framing is correct, then the parser constructs\r\n * a generic `DocInlineTag` instead of `DocErrorText`.  This means, for example, that it is possible sometimes for\r\n * `DocInlineTag.tagName` to be `\"@link\"`.\r\n */\r\nexport class DocInlineTag extends DocInlineTagBase {\r\n  private _tagContent: string | undefined;\r\n  private readonly _tagContentExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocInlineTagParameters | IDocInlineTagParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      if (parameters.tagContentExcerpt) {\r\n        this._tagContentExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.InlineTag_TagContent,\r\n          content: parameters.tagContentExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._tagContent = parameters.tagContent;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.InlineTag;\r\n  }\r\n\r\n  /**\r\n   * The tag content.\r\n   * @remarks\r\n   * For example, if the tag is `{@myTag x=12.34 y=56.78 }` then the tag content\r\n   * would be `x=12.34 y=56.78 `, including the trailing space but not the leading space.\r\n   */\r\n  public get tagContent(): string {\r\n    if (this._tagContent === undefined) {\r\n      if (this._tagContentExcerpt) {\r\n        this._tagContent = this._tagContentExcerpt.content.toString();\r\n      } else {\r\n        return '';\r\n      }\r\n    }\r\n    return this._tagContent;\r\n  }\r\n\r\n  /** @override */\r\n  protected getChildNodesForContent(): ReadonlyArray<DocNode | undefined> {\r\n    // abstract\r\n    return [this._tagContentExcerpt];\r\n  }\r\n}\r\n"]}