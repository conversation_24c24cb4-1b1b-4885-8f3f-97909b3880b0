{"version": 3, "file": "DocExcerpt.js", "sourceRoot": "", "sources": ["../../src/nodes/DocExcerpt.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,qCAA0E;AAE1E,yCAA4C;AAE5C,yDAAyD;AAEzD;;GAEG;AACH,IAAY,WA0EX;AA1ED,WAAY,WAAW;IACrB,kCAAmB,CAAA;IAEnB,oCAAqB,CAAA;IAErB,sEAAuD,CAAA;IACvD,8CAA+B,CAAA;IAC/B,sEAAuD,CAAA;IAEvD,oFAAqE,CAAA;IACrE,kFAAmE,CAAA;IACnE,kFAAmE,CAAA;IAEnE;;;OAGG;IACH,sCAAuB,CAAA;IAEvB;;;;OAIG;IACH,kDAAmC,CAAA;IAEnC,0CAA2B,CAAA;IAE3B,kEAAmD,CAAA;IACnD,0DAA2C,CAAA;IAC3C,kDAAmC,CAAA;IACnC,kEAAmD,CAAA;IAEnD,wDAAyC,CAAA;IACzC,4DAA6C,CAAA;IAC7C,0DAA2C,CAAA;IAE3C,0EAA2D,CAAA;IAC3D,kDAAmC,CAAA;IACnC,0EAA2D,CAAA;IAE3D,8EAA+D,CAAA;IAC/D,sDAAuC,CAAA;IACvC,8EAA+D,CAAA;IAE/D,wEAAyD,CAAA;IACzD,sDAAuC,CAAA;IACvC,4DAA6C,CAAA;IAC7C,wEAAyD,CAAA;IAEzD,gEAAiD,CAAA;IACjD,4CAA6B,CAAA;IAC7B,oDAAqC,CAAA;IAErC,wEAAyD,CAAA;IACzD,0EAA2D,CAAA;IAC3D,0EAA2D,CAAA;IAE3D,0DAA2C,CAAA;IAC3C,kFAAmE,CAAA;IACnE,8DAA+C,CAAA;IAC/C,oFAAqE,CAAA;IAErE,gDAAiC,CAAA;IAEjC,0EAA2D,CAAA;IAC3D,4EAA6D,CAAA;IAE7D,oEAAqD,CAAA;IACrD,sDAAuC,CAAA;IAEvC,sCAAuB,CAAA;IAEvB,sCAAuB,CAAA;AACzB,CAAC,EA1EW,WAAW,2BAAX,WAAW,QA0EtB;AAYD;;;;;;;;;;;;;GAaG;AACH;IAAgC,8BAAO;IAIrC;;;OAGG;IACH,oBAAmB,UAAiC;QAClD,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,UAAU,CAAC,WAAW,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;YACnD,KAAoB,UAA0B,EAA1B,KAAA,UAAU,CAAC,OAAQ,CAAC,MAAM,EAA1B,cAA0B,EAA1B,IAA0B,EAAE,CAAC;gBAA5C,IAAM,KAAK,SAAA;gBACd,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,iBAAS,CAAC,OAAO,CAAC;oBACvB,KAAK,iBAAS,CAAC,OAAO,CAAC;oBACvB,KAAK,iBAAS,CAAC,UAAU;wBACvB,MAAM;oBACR;wBACE,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;gBAC7F,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC;QAC3C,KAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;;IACrC,CAAC;IAGD,sBAAW,4BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,OAAO,CAAC;QAC7B,CAAC;;;OAAA;IAKD,sBAAW,mCAAW;QAHtB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;;;OAAA;IAQD,sBAAW,+BAAO;QANlB;;;;;WAKG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;;;OAAA;IACH,iBAAC;AAAD,CAAC,AAjDD,CAAgC,iBAAO,GAiDtC;AAjDY,gCAAU", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See L<PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { DocNode, type IDocNodeParameters, DocNodeKind } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { TokenKind } from '../parser/Token';\r\n\r\n/* eslint-disable @typescript-eslint/naming-convention */\r\n\r\n/**\r\n * Indicates the type of {@link DocExcerpt}.\r\n */\r\nexport enum ExcerptKind {\r\n  Spacing = 'Spacing',\r\n\r\n  BlockTag = 'BlockTag',\r\n\r\n  CodeSpan_OpeningDelimiter = 'CodeSpan_OpeningDelimiter',\r\n  CodeSpan_Code = 'CodeSpan_Code',\r\n  CodeSpan_ClosingDelimiter = 'CodeSpan_ClosingDelimiter',\r\n\r\n  DeclarationReference_PackageName = 'DeclarationReference_PackageName',\r\n  DeclarationReference_ImportPath = 'DeclarationReference_ImportPath',\r\n  DeclarationReference_ImportHash = 'DeclarationReference_ImportHash',\r\n\r\n  /**\r\n   * Input characters that were reported as an error and do not appear to be part of a valid expression.\r\n   * A syntax highlighter might display them with an error color (e.g. red).\r\n   */\r\n  ErrorText = 'ErrorText',\r\n\r\n  /**\r\n   * Input characters that do not conform to the TSDoc specification, but were recognized by the parser, for example\r\n   * as a known JSDoc pattern.  A syntax highlighter should not display them with an error color (e.g. red)\r\n   * because the error reporting may be suppressed for \"lax\" parsing of legacy source code.\r\n   */\r\n  NonstandardText = 'NonstandardText',\r\n\r\n  EscapedText = 'EscapedText',\r\n\r\n  FencedCode_OpeningFence = 'FencedCode_OpeningFence',\r\n  FencedCode_Language = 'FencedCode_Language',\r\n  FencedCode_Code = 'FencedCode_Code',\r\n  FencedCode_ClosingFence = 'FencedCode_ClosingFence',\r\n\r\n  HtmlAttribute_Name = 'HtmlAttribute_Name',\r\n  HtmlAttribute_Equals = 'HtmlAttribute_Equals',\r\n  HtmlAttribute_Value = 'HtmlAttribute_Value',\r\n\r\n  HtmlEndTag_OpeningDelimiter = 'HtmlEndTag_OpeningDelimiter',\r\n  HtmlEndTag_Name = 'HtmlEndTag_Name',\r\n  HtmlEndTag_ClosingDelimiter = 'HtmlEndTag_ClosingDelimiter',\r\n\r\n  HtmlStartTag_OpeningDelimiter = 'HtmlStartTag_OpeningDelimiter',\r\n  HtmlStartTag_Name = 'HtmlStartTag_Name',\r\n  HtmlStartTag_ClosingDelimiter = 'HtmlStartTag_ClosingDelimiter',\r\n\r\n  InlineTag_OpeningDelimiter = 'InlineTag_OpeningDelimiter',\r\n  InlineTag_TagName = 'InlineTag_TagName',\r\n  InlineTag_TagContent = 'InlineTag_TagContent',\r\n  InlineTag_ClosingDelimiter = 'InlineTag_ClosingDelimiter',\r\n\r\n  LinkTag_UrlDestination = 'LinkTag_UrlDestination',\r\n  LinkTag_Pipe = 'LinkTag_Pipe',\r\n  LinkTag_LinkText = 'LinkTag_LinkText',\r\n\r\n  MemberIdentifier_LeftQuote = 'MemberIdentifier_LeftQuote',\r\n  MemberIdentifier_Identifier = 'MemberIdentifier_Identifier',\r\n  MemberIdentifier_RightQuote = 'MemberIdentifier_RightQuote',\r\n\r\n  MemberReference_Dot = 'MemberReference_Dot',\r\n  MemberReference_LeftParenthesis = 'MemberReference_LeftParenthesis',\r\n  MemberReference_Colon = 'MemberReference_Colon',\r\n  MemberReference_RightParenthesis = 'MemberReference_RightParenthesis',\r\n\r\n  MemberSelector = 'MemberSelector',\r\n\r\n  DocMemberSymbol_LeftBracket = 'DocMemberSymbol_LeftBracket',\r\n  DocMemberSymbol_RightBracket = 'DocMemberSymbol_RightBracket',\r\n\r\n  ParamBlock_ParameterName = 'ParamBlock_ParameterName',\r\n  ParamBlock_Hyphen = 'ParamBlock_Hyphen',\r\n\r\n  PlainText = 'PlainText',\r\n\r\n  SoftBreak = 'SoftBreak'\r\n}\r\n\r\n/* eslint-enable @typescript-eslint/naming-convention */\r\n\r\n/**\r\n * Constructor parameters for {@link DocExcerpt}.\r\n */\r\nexport interface IDocExcerptParameters extends IDocNodeParameters {\r\n  excerptKind: ExcerptKind;\r\n  content: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a parsed token sequence.\r\n *\r\n * @remarks\r\n * When a `DocNode` is created by parsing a doc comment, it will have `DocExcerpt` child nodes corresponding to\r\n * the parsed syntax elements such as names, keywords, punctuation, and spaces.  These excerpts indicate the original\r\n * coordinates of the syntax element, and thus can be used for syntax highlighting and precise error reporting.\r\n * They could also be used to rewrite specific words in a source file (e.g. renaming a parameter) without disturbing\r\n * any other characters in the file.\r\n *\r\n * Every parsed character will correspond to at most one DocExcerpt object.  In other words, excerpts never overlap.\r\n * A given excerpt can span multiple comment lines, and it may contain gaps, for example to skip the `*` character\r\n * that starts a new TSDoc comment line.\r\n */\r\nexport class DocExcerpt extends DocNode {\r\n  private readonly _excerptKind: ExcerptKind;\r\n  private readonly _content: TokenSequence;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocExcerptParameters) {\r\n    super(parameters);\r\n\r\n    if (parameters.excerptKind === ExcerptKind.Spacing) {\r\n      for (const token of parameters.content!.tokens) {\r\n        switch (token.kind) {\r\n          case TokenKind.Spacing:\r\n          case TokenKind.Newline:\r\n          case TokenKind.EndOfInput:\r\n            break;\r\n          default:\r\n            throw new Error(`The excerptKind=Spacing but the range contains a non-whitespace token`);\r\n        }\r\n      }\r\n    }\r\n\r\n    this._excerptKind = parameters.excerptKind;\r\n    this._content = parameters.content;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.Excerpt;\r\n  }\r\n\r\n  /**\r\n   * Indicates the kind of DocExcerpt.\r\n   */\r\n  public get excerptKind(): ExcerptKind {\r\n    return this._excerptKind;\r\n  }\r\n\r\n  /**\r\n   * The input token sequence corresponding to this excerpt.\r\n   * @remarks\r\n   * Note that a token sequence can span multiple input lines and may contain gaps, for example to skip the `*`\r\n   * character that starts a new TSDoc comment line.\r\n   */\r\n  public get content(): TokenSequence {\r\n    return this._content;\r\n  }\r\n}\r\n"]}