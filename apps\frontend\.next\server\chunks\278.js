"use strict";exports.id=278,exports.ids=[278],exports.modules={9782:(e,s,r)=>{r.d(s,{Header:()=>o});var t=r(13486),a=r(49989),l=r.n(a),c=r(2518),i=r(26973),n=r(58229),x=r(32399),h=r(57469),d=r(60159);function o(){let{user:e,signOut:s}=(0,c.A)(),[r,a]=(0,d.useState)(!1);return(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)(l(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"ش"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"شاريو"})]})}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,t.jsx)(l(),{href:"/features",className:"text-gray-600 hover:text-gray-900",children:"المميزات"}),(0,t.jsx)(l(),{href:"/pricing",className:"text-gray-600 hover:text-gray-900",children:"الأسعار"}),(0,t.jsx)(l(),{href:"/templates",className:"text-gray-600 hover:text-gray-900",children:"القوالب"}),(0,t.jsx)(l(),{href:"/help",className:"text-gray-600 hover:text-gray-900",children:"المساعدة"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[e?(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l(),{href:"/dashboard",children:(0,t.jsxs)(i.$,{variant:"ghost",size:"sm",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 ml-2"}),"لوحة التحكم"]})}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",onClick:s,children:"تسجيل الخروج"})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l(),{href:"/auth/login",children:(0,t.jsx)(i.$,{variant:"ghost",size:"sm",children:"تسجيل الدخول"})}),(0,t.jsx)(l(),{href:"/auth/register",children:(0,t.jsx)(i.$,{size:"sm",children:"إنشاء حساب"})})]}),(0,t.jsx)("button",{className:"md:hidden p-2",onClick:()=>a(!r),children:r?(0,t.jsx)(x.A,{className:"w-6 h-6"}):(0,t.jsx)(h.A,{className:"w-6 h-6"})})]})]}),r&&(0,t.jsx)("div",{className:"md:hidden py-4 border-t",children:(0,t.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,t.jsx)(l(),{href:"/features",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"المميزات"}),(0,t.jsx)(l(),{href:"/pricing",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"الأسعار"}),(0,t.jsx)(l(),{href:"/templates",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"القوالب"}),(0,t.jsx)(l(),{href:"/help",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"المساعدة"})]})})]})})}},43128:(e,s,r)=>{r.d(s,{cn:()=>l});var t=r(56413),a=r(4465);function l(...e){return(0,a.QP)((0,t.$)(e))}},53847:(e,s,r)=>{r.d(s,{$:()=>i});var t=r(38828),a=r(61365),l=r.n(a),c=r(43128);let i=l().forwardRef(({className:e,variant:s="primary",size:r="md",loading:a,children:l,disabled:i,...n},x)=>(0,t.jsxs)("button",{className:(0,c.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[r],e),ref:x,disabled:i||a,...n,children:[a&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]}));i.displayName="Button"},58618:(e,s,r)=>{r.d(s,{w:()=>c});var t=r(38828),a=r(42671),l=r.n(a);function c(){return(0,t.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"ش"})}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"شاريو"})]}),(0,t.jsx)("p",{className:"text-gray-300 mb-4 max-w-md",children:"منصة التجارة الإلكترونية الجزائرية الرائدة. أنشئ متجرك الإلكتروني في دقائق واستفد من جميع الأدوات اللازمة لنجاح تجارتك الإلكترونية."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("a",{href:"#",className:"text-gray-300 hover:text-white",children:[(0,t.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,t.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})]}),(0,t.jsxs)("a",{href:"#",className:"text-gray-300 hover:text-white",children:[(0,t.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,t.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C3.85 14.724 3.85 12.78 5.126 11.504c1.276-1.276 3.22-1.276 4.496 0 1.276 1.276 1.276 3.22 0 4.496-.875.807-2.026 1.297-3.323 1.297z"})})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"روابط سريعة"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/features",className:"text-gray-300 hover:text-white",children:"المميزات"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/pricing",className:"text-gray-300 hover:text-white",children:"الأسعار"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/templates",className:"text-gray-300 hover:text-white",children:"القوالب"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/help",className:"text-gray-300 hover:text-white",children:"المساعدة"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"الدعم"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/contact",className:"text-gray-300 hover:text-white",children:"اتصل بنا"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/docs",className:"text-gray-300 hover:text-white",children:"الوثائق"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/faq",className:"text-gray-300 hover:text-white",children:"الأسئلة الشائعة"})}),(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/privacy",className:"text-gray-300 hover:text-white",children:"سياسة الخصوصية"})})]})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:(0,t.jsx)("p",{className:"text-gray-300",children:"\xa9 2024 شاريو. جميع الحقوق محفوظة."})})]})})}},67756:(e,s,r)=>{r.d(s,{Header:()=>t});let t=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx","Header")}};