exports.id=703,exports.ids=[703],exports.modules={1241:()=>{},2518:(e,s,t)=>{"use strict";t.d(s,{O:()=>x,A:()=>m});var r=t(13486),a=t(60159);let i=(0,t(95681).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),n=async()=>{let{error:e}=await i.auth.signOut();return{error:e}},l=async()=>{let{data:{user:e},error:s}=await i.auth.getUser();return{user:e,error:s}},o=t(93153).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let s=localStorage.getItem("auth_token");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)));let c={validateToken:e=>o.post("/auth/validate",{token:e})};var d=t(85499);let h=(0,a.createContext)(void 0);function x({children:e}){let[s,t]=(0,a.useState)(null),[o,x]=(0,a.useState)(!0),m=async()=>{try{let{user:e}=await l();if(t(e),e){let{data:{session:e}}=await i.auth.getSession();if(e?.access_token)try{let s=await c.validateToken(e.access_token);localStorage.setItem("auth_token",s.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),t(null),localStorage.removeItem("auth_token")}},u=async(e,s)=>{try{let{data:t,error:r}=await i.auth.signInWithPassword({email:e,password:s});if(r)throw r;if(t.session?.access_token){let e=await c.validateToken(t.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الدخول"),e}},v=async(e,s,t)=>{try{let{data:r,error:a}=await i.auth.signUp({email:e,password:s,options:{data:t}});if(a)throw a;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(e){throw d.Ay.error(e.message||"فشل في إنشاء الحساب"),e}},g=async()=>{try{await n(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الخروج"),e}};return(0,r.jsx)(h.Provider,{value:{user:s,loading:o,signIn:u,signUp:v,signOut:g,refreshUser:m},children:e})}function m(){let e=(0,a.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4573:(e,s,t)=>{Promise.resolve().then(t.bind(t,49937))},4946:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,69355,23)),Promise.resolve().then(t.t.bind(t,54439,23)),Promise.resolve().then(t.t.bind(t,67851,23)),Promise.resolve().then(t.t.bind(t,94730,23)),Promise.resolve().then(t.t.bind(t,19774,23)),Promise.resolve().then(t.t.bind(t,53170,23)),Promise.resolve().then(t.t.bind(t,20968,23)),Promise.resolve().then(t.t.bind(t,78298,23))},9699:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(41253);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},9782:(e,s,t)=>{"use strict";t.d(s,{Header:()=>x});var r=t(13486),a=t(49989),i=t.n(a),n=t(2518),l=t(26973),o=t(58229),c=t(32399),d=t(57469),h=t(60159);function x(){let{user:e,signOut:s}=(0,n.A)(),[t,a]=(0,h.useState)(!1);return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"ش"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"شاريو"})]})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,r.jsx)(i(),{href:"/features",className:"text-gray-600 hover:text-gray-900",children:"المميزات"}),(0,r.jsx)(i(),{href:"/pricing",className:"text-gray-600 hover:text-gray-900",children:"الأسعار"}),(0,r.jsx)(i(),{href:"/templates",className:"text-gray-600 hover:text-gray-900",children:"القوالب"}),(0,r.jsx)(i(),{href:"/help",className:"text-gray-600 hover:text-gray-900",children:"المساعدة"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e?(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/dashboard",children:(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 ml-2"}),"لوحة التحكم"]})}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",onClick:s,children:"تسجيل الخروج"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i(),{href:"/auth/login",children:(0,r.jsx)(l.$,{variant:"ghost",size:"sm",children:"تسجيل الدخول"})}),(0,r.jsx)(i(),{href:"/auth/register",children:(0,r.jsx)(l.$,{size:"sm",children:"إنشاء حساب"})})]}),(0,r.jsx)("button",{className:"md:hidden p-2",onClick:()=>a(!t),children:t?(0,r.jsx)(c.A,{className:"w-6 h-6"}):(0,r.jsx)(d.A,{className:"w-6 h-6"})})]})]}),t&&(0,r.jsx)("div",{className:"md:hidden py-4 border-t",children:(0,r.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,r.jsx)(i(),{href:"/features",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"المميزات"}),(0,r.jsx)(i(),{href:"/pricing",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"الأسعار"}),(0,r.jsx)(i(),{href:"/templates",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"القوالب"}),(0,r.jsx)(i(),{href:"/help",className:"text-gray-600 hover:text-gray-900 py-2",onClick:()=>a(!1),children:"المساعدة"})]})})]})})}},14276:()=>{},20685:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>l});var r=t(38828),a=t(93390),i=t.n(a);t(14276);var n=t(52079);let l={title:"Sharyou - منصة التجارة الإلكترونية الجزائرية",description:"أنشئ متجرك الإلكتروني في دقائق مع شاريو - المنصة الجزائرية الرائدة للتجارة الإلكترونية",keywords:"تجارة إلكترونية, متجر إلكتروني, الجزائر, شاريو, بيع أونلاين"};function o({children:e}){return(0,r.jsx)("html",{lang:"ar",dir:"rtl",children:(0,r.jsx)("body",{className:`${i().variable} font-sans antialiased`,children:(0,r.jsx)(n.Providers,{children:e})})})}},26518:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(4627),a=t(55855);function i(...e){return(0,a.QP)((0,r.$)(e))}},26973:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var r=t(13486),a=t(60159),i=t.n(a),n=t(26518);let l=i().forwardRef(({className:e,variant:s="primary",size:t="md",loading:a,children:i,disabled:l,...o},c)=>(0,r.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[t],e),ref:c,disabled:l||a,...o,children:[a&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]}));l.displayName="Button"},43128:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(56413),a=t(4465);function i(...e){return(0,a.QP)((0,r.$)(e))}},49937:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>c});var r=t(13486),a=t(10803),i=t(84364),n=t(85499),l=t(2518),o=t(60159);function c({children:e}){let[s]=(0,o.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,r.jsx)(i.Ht,{client:s,children:(0,r.jsxs)(l.O,{children:[e,(0,r.jsx)(n.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})}},51802:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,30385,23)),Promise.resolve().then(t.t.bind(t,33737,23)),Promise.resolve().then(t.t.bind(t,86081,23)),Promise.resolve().then(t.t.bind(t,1904,23)),Promise.resolve().then(t.t.bind(t,35856,23)),Promise.resolve().then(t.t.bind(t,55492,23)),Promise.resolve().then(t.t.bind(t,89082,23)),Promise.resolve().then(t.t.bind(t,45812,23))},52079:(e,s,t)=>{"use strict";t.d(s,{Providers:()=>r});let r=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx","Providers")},53847:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var r=t(38828),a=t(61365),i=t.n(a),n=t(43128);let l=i().forwardRef(({className:e,variant:s="primary",size:t="md",loading:a,children:i,disabled:l,...o},c)=>(0,r.jsxs)("button",{className:(0,n.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[t],e),ref:c,disabled:l||a,...o,children:[a&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]}));l.displayName="Button"},58618:(e,s,t)=>{"use strict";t.d(s,{w:()=>n});var r=t(38828),a=t(42671),i=t.n(a);function n(){return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"ش"})}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"شاريو"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4 max-w-md",children:"منصة التجارة الإلكترونية الجزائرية الرائدة. أنشئ متجرك الإلكتروني في دقائق واستفد من جميع الأدوات اللازمة لنجاح تجارتك الإلكترونية."}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("a",{href:"#",className:"text-gray-300 hover:text-white",children:[(0,r.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,r.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})]}),(0,r.jsxs)("a",{href:"#",className:"text-gray-300 hover:text-white",children:[(0,r.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,r.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C3.85 14.724 3.85 12.78 5.126 11.504c1.276-1.276 3.22-1.276 4.496 0 1.276 1.276 1.276 3.22 0 4.496-.875.807-2.026 1.297-3.323 1.297z"})})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"روابط سريعة"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/features",className:"text-gray-300 hover:text-white",children:"المميزات"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/pricing",className:"text-gray-300 hover:text-white",children:"الأسعار"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/templates",className:"text-gray-300 hover:text-white",children:"القوالب"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/help",className:"text-gray-300 hover:text-white",children:"المساعدة"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"الدعم"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/contact",className:"text-gray-300 hover:text-white",children:"اتصل بنا"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/docs",className:"text-gray-300 hover:text-white",children:"الوثائق"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/faq",className:"text-gray-300 hover:text-white",children:"الأسئلة الشائعة"})}),(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:"/privacy",className:"text-gray-300 hover:text-white",children:"سياسة الخصوصية"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:(0,r.jsx)("p",{className:"text-gray-300",children:"\xa9 2024 شاريو. جميع الحقوق محفوظة."})})]})})}},67756:(e,s,t)=>{"use strict";t.d(s,{Header:()=>r});let r=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx","Header")},72088:()=>{},76029:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=76029,e.exports=s},86013:(e,s,t)=>{Promise.resolve().then(t.bind(t,52079))}};