{"version": 3, "file": "DocCodeSpan.js", "sourceRoot": "", "sources": ["../../src/nodes/DocCodeSpan.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAA2B,OAAO,EAAiC,MAAM,WAAW,CAAC;AAEzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAoBvD;;;GAGG;AACH;IAAiC,+BAAO;IAWtC;;;OAGG;IACH,qBAAmB,UAAiE;QAClF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,yBAAyB;gBAClD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;YACH,KAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,aAAa;gBACtC,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;YACH,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,yBAAyB;gBAClD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;QAC/B,CAAC;;IACH,CAAC;IAGD,sBAAW,6BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,QAAQ,CAAC;QAC9B,CAAC;;;OAAA;IAKD,sBAAW,6BAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrD,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAED,gBAAgB;IACN,qCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC3F,CAAC;IACH,kBAAC;AAAD,CAAC,AA1DD,CAAiC,OAAO,GA0DvC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, type IDocNodeParameters, DocNode, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocCodeSpan}.\r\n */\r\nexport interface IDocCodeSpanParameters extends IDocNodeParameters {\r\n  code: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocCodeSpan}.\r\n */\r\nexport interface IDocCodeSpanParsedParameters extends IDocNodeParsedParameters {\r\n  openingDelimiterExcerpt: TokenSequence;\r\n\r\n  codeExcerpt: TokenSequence;\r\n\r\n  closingDelimiterExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents CommonMark-style code span, i.e. code surrounded by\r\n * backtick characters.\r\n */\r\nexport class DocCodeSpan extends DocNode {\r\n  // The opening ` delimiter\r\n  private readonly _openingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  // The code content\r\n  private _code: string | undefined;\r\n  private readonly _codeExcerpt: DocExcerpt | undefined;\r\n\r\n  // The closing ` delimiter\r\n  private readonly _closingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocCodeSpanParameters | IDocCodeSpanParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._openingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.CodeSpan_OpeningDelimiter,\r\n        content: parameters.openingDelimiterExcerpt\r\n      });\r\n      this._codeExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.CodeSpan_Code,\r\n        content: parameters.codeExcerpt\r\n      });\r\n      this._closingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.CodeSpan_ClosingDelimiter,\r\n        content: parameters.closingDelimiterExcerpt\r\n      });\r\n    } else {\r\n      this._code = parameters.code;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.CodeSpan;\r\n  }\r\n\r\n  /**\r\n   * The text that should be rendered as code, excluding the backtick delimiters.\r\n   */\r\n  public get code(): string {\r\n    if (this._code === undefined) {\r\n      this._code = this._codeExcerpt!.content.toString();\r\n    }\r\n    return this._code;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._openingDelimiterExcerpt, this._codeExcerpt, this._closingDelimiterExcerpt];\r\n  }\r\n}\r\n"]}