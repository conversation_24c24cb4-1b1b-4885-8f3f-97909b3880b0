(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{765:(e,t,r)=>{Promise.resolve().then(r.bind(r,3963))},1053:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(4568),a=r(7620),l=r(3928);let i=a.forwardRef((e,t)=>{let{className:r,label:a,error:i,helperText:n,type:o,...c}=e;return(0,s.jsxs)("div",{className:"space-y-1",children:[a&&(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:a}),(0,s.jsx)("input",{type:o,className:(0,l.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i&&"border-red-500 focus-visible:ring-red-500",r),ref:t,...c}),i&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:i}),n&&!i&&(0,s.jsx)("p",{className:"text-sm text-gray-500",children:n})]})});i.displayName="Input"},2921:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=r(4568),a=r(7620),l=r(3928);let i=a.forwardRef((e,t)=>{let{className:r,children:a,...i}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm",r),...i,children:a})});i.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...a})});n.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-gray-500",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},2942:(e,t,r)=>{"use strict";var s=r(2418);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},3928:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var s=r(2987),a=r(607);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},3963:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(4568),a=r(7620),l=r(7261),i=r.n(l),n=r(2942),o=r(9344),c=r(9080),d=r(1053),u=r(2921);function m(){let[e,t]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[m,h]=(0,a.useState)(!1),[x,f]=(0,a.useState)(""),{signIn:p}=(0,o.A)(),g=(0,n.useRouter)(),b=async t=>{t.preventDefault(),h(!0),f("");try{await p(e,r),g.push("/dashboard")}catch(e){f(e instanceof Error?e.message:"فشل في تسجيل الدخول")}finally{h(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(i(),{href:"/",className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-2xl",children:"ش"})}),(0,s.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"شاريو"})]})}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsxs)(u.aR,{className:"text-center",children:[(0,s.jsx)(u.ZB,{className:"text-2xl font-bold",children:"تسجيل الدخول"}),(0,s.jsx)(u.BT,{children:"ادخل إلى حسابك للوصول إلى لوحة التحكم"})]}),(0,s.jsxs)(u.Wu,{children:[(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[x&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:x}),(0,s.jsx)(d.p,{label:"البريد الإلكتروني",type:"email",value:e,onChange:e=>t(e.target.value),required:!0,placeholder:"<EMAIL>"}),(0,s.jsx)(d.p,{label:"كلمة المرور",type:"password",value:r,onChange:e=>l(e.target.value),required:!0,placeholder:"••••••••"}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"mr-2 block text-sm text-gray-900",children:"تذكرني"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(i(),{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"نسيت كلمة المرور؟"})})]}),(0,s.jsx)(c.$,{type:"submit",loading:m,className:"w-full",size:"lg",children:"تسجيل الدخول"})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["ليس لديك حساب؟"," ",(0,s.jsx)(i(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"إنشاء حساب جديد"})]})})]})]})]})]})})}},9080:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var s=r(4568),a=r(7620),l=r(3928);let i=a.forwardRef((e,t)=>{let{className:r,variant:a="primary",size:i="md",loading:n,children:o,disabled:c,...d}=e;return(0,s.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[a],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[i],r),ref:t,disabled:c||n,...d,children:[n&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});i.displayName="Button"},9344:(e,t,r)=>{"use strict";r.d(t,{O:()=>m,A:()=>h});var s=r(4568),a=r(7620);let l=(0,r(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),i=async()=>{let{error:e}=await l.auth.signOut();return{error:e}},n=async()=>{let{data:{user:e},error:t}=await l.auth.getUser();return{user:e,error:t}},o=r(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let c={validateToken:e=>o.post("/auth/validate",{token:e})};var d=r(5317);let u=(0,a.createContext)(void 0);function m(e){let{children:t}=e,[r,o]=(0,a.useState)(null),[m,h]=(0,a.useState)(!0),x=async()=>{try{let{user:e}=await n();if(o(e),e){let{data:{session:e}}=await l.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await c.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),o(null),localStorage.removeItem("auth_token")}};(0,a.useEffect)(()=>{x().finally(()=>h(!1));let{data:{subscription:e}}=l.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){o(t.user);try{let e=await c.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(o(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let f=async(e,t)=>{try{var r;let{data:s,error:a}=await l.auth.signInWithPassword({email:e,password:t});if(a)throw a;if(null==(r=s.session)?void 0:r.access_token){let e=await c.validateToken(s.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الدخول";throw d.Ay.error(e),t}},p=async(e,t,r)=>{try{let{error:s}=await l.auth.signUp({email:e,password:t,options:{data:r}});if(s)throw s;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(t){let e=t instanceof Error?t.message:"فشل في إنشاء الحساب";throw d.Ay.error(e),t}},g=async()=>{try{await i(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الخروج";throw d.Ay.error(e),t}};return(0,s.jsx)(u.Provider,{value:{user:r,loading:m,signIn:f,signUp:p,signOut:g,refreshUser:x},children:t})}function h(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[286,661,587,315,358],()=>t(765)),_N_E=e.O()}]);