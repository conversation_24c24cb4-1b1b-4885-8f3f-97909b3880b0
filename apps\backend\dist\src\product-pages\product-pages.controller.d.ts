import { ProductPagesService } from './product-pages.service';
import { CreateProductPageDto } from './dto/create-product-page.dto';
import { UpdateProductPageDto } from './dto/update-product-page.dto';
export declare class ProductPagesController {
    private readonly productPagesService;
    constructor(productPagesService: ProductPagesService);
    create(req: any, createProductPageDto: CreateProductPageDto): Promise<any>;
    findAll(): Promise<any>;
    findBySlug(slug: string): Promise<any>;
    findByDomain(domain: string): Promise<any>;
    findMyPages(req: any): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, req: any, updateProductPageDto: UpdateProductPageDto): Promise<any>;
    remove(id: string, req: any): Promise<any>;
}
