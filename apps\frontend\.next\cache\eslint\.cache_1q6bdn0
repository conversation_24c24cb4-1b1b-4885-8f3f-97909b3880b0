[{"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\register\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\verify-email\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Footer.tsx": "9", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx": "10", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx": "11", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Button.tsx": "12", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Card.tsx": "13", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\DemoBanner.tsx": "14", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Input.tsx": "15", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\contexts\\AuthContext.tsx": "16", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\api.ts": "17", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\supabase.ts": "18", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\utils.ts": "19"}, {"size": 4776, "mtime": 1749856068845, "results": "20", "hashOfConfig": "21"}, {"size": 6992, "mtime": 1749856102980, "results": "22", "hashOfConfig": "21"}, {"size": 2811, "mtime": 1749856120865, "results": "23", "hashOfConfig": "21"}, {"size": 3977, "mtime": 1749856141802, "results": "24", "hashOfConfig": "21"}, {"size": 7840, "mtime": 1749856177537, "results": "25", "hashOfConfig": "21"}, {"size": 975, "mtime": 1749858310355, "results": "26", "hashOfConfig": "21"}, {"size": 6497, "mtime": 1749854767705, "results": "27", "hashOfConfig": "21"}, {"size": 10156, "mtime": 1749856883861, "results": "28", "hashOfConfig": "21"}, {"size": 4360, "mtime": 1749854662636, "results": "29", "hashOfConfig": "21"}, {"size": 4401, "mtime": 1749854638844, "results": "30", "hashOfConfig": "21"}, {"size": 1263, "mtime": 1749854554278, "results": "31", "hashOfConfig": "21"}, {"size": 2160, "mtime": 1749854572867, "results": "32", "hashOfConfig": "21"}, {"size": 2013, "mtime": 1749854599131, "results": "33", "hashOfConfig": "21"}, {"size": 2219, "mtime": 1749856897360, "results": "34", "hashOfConfig": "21"}, {"size": 1361, "mtime": 1749854585608, "results": "35", "hashOfConfig": "21"}, {"size": 4425, "mtime": 1749858284524, "results": "36", "hashOfConfig": "21"}, {"size": 3401, "mtime": 1749910479792, "results": "37", "hashOfConfig": "21"}, {"size": 1094, "mtime": 1749854498075, "results": "38", "hashOfConfig": "21"}, {"size": 1202, "mtime": 1749854516716, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1tjd44v", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx", ["97"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\register\\page.tsx", ["98"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\verify-email\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx", ["99", "100", "101"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx", ["102"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\DemoBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\contexts\\AuthContext.tsx", ["103", "104", "105", "106", "107", "108"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\api.ts", ["109", "110", "111", "112", "113", "114", "115"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\supabase.ts", ["116"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\utils.ts", [], [], {"ruleId": "117", "severity": 2, "message": "118", "line": 28, "column": 21, "nodeType": "119", "messageId": "120", "endLine": 28, "endColumn": 24, "suggestions": "121"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 58, "column": 21, "nodeType": "119", "messageId": "120", "endLine": 58, "endColumn": 24, "suggestions": "122"}, {"ruleId": "123", "severity": 2, "message": "124", "line": 8, "column": 3, "nodeType": null, "messageId": "125", "endLine": 8, "endColumn": 18}, {"ruleId": "126", "severity": 2, "message": "127", "line": 85, "column": 67, "nodeType": "128", "messageId": "129", "suggestions": "130"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 85, "column": 75, "nodeType": "128", "messageId": "129", "suggestions": "131"}, {"ruleId": "123", "severity": 2, "message": "132", "line": 8, "column": 3, "nodeType": null, "messageId": "125", "endLine": 8, "endColumn": 18}, {"ruleId": "117", "severity": 2, "message": "118", "line": 13, "column": 56, "nodeType": "119", "messageId": "120", "endLine": 13, "endColumn": 59, "suggestions": "133"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 91, "column": 21, "nodeType": "119", "messageId": "120", "endLine": 91, "endColumn": 24, "suggestions": "134"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 97, "column": 69, "nodeType": "119", "messageId": "120", "endLine": 97, "endColumn": 72, "suggestions": "135"}, {"ruleId": "123", "severity": 2, "message": "136", "line": 99, "column": 15, "nodeType": null, "messageId": "125", "endLine": 99, "endColumn": 19}, {"ruleId": "117", "severity": 2, "message": "118", "line": 110, "column": 21, "nodeType": "119", "messageId": "120", "endLine": 110, "endColumn": 24, "suggestions": "137"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 121, "column": 21, "nodeType": "119", "messageId": "120", "endLine": 121, "endColumn": 24, "suggestions": "138"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 46, "column": 25, "nodeType": "119", "messageId": "120", "endLine": 46, "endColumn": 28, "suggestions": "139"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 51, "column": 18, "nodeType": "119", "messageId": "120", "endLine": 51, "endColumn": 21, "suggestions": "140"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 57, "column": 30, "nodeType": "119", "messageId": "120", "endLine": 57, "endColumn": 33, "suggestions": "141"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 62, "column": 18, "nodeType": "119", "messageId": "120", "endLine": 62, "endColumn": 21, "suggestions": "142"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 69, "column": 30, "nodeType": "119", "messageId": "120", "endLine": 69, "endColumn": 33, "suggestions": "143"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 74, "column": 18, "nodeType": "119", "messageId": "120", "endLine": 74, "endColumn": 21, "suggestions": "144"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 79, "column": 30, "nodeType": "119", "messageId": "120", "endLine": 79, "endColumn": 33, "suggestions": "145"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 8, "column": 74, "nodeType": "119", "messageId": "120", "endLine": 8, "endColumn": 77, "suggestions": "146"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["147", "148"], ["149", "150"], "@typescript-eslint/no-unused-vars", "'CodeBracketIcon' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["151", "152", "153", "154"], ["155", "156", "157", "158"], "'ShoppingBagIcon' is defined but never used.", ["159", "160"], ["161", "162"], ["163", "164"], "'data' is assigned a value but never used.", ["165", "166"], ["167", "168"], ["169", "170"], ["171", "172"], ["173", "174"], ["175", "176"], ["177", "178"], ["179", "180"], ["181", "182"], ["183", "184"], {"messageId": "185", "fix": "186", "desc": "187"}, {"messageId": "188", "fix": "189", "desc": "190"}, {"messageId": "185", "fix": "191", "desc": "187"}, {"messageId": "188", "fix": "192", "desc": "190"}, {"messageId": "193", "data": "194", "fix": "195", "desc": "196"}, {"messageId": "193", "data": "197", "fix": "198", "desc": "199"}, {"messageId": "193", "data": "200", "fix": "201", "desc": "202"}, {"messageId": "193", "data": "203", "fix": "204", "desc": "205"}, {"messageId": "193", "data": "206", "fix": "207", "desc": "196"}, {"messageId": "193", "data": "208", "fix": "209", "desc": "199"}, {"messageId": "193", "data": "210", "fix": "211", "desc": "202"}, {"messageId": "193", "data": "212", "fix": "213", "desc": "205"}, {"messageId": "185", "fix": "214", "desc": "187"}, {"messageId": "188", "fix": "215", "desc": "190"}, {"messageId": "185", "fix": "216", "desc": "187"}, {"messageId": "188", "fix": "217", "desc": "190"}, {"messageId": "185", "fix": "218", "desc": "187"}, {"messageId": "188", "fix": "219", "desc": "190"}, {"messageId": "185", "fix": "220", "desc": "187"}, {"messageId": "188", "fix": "221", "desc": "190"}, {"messageId": "185", "fix": "222", "desc": "187"}, {"messageId": "188", "fix": "223", "desc": "190"}, {"messageId": "185", "fix": "224", "desc": "187"}, {"messageId": "188", "fix": "225", "desc": "190"}, {"messageId": "185", "fix": "226", "desc": "187"}, {"messageId": "188", "fix": "227", "desc": "190"}, {"messageId": "185", "fix": "228", "desc": "187"}, {"messageId": "188", "fix": "229", "desc": "190"}, {"messageId": "185", "fix": "230", "desc": "187"}, {"messageId": "188", "fix": "231", "desc": "190"}, {"messageId": "185", "fix": "232", "desc": "187"}, {"messageId": "188", "fix": "233", "desc": "190"}, {"messageId": "185", "fix": "234", "desc": "187"}, {"messageId": "188", "fix": "235", "desc": "190"}, {"messageId": "185", "fix": "236", "desc": "187"}, {"messageId": "188", "fix": "237", "desc": "190"}, {"messageId": "185", "fix": "238", "desc": "187"}, {"messageId": "188", "fix": "239", "desc": "190"}, "suggestUnknown", {"range": "240", "text": "241"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "242", "text": "243"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "244", "text": "241"}, {"range": "245", "text": "243"}, "replaceWithAlt", {"alt": "246"}, {"range": "247", "text": "248"}, "Replace with `&quot;`.", {"alt": "249"}, {"range": "250", "text": "251"}, "Replace with `&ldquo;`.", {"alt": "252"}, {"range": "253", "text": "254"}, "Replace with `&#34;`.", {"alt": "255"}, {"range": "256", "text": "257"}, "Replace with `&rdquo;`.", {"alt": "246"}, {"range": "258", "text": "259"}, {"alt": "249"}, {"range": "260", "text": "261"}, {"alt": "252"}, {"range": "262", "text": "263"}, {"alt": "255"}, {"range": "264", "text": "265"}, {"range": "266", "text": "241"}, {"range": "267", "text": "243"}, {"range": "268", "text": "241"}, {"range": "269", "text": "243"}, {"range": "270", "text": "241"}, {"range": "271", "text": "243"}, {"range": "272", "text": "241"}, {"range": "273", "text": "243"}, {"range": "274", "text": "241"}, {"range": "275", "text": "243"}, {"range": "276", "text": "241"}, {"range": "277", "text": "243"}, {"range": "278", "text": "241"}, {"range": "279", "text": "243"}, {"range": "280", "text": "241"}, {"range": "281", "text": "243"}, {"range": "282", "text": "241"}, {"range": "283", "text": "243"}, {"range": "284", "text": "241"}, {"range": "285", "text": "243"}, {"range": "286", "text": "241"}, {"range": "287", "text": "243"}, {"range": "288", "text": "241"}, {"range": "289", "text": "243"}, {"range": "290", "text": "241"}, {"range": "291", "text": "243"}, [872, 875], "unknown", [872, 875], "never", [1579, 1582], [1579, 1582], "&quot;", [3415, 3455], "اختر اسم &quot;<PERSON><PERSON><PERSON><PERSON>\" ومنطقة أوروبا الغربية", "&ldquo;", [3415, 3455], "اختر اسم &ldquo;Sharyou\" ومنطقة أوروبا الغربية", "&#34;", [3415, 3455], "ا<PERSON><PERSON>ر اسم &#34;<PERSON><PERSON><PERSON><PERSON>\" ومنطقة أوروبا الغربية", "&rdquo;", [3415, 3455], "اختر اسم &rdquo;Sharyou\" ومنطقة أوروبا الغربية", [3415, 3455], "اختر اسم \"Sharyou&quot; ومنطقة أوروبا الغربية", [3415, 3455], "اختر اسم \"<PERSON><PERSON><PERSON><PERSON>&ldquo; ومنطقة أوروبا الغربية", [3415, 3455], "اختر اسم \"Sharyou&#34; ومنطقة أوروبا الغربية", [3415, 3455], "اختر اسم \"<PERSON><PERSON>yo<PERSON>&rdquo; ومنطقة أوروبا الغربية", [488, 491], [488, 491], [3044, 3047], [3044, 3047], [3209, 3212], [3209, 3212], [3518, 3521], [3518, 3521], [3799, 3802], [3799, 3802], [1093, 1096], [1093, 1096], [1246, 1249], [1246, 1249], [1639, 1642], [1639, 1642], [1800, 1803], [1800, 1803], [2294, 2297], [2294, 2297], [2443, 2446], [2443, 2446], [2800, 2803], [2800, 2803], [331, 334], [331, 334]]