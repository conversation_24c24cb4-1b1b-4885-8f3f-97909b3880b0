[{"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\register\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\verify-email\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Footer.tsx": "9", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx": "10", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx": "11", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Button.tsx": "12", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Card.tsx": "13", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\DemoBanner.tsx": "14", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Input.tsx": "15", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\contexts\\AuthContext.tsx": "16", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\api.ts": "17", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\supabase.ts": "18", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\utils.ts": "19"}, {"size": 4845, "mtime": 1749911530807, "results": "20", "hashOfConfig": "21"}, {"size": 7061, "mtime": 1749911554399, "results": "22", "hashOfConfig": "21"}, {"size": 2811, "mtime": 1749856120865, "results": "23", "hashOfConfig": "21"}, {"size": 3977, "mtime": 1749856141802, "results": "24", "hashOfConfig": "21"}, {"size": 7840, "mtime": 1749856177537, "results": "25", "hashOfConfig": "21"}, {"size": 975, "mtime": 1749858310355, "results": "26", "hashOfConfig": "21"}, {"size": 6497, "mtime": 1749854767705, "results": "27", "hashOfConfig": "21"}, {"size": 10136, "mtime": 1749911357844, "results": "28", "hashOfConfig": "21"}, {"size": 4360, "mtime": 1749854662636, "results": "29", "hashOfConfig": "21"}, {"size": 4378, "mtime": 1749911392748, "results": "30", "hashOfConfig": "21"}, {"size": 1263, "mtime": 1749854554278, "results": "31", "hashOfConfig": "21"}, {"size": 2160, "mtime": 1749854572867, "results": "32", "hashOfConfig": "21"}, {"size": 2013, "mtime": 1749854599131, "results": "33", "hashOfConfig": "21"}, {"size": 2219, "mtime": 1749856897360, "results": "34", "hashOfConfig": "21"}, {"size": 1361, "mtime": 1749854585608, "results": "35", "hashOfConfig": "21"}, {"size": 4597, "mtime": 1749911510724, "results": "36", "hashOfConfig": "21"}, {"size": 3539, "mtime": 1749911735160, "results": "37", "hashOfConfig": "21"}, {"size": 1114, "mtime": 1749911794198, "results": "38", "hashOfConfig": "21"}, {"size": 1202, "mtime": 1749854516716, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tjd44v", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\verify-email\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx", ["97", "98"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\DemoBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\contexts\\AuthContext.tsx", ["99"], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\utils.ts", [], [], {"ruleId": "100", "severity": 2, "message": "101", "line": 84, "column": 67, "nodeType": "102", "messageId": "103", "suggestions": "104"}, {"ruleId": "100", "severity": 2, "message": "101", "line": 84, "column": 75, "nodeType": "102", "messageId": "103", "suggestions": "105"}, {"ruleId": "106", "severity": 2, "message": "107", "line": 91, "column": 21, "nodeType": "108", "messageId": "109", "endLine": 91, "endColumn": 24, "suggestions": "110"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["111", "112", "113", "114"], ["115", "116", "117", "118"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["119", "120"], {"messageId": "121", "data": "122", "fix": "123", "desc": "124"}, {"messageId": "121", "data": "125", "fix": "126", "desc": "127"}, {"messageId": "121", "data": "128", "fix": "129", "desc": "130"}, {"messageId": "121", "data": "131", "fix": "132", "desc": "133"}, {"messageId": "121", "data": "134", "fix": "135", "desc": "124"}, {"messageId": "121", "data": "136", "fix": "137", "desc": "127"}, {"messageId": "121", "data": "138", "fix": "139", "desc": "130"}, {"messageId": "121", "data": "140", "fix": "141", "desc": "133"}, {"messageId": "142", "fix": "143", "desc": "144"}, {"messageId": "145", "fix": "146", "desc": "147"}, "replaceWithAlt", {"alt": "148"}, {"range": "149", "text": "150"}, "Replace with `&quot;`.", {"alt": "151"}, {"range": "152", "text": "153"}, "Replace with `&ldquo;`.", {"alt": "154"}, {"range": "155", "text": "156"}, "Replace with `&#34;`.", {"alt": "157"}, {"range": "158", "text": "159"}, "Replace with `&rdquo;`.", {"alt": "148"}, {"range": "160", "text": "161"}, {"alt": "151"}, {"range": "162", "text": "163"}, {"alt": "154"}, {"range": "164", "text": "165"}, {"alt": "157"}, {"range": "166", "text": "167"}, "suggestUnknown", {"range": "168", "text": "169"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "170", "text": "171"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "&quot;", [3395, 3435], "اختر اسم &quot;<PERSON><PERSON><PERSON><PERSON>\" ومنطقة أوروبا الغربية", "&ldquo;", [3395, 3435], "اختر اسم &ldquo;Sharyou\" ومنطقة أوروبا الغربية", "&#34;", [3395, 3435], "ا<PERSON><PERSON>ر اسم &#34;<PERSON><PERSON><PERSON><PERSON>\" ومنطقة أوروبا الغربية", "&rdquo;", [3395, 3435], "اختر اسم &rdquo;Sharyou\" ومنطقة أوروبا الغربية", [3395, 3435], "اختر اسم \"Sharyou&quot; ومنطقة أوروبا الغربية", [3395, 3435], "اختر اسم \"<PERSON><PERSON><PERSON><PERSON>&ldquo; ومنطقة أوروبا الغربية", [3395, 3435], "اختر اسم \"Sharyou&#34; ومنطقة أوروبا الغربية", [3395, 3435], "اختر اسم \"<PERSON><PERSON>yo<PERSON>&rdquo; ومنطقة أوروبا الغربية", [3064, 3067], "unknown", [3064, 3067], "never"]