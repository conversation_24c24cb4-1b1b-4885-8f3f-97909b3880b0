[{"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\register\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\verify-email\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Footer.tsx": "9", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx": "10", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx": "11", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Button.tsx": "12", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Card.tsx": "13", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\DemoBanner.tsx": "14", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Input.tsx": "15", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\contexts\\AuthContext.tsx": "16", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\api.ts": "17", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\supabase.ts": "18", "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\utils.ts": "19"}, {"size": 4845, "mtime": 1749911530807, "results": "20", "hashOfConfig": "21"}, {"size": 7061, "mtime": 1749911554399, "results": "22", "hashOfConfig": "21"}, {"size": 2811, "mtime": 1749856120865, "results": "23", "hashOfConfig": "21"}, {"size": 3977, "mtime": 1749856141802, "results": "24", "hashOfConfig": "21"}, {"size": 7840, "mtime": 1749856177537, "results": "25", "hashOfConfig": "21"}, {"size": 975, "mtime": 1749858310355, "results": "26", "hashOfConfig": "21"}, {"size": 6497, "mtime": 1749854767705, "results": "27", "hashOfConfig": "21"}, {"size": 10146, "mtime": 1749912041862, "results": "28", "hashOfConfig": "21"}, {"size": 4360, "mtime": 1749854662636, "results": "29", "hashOfConfig": "21"}, {"size": 4378, "mtime": 1749911392748, "results": "30", "hashOfConfig": "21"}, {"size": 1263, "mtime": 1749854554278, "results": "31", "hashOfConfig": "21"}, {"size": 2160, "mtime": 1749854572867, "results": "32", "hashOfConfig": "21"}, {"size": 2013, "mtime": 1749854599131, "results": "33", "hashOfConfig": "21"}, {"size": 2219, "mtime": 1749856897360, "results": "34", "hashOfConfig": "21"}, {"size": 1361, "mtime": 1749854585608, "results": "35", "hashOfConfig": "21"}, {"size": 4666, "mtime": 1749912085617, "results": "36", "hashOfConfig": "21"}, {"size": 3539, "mtime": 1749911735160, "results": "37", "hashOfConfig": "21"}, {"size": 1114, "mtime": 1749911794198, "results": "38", "hashOfConfig": "21"}, {"size": 1202, "mtime": 1749854516716, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tjd44v", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\verify-email\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\setup\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\DemoBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\lib\\utils.ts", [], []]