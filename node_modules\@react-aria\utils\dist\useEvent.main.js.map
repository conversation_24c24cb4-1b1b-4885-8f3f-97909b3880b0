{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAMM,SAAS,0CACd,GAAkC,EAClC,KAAwB,EACxB,OAAqE,EACrE,OAA2C;IAE3C,IAAI,cAAc,CAAA,GAAA,wCAAa,EAAE;IACjC,IAAI,aAAa,WAAW;IAE5B,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,cAAc,CAAC,IAAI,OAAO,EAC5B;QAGF,IAAI,UAAU,IAAI,OAAO;QACzB,QAAQ,gBAAgB,CAAC,OAAO,aAA8B;QAC9D,OAAO;YACL,QAAQ,mBAAmB,CAAC,OAAO,aAA8B;QACnE;IACF,GAAG;QAAC;QAAK;QAAO;QAAS;QAAY;KAAY;AACnD", "sources": ["packages/@react-aria/utils/src/useEvent.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useEvent<K extends keyof GlobalEventHandlersEventMap>(\n  ref: RefObject<EventTarget | null>,\n  event: K | (string & {}),\n  handler?: (this: Document, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n): void {\n  let handleEvent = useEffectEvent(handler);\n  let isDisabled = handler == null;\n\n  useEffect(() => {\n    if (isDisabled || !ref.current) {\n      return;\n    }\n\n    let element = ref.current;\n    element.addEventListener(event, handleEvent as EventListener, options);\n    return () => {\n      element.removeEventListener(event, handleEvent as EventListener, options);\n    };\n  }, [ref, event, options, isDisabled, handleEvent]);\n}\n"], "names": [], "version": 3, "file": "useEvent.main.js.map"}