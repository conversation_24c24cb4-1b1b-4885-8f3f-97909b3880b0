{"version": 3, "file": "DocHtmlStartTag.js", "sourceRoot": "", "sources": ["../../src/nodes/DocHtmlStartTag.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,OAAO,EAAE,WAAW,EAA0D,MAAM,WAAW,CAAC;AAGzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AA4BxD;;;;GAIG;AACH;IAAqC,mCAAO;IAkB1C;;;OAGG;IACH,yBAAmB,UAAyE;;QAC1F,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,6BAA6B;gBACtD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;YAEH,KAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,iBAAiB;gBAC1C,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBACvC,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;oBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,uBAAuB;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,6BAA6B;gBACtD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;YAC7B,KAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,CAAC;QACvD,CAAC;QAED,KAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;YAC9B,CAAA,KAAA,KAAI,CAAC,eAAe,CAAA,CAAC,IAAI,WAAI,UAAU,CAAC,cAAc,EAAE;QAC1D,CAAC;QAED,KAAI,CAAC,eAAe,GAAG,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC;;IACrD,CAAC;IAGD,sBAAW,iCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,YAAY,CAAC;QAClC,CAAC;;;OAAA;IAKD,sBAAW,iCAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrD,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAKD,sBAAW,2CAAc;QAHzB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;;;OAAA;IAKD,sBAAW,2CAAc;QAHzB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;;;OAAA;IAMD,sBAAW,6CAAgB;QAJ3B;;;WAGG;aACH;YACE,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBACzC,IAAI,IAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;oBAChD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC5E,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,CAAC;;;OAAA;IAED;;OAEG;IACI,oCAAU,GAAjB;QACE,qGAAqG;QACrG,IAAM,aAAa,GAAkB,IAAI,aAAa,EAAE,CAAC;QACzD,IAAM,OAAO,GAAiB,IAAI,YAAY,EAAE,CAAC;QACjD,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,aAAa,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,gBAAgB;IACN,yCAAe,GAAzB;QACE;YACE,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,wBAAwB;WAC1B,IAAI,CAAC,eAAe;YACvB,IAAI,CAAC,wBAAwB;kBAC7B;IACJ,CAAC;IACH,sBAAC;AAAD,CAAC,AA/HD,CAAqC,OAAO,GA+H3C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNode, DocNodeKind, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { DocHtmlAttribute } from './DocHtmlAttribute';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport { StringBuilder } from '../emitters/StringBuilder';\r\nimport { TSDocEmitter } from '../emitters/TSDocEmitter';\r\n\r\n/**\r\n * Constructor parameters for {@link DocHtmlStartTag}.\r\n */\r\nexport interface IDocHtmlStartTagParameters extends IDocNodeParameters {\r\n  name: string;\r\n  spacingAfterName?: string;\r\n\r\n  htmlAttributes?: DocHtmlAttribute[];\r\n  selfClosingTag?: boolean;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocHtmlStartTag}.\r\n */\r\nexport interface IDocHtmlStartTagParsedParameters extends IDocNodeParsedParameters {\r\n  openingDelimiterExcerpt: TokenSequence;\r\n\r\n  nameExcerpt: TokenSequence;\r\n  spacingAfterNameExcerpt?: TokenSequence;\r\n\r\n  htmlAttributes: DocHtmlAttribute[];\r\n  selfClosingTag: boolean;\r\n\r\n  closingDelimiterExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents an HTML start tag, which may or may not be self-closing.\r\n *\r\n * Example: `<a href=\"#\" />`\r\n */\r\nexport class DocHtmlStartTag extends DocNode {\r\n  // The \"<\" delimiter\r\n  private readonly _openingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  // The element name\r\n  private _name: string | undefined;\r\n  private readonly _nameExcerpt: DocExcerpt | undefined;\r\n\r\n  private _spacingAfterName: string | undefined;\r\n  private readonly _spacingAfterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _htmlAttributes: DocHtmlAttribute[];\r\n\r\n  private readonly _selfClosingTag: boolean;\r\n\r\n  // The \">\" or \"/>\" delimiter\r\n  private readonly _closingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocHtmlStartTagParameters | IDocHtmlStartTagParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._openingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlStartTag_OpeningDelimiter,\r\n        content: parameters.openingDelimiterExcerpt\r\n      });\r\n\r\n      this._nameExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlStartTag_Name,\r\n        content: parameters.nameExcerpt\r\n      });\r\n      if (parameters.spacingAfterNameExcerpt) {\r\n        this._spacingAfterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterNameExcerpt\r\n        });\r\n      }\r\n\r\n      this._closingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlStartTag_ClosingDelimiter,\r\n        content: parameters.closingDelimiterExcerpt\r\n      });\r\n    } else {\r\n      this._name = parameters.name;\r\n      this._spacingAfterName = parameters.spacingAfterName;\r\n    }\r\n\r\n    this._htmlAttributes = [];\r\n    if (parameters.htmlAttributes) {\r\n      this._htmlAttributes.push(...parameters.htmlAttributes);\r\n    }\r\n\r\n    this._selfClosingTag = !!parameters.selfClosingTag;\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.HtmlStartTag;\r\n  }\r\n\r\n  /**\r\n   * The HTML element name.\r\n   */\r\n  public get name(): string {\r\n    if (this._name === undefined) {\r\n      this._name = this._nameExcerpt!.content.toString();\r\n    }\r\n    return this._name;\r\n  }\r\n\r\n  /**\r\n   * The HTML attributes belonging to this HTML element.\r\n   */\r\n  public get htmlAttributes(): ReadonlyArray<DocHtmlAttribute> {\r\n    return this._htmlAttributes;\r\n  }\r\n\r\n  /**\r\n   * If true, then the HTML tag ends with `/>` instead of `>`.\r\n   */\r\n  public get selfClosingTag(): boolean {\r\n    return this._selfClosingTag;\r\n  }\r\n\r\n  /**\r\n   * Explicit whitespace that a renderer should insert after the HTML element name.\r\n   * If undefined, then the renderer can use a formatting rule to generate appropriate spacing.\r\n   */\r\n  public get spacingAfterName(): string | undefined {\r\n    if (this._spacingAfterName === undefined) {\r\n      if (this._spacingAfterNameExcerpt !== undefined) {\r\n        this._spacingAfterName = this._spacingAfterNameExcerpt.content.toString();\r\n      }\r\n    }\r\n\r\n    return this._spacingAfterName;\r\n  }\r\n\r\n  /**\r\n   * Generates the HTML for this tag.\r\n   */\r\n  public emitAsHtml(): string {\r\n    // NOTE: Here we're assuming that the TSDoc representation for a tag is also a valid HTML expression.\r\n    const stringBuilder: StringBuilder = new StringBuilder();\r\n    const emitter: TSDocEmitter = new TSDocEmitter();\r\n    emitter.renderHtmlTag(stringBuilder, this);\r\n    return stringBuilder.toString();\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._openingDelimiterExcerpt,\r\n      this._nameExcerpt,\r\n      this._spacingAfterNameExcerpt,\r\n      ...this._htmlAttributes,\r\n      this._closingDelimiterExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}