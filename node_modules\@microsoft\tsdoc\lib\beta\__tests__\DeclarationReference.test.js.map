{"version": 3, "file": "DeclarationReference.test.js", "sourceRoot": "", "sources": ["../../../src/beta/__tests__/DeclarationReference.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;AAE3D,OAAO,EACL,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,aAAa,EACb,UAAU,EACV,mBAAmB,EACnB,oBAAoB,EACpB,eAAe,EACf,kBAAkB,EACnB,MAAM,yBAAyB,CAAC;AAEjC,QAAQ,CAAC,QAAQ,EAAE;IACjB,EAAE,CAAC,sBAAsB,EAAE;QACzB,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACpE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,gCAAgC,EAAE;QACnC,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAC5E,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,6kBAAA,wHAEH,EAAM,oCAAqC,EAAK,oCAAqC,EAAS,KAAM,EAAS,QAC7G,EAAQ,kCAAmC,EAAO,kCAAmC,EAAS,KAAM,EAAS,QAC7G,EAAqC,KAAM,EAAoC,KAAM,EAAS,KAAM,EAAS,QAC7G,EAAS,iCAAkC,EAAK,oCAAqC,EAAG,WAAY,EAAK,QACzG,EAAU,gCAAiC,EAAK,oCAAqC,EAAG,WAAY,EAAK,MAC5G,KALG,MAAM,EAAqC,KAAK,EAAqC,SAAS,EAAM,SAAS,EAC7G,QAAQ,EAAmC,OAAO,EAAmC,SAAS,EAAM,SAAS,EAC7G,qCAAqC,EAAM,oCAAoC,EAAM,SAAS,EAAM,SAAS,EAC7G,SAAS,EAAkC,KAAK,EAAqC,GAAG,EAAY,KAAK,EACzG,UAAU,EAAiC,KAAK,EAAqC,GAAG,EAAY,KAAK,EAC3G,2BAA2B,EAAE,UAAC,EAAkC;YAAhC,IAAI,UAAA,EAAE,IAAI,UAAA,EAAE,UAAU,gBAAA,EAAE,MAAM,YAAA;QAC9D,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAChD,MAAM,CAAE,GAAG,CAAC,MAAuB,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;YAChD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvE,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACrC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,kHAAA,gCAEH,EAAM,KAAM,EAAK,MACpB,KADG,MAAM,EAAM,KAAK,EACnB,2BAA2B,EAAE,UAAC,EAAgB;YAAd,IAAI,UAAA,EAAE,MAAM,YAAA;QAC5C,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;QAChD,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,aAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,gaAAA,0CAEH,EAAS,WAAY,EAAa,QAClC,EAAa,OAAQ,EAAiB,QACtC,EAAQ,YAAa,EAAiB,QACtC,EAAQ,YAAa,EAAY,QACjC,EAAa,OAAQ,EAAiB,QACtC,EAAY,QAAS,EAAgB,QACrC,EAAO,aAAc,EAAgB,QACrC,EAAe,KAAM,EAAmB,QACxC,EAAU,UAAW,EAAc,QACnC,EAAS,WAAY,EAAa,QAClC,EAAQ,YAAa,EAAqB,QAC1C,EAAO,aAAc,EAA0B,QAC/C,EAAS,WAAY,EAAsB,QAC3C,EAAW,SAAU,EAAmB,MAC3C,KAdG,SAAS,EAAY,OAAO,CAAC,KAAK,EAClC,aAAa,EAAQ,OAAO,CAAC,SAAS,EACtC,QAAQ,EAAa,OAAO,CAAC,SAAS,EACtC,QAAQ,EAAa,OAAO,CAAC,IAAI,EACjC,aAAa,EAAQ,OAAO,CAAC,SAAS,EACtC,YAAY,EAAS,OAAO,CAAC,QAAQ,EACrC,OAAO,EAAc,OAAO,CAAC,QAAQ,EACrC,eAAe,EAAM,OAAO,CAAC,WAAW,EACxC,UAAU,EAAW,OAAO,CAAC,MAAM,EACnC,SAAS,EAAY,OAAO,CAAC,KAAK,EAClC,QAAQ,EAAa,OAAO,CAAC,aAAa,EAC1C,OAAO,EAAc,OAAO,CAAC,kBAAkB,EAC/C,SAAS,EAAY,OAAO,CAAC,cAAc,EAC3C,WAAW,EAAU,OAAO,CAAC,WAAW,EAC1C,wBAAwB,EAAE,UAAC,EAAiB;YAAf,IAAI,UAAA,EAAE,OAAO,aAAA;QAC1C,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnE,MAAM,CAAC,GAAG,CAAC,MAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,eAAe,EAAE;QAClB,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAExF,IAAM,MAAM,GAAiB,GAAG,CAAC,MAAsB,CAAC;QACxD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE3C,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAM,MAAM,GAAoB,GAAG,CAAC,MAAO,CAAC;QAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAErC,IAAM,UAAU,GAAwB,MAAM,CAAC,aAAoC,CAAC;QACpF,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QACvD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElD,IAAM,UAAU,GAAwB,UAAU,CAAC,MAA6B,CAAC;QACjF,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QACvD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElD,IAAM,UAAU,GAAkB,UAAU,CAAC,MAAuB,CAAC;QACrE,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACjD,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElD,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,gCAAgC,EAAE;QACnC,MAAM,CAAC;YACL,oBAAoB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,EAAE,CAAC,qBAAqB,EAAE;IACxB,IAAM,GAAG,GAAyB,oBAAoB,CAAC,KAAK,EAAE,CAAC,iBAAiB,CAC9E,UAAU,CAAC,OAAO,EAClB,kBAAkB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAC9C,CAAC;IACF,IAAM,MAAM,GAAoB,GAAG,CAAC,MAAO,CAAC;IAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IAC/C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC;IAC3C,MAAM,CAAC,MAAM,CAAC,aAAc,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC;AACH,QAAQ,CAAC,sBAAsB,EAAE;IAC/B,EAAE,CAAC,IAAI,61BAAA,uCAEH,EAAE,cAAe,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAK,WAAY,EAAK,QACtB,EAAK,WAAY,EAAK,QACtB,EAAK,WAAY,EAAK,QACtB,EAAS,OAAQ,EAAK,QACtB,EAAI,YAAa,EAAK,QACtB,EAAI,YAAa,EAAK,QACtB,EAAI,YAAa,EAAK,QACtB,EAAG,aAAc,EAAK,QACtB,EAAG,aAAc,EAAK,QACtB,EAAG,aAAc,EAAK,QACtB,EAAG,aAAc,EAAK,QACtB,EAAG,aAAc,EAAK,QACtB,EAAG,aAAc,EAAK,QACtB,EAAK,WAAY,EAAK,QACtB,EAAO,SAAU,EAAK,QACtB,EAAO,SAAU,EAAK,QACtB,EAAI,YAAa,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAW,KAAM,EAAI,QACrB,EAAM,UAAW,EAAI,QACrB,EAAM,UAAW,EAAI,QACrB,EAAM,UAAW,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,MACxB,KAjCG,EAAE,EAAe,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,KAAK,EAAY,KAAK,EACtB,KAAK,EAAY,KAAK,EACtB,KAAK,EAAY,KAAK,EACtB,SAAS,EAAQ,KAAK,EACtB,IAAI,EAAa,KAAK,EACtB,IAAI,EAAa,KAAK,EACtB,IAAI,EAAa,KAAK,EACtB,GAAG,EAAc,KAAK,EACtB,GAAG,EAAc,KAAK,EACtB,GAAG,EAAc,KAAK,EACtB,GAAG,EAAc,KAAK,EACtB,GAAG,EAAc,KAAK,EACtB,GAAG,EAAc,KAAK,EACtB,KAAK,EAAY,KAAK,EACtB,OAAO,EAAU,KAAK,EACtB,OAAO,EAAU,KAAK,EACtB,IAAI,EAAa,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,WAAW,EAAM,IAAI,EACrB,MAAM,EAAW,IAAI,EACrB,MAAM,EAAW,IAAI,EACrB,MAAM,EAAW,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACvB,oCAAoC,EAAE,UAAC,EAAkB;YAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;QACvD,MAAM,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,uhBAAA,qCAEH,EAAE,YAAa,EAAI,QACnB,EAAG,WAAY,EAAG,QAClB,EAAK,SAAU,EAAO,QACtB,EAAK,SAAU,EAAO,QACtB,EAAK,SAAU,EAAO,QACtB,EAAS,KAAM,EAAW,QAC1B,EAAI,UAAW,EAAM,QACrB,EAAI,UAAW,EAAM,QACrB,EAAI,UAAW,EAAM,QACrB,EAAG,WAAY,EAAK,QACpB,EAAG,WAAY,EAAK,QACpB,EAAG,WAAY,EAAK,QACpB,EAAG,WAAY,EAAK,QACpB,EAAG,WAAY,EAAK,QACpB,EAAG,WAAY,EAAK,QACpB,EAAK,SAAU,EAAO,QACtB,EAAO,OAAQ,EAAS,QACxB,EAAO,OAAQ,EAAS,QACxB,EAAI,UAAW,EAAU,QACzB,EAAK,SAAU,EAAW,MAC7B,KApBG,EAAE,EAAa,IAAI,EACnB,GAAG,EAAY,GAAG,EAClB,KAAK,EAAU,OAAO,EACtB,KAAK,EAAU,OAAO,EACtB,KAAK,EAAU,OAAO,EACtB,SAAS,EAAM,WAAW,EAC1B,IAAI,EAAW,MAAM,EACrB,IAAI,EAAW,MAAM,EACrB,IAAI,EAAW,MAAM,EACrB,GAAG,EAAY,KAAK,EACpB,GAAG,EAAY,KAAK,EACpB,GAAG,EAAY,KAAK,EACpB,GAAG,EAAY,KAAK,EACpB,GAAG,EAAY,KAAK,EACpB,GAAG,EAAY,KAAK,EACpB,KAAK,EAAU,OAAO,EACtB,OAAO,EAAQ,SAAS,EACxB,OAAO,EAAQ,SAAS,EACxB,IAAI,EAAW,UAAU,EACzB,KAAK,EAAU,WAAW,EAC5B,8BAA8B,EAAE,UAAC,EAAkB;YAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;QACjD,MAAM,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,kQAAA,uCAEH,EAAE,cAAe,EAAE,QACnB,EAAI,YAAa,EAAE,QACnB,EAAG,aAAc,EAAG,QACpB,EAAK,WAAY,EAAG,QACpB,EAAO,SAAU,EAAK,QACtB,EAAU,MAAO,EAAI,QACrB,EAAW,KAAM,EAAK,MACzB,KAPG,EAAE,EAAe,EAAE,EACnB,IAAI,EAAa,EAAE,EACnB,GAAG,EAAc,GAAG,EACpB,KAAK,EAAY,GAAG,EACpB,OAAO,EAAU,KAAK,EACtB,UAAU,EAAO,IAAI,EACrB,WAAW,EAAM,KAAK,EACxB,gCAAgC,EAAE,UAAC,EAAkB;YAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;QACnD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,cAAM,OAAA,oBAAoB,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAlD,CAAkD,CAAC,CAAC,OAAO,EAAE,CAAC;QAC7E,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,g3BAAA,uCAEH,EAAE,cAAe,EAAK,QACtB,EAAG,aAAc,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAS,OAAQ,EAAI,QACrB,EAAI,YAAa,EAAK,QACtB,EAAI,YAAa,EAAI,QACrB,EAAI,YAAa,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAG,aAAc,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAO,SAAU,EAAK,QACtB,EAAI,YAAa,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAO,SAAU,EAAI,QACrB,EAAW,KAAM,EAAI,QACrB,EAAM,UAAW,EAAI,QACrB,EAAM,UAAW,EAAI,QACrB,EAAM,UAAW,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAK,WAAY,EAAI,QACrB,EAAS,OAAQ,EAAI,MACxB,KAlCG,EAAE,EAAe,KAAK,EACtB,GAAG,EAAc,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,SAAS,EAAQ,IAAI,EACrB,IAAI,EAAa,KAAK,EACtB,IAAI,EAAa,IAAI,EACrB,IAAI,EAAa,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,GAAG,EAAc,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,OAAO,EAAU,KAAK,EACtB,IAAI,EAAa,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,OAAO,EAAU,IAAI,EACrB,WAAW,EAAM,IAAI,EACrB,MAAM,EAAW,IAAI,EACrB,MAAM,EAAW,IAAI,EACrB,MAAM,EAAW,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,KAAK,EAAY,IAAI,EACrB,SAAS,EAAQ,IAAI,EACvB,uCAAuC,EAAE,UAAC,EAAkB;YAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;QAC1D,MAAM,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,uhBAAA,qCAEH,EAAE,YAAa,EAAI,QACnB,EAAG,WAAY,EAAG,QAClB,EAAK,SAAU,EAAK,QACpB,EAAK,SAAU,EAAK,QACpB,EAAK,SAAU,EAAK,QACpB,EAAS,KAAM,EAAS,QACxB,EAAI,UAAW,EAAM,QACrB,EAAI,UAAW,EAAI,QACnB,EAAI,UAAW,EAAI,QACnB,EAAG,WAAY,EAAG,QAClB,EAAG,WAAY,EAAG,QAClB,EAAG,WAAY,EAAG,QAClB,EAAG,WAAY,EAAG,QAClB,EAAG,WAAY,EAAG,QAClB,EAAG,WAAY,EAAG,QAClB,EAAK,SAAU,EAAK,QACpB,EAAO,OAAQ,EAAO,QACtB,EAAO,OAAQ,EAAS,QACxB,EAAI,UAAW,EAAU,QACzB,EAAK,SAAU,EAAW,MAC7B,KApBG,EAAE,EAAa,IAAI,EACnB,GAAG,EAAY,GAAG,EAClB,KAAK,EAAU,KAAK,EACpB,KAAK,EAAU,KAAK,EACpB,KAAK,EAAU,KAAK,EACpB,SAAS,EAAM,SAAS,EACxB,IAAI,EAAW,MAAM,EACrB,IAAI,EAAW,IAAI,EACnB,IAAI,EAAW,IAAI,EACnB,GAAG,EAAY,GAAG,EAClB,GAAG,EAAY,GAAG,EAClB,GAAG,EAAY,GAAG,EAClB,GAAG,EAAY,GAAG,EAClB,GAAG,EAAY,GAAG,EAClB,GAAG,EAAY,GAAG,EAClB,KAAK,EAAU,KAAK,EACpB,OAAO,EAAQ,OAAO,EACtB,OAAO,EAAQ,SAAS,EACxB,IAAI,EAAW,UAAU,EACzB,KAAK,EAAU,WAAW,EAC5B,iCAAiC,EAAE,UAAC,EAAkB;YAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;QACpD,MAAM,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,wRAAA,uCAEH,EAAE,cAAe,EAAS,QAC1B,EAAI,YAAa,EAAE,QACnB,EAAG,aAAc,EAAG,QACpB,EAAK,WAAY,EAAG,QACpB,EAAM,UAAW,EAAI,QACrB,EAAO,SAAU,EAAK,QACtB,EAAU,MAAO,EAAI,QACrB,EAAW,KAAM,EAAK,MACzB,KARG,EAAE,EAAe,SAAS,EAC1B,IAAI,EAAa,EAAE,EACnB,GAAG,EAAc,GAAG,EACpB,KAAK,EAAY,GAAG,EACpB,MAAM,EAAW,IAAI,EACrB,OAAO,EAAU,KAAK,EACtB,UAAU,EAAO,IAAI,EACrB,WAAW,EAAM,KAAK,EACxB,mCAAmC,EAAE,UAAC,EAAkB;YAAhB,IAAI,UAAA,EAAE,QAAQ,cAAA;QACtD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,MAAM,CAAC,cAAM,OAAA,oBAAoB,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAArD,CAAqD,CAAC,CAAC,OAAO,EAAE,CAAC;QAChF,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,QAAQ,CAAC,cAAc,EAAE;IACvB,EAAE,CAAC,IAAI,yYAAA,sFAEH,EAAG,UAAW,EAAG,UAAW,EAAE,SAAU,EAAG,kBAAmB,EAAE,QAChE,EAAK,QAAS,EAAG,UAAW,EAAE,SAAU,EAAG,kBAAmB,EAAG,QACjE,EAAM,OAAQ,EAAM,OAAQ,EAAI,OAAQ,EAAG,kBAAmB,EAAE,QAChE,EAAQ,KAAM,EAAM,OAAQ,EAAI,OAAQ,EAAG,kBAAmB,EAAG,MACpE,KAJG,GAAG,EAAW,GAAG,EAAW,EAAE,EAAU,GAAG,EAAmB,EAAE,EAChE,KAAK,EAAS,GAAG,EAAW,EAAE,EAAU,GAAG,EAAmB,GAAG,EACjE,MAAM,EAAQ,MAAM,EAAQ,IAAI,EAAQ,GAAG,EAAmB,EAAE,EAChE,QAAQ,EAAM,MAAM,EAAQ,IAAI,EAAQ,GAAG,EAAmB,GAAG,EACnE,wBAAwB,EAAE,UAAC,EAAiE;YAA/D,IAAI,UAAA,EAAE,WAAW,iBAAA,EAAE,SAAS,eAAA,EAAE,mBAAmB,yBAAA,EAAE,UAAU,gBAAA;QAC1F,IAAM,MAAM,GAAiB,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC7D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,4OAAA,+CAEH,EAAG,UAAW,EAAS,KAAM,EAAG,QAChC,EAAG,UAAW,EAAG,WAAY,EAAK,QAClC,EAAM,OAAQ,EAAS,KAAM,EAAM,QACnC,EAAM,OAAQ,EAAG,WAAY,EAAQ,MACxC,KAJG,GAAG,EAAW,SAAS,EAAM,GAAG,EAChC,GAAG,EAAW,GAAG,EAAY,KAAK,EAClC,MAAM,EAAQ,SAAS,EAAM,MAAM,EACnC,MAAM,EAAQ,GAAG,EAAY,QAAQ,EACvC,wCAAwC,EAAE,UAAC,EAAiC;YAA/B,WAAW,iBAAA,EAAE,UAAU,gBAAA,EAAE,IAAI,UAAA;QAC1E,IAAM,MAAM,GAAiB,YAAY,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC/E,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,IAAI,kbAAA,mEAEH,EAAE,SAAU,EAAG,kBAAmB,EAAS,KAAM,EAAG,QACpD,EAAE,SAAU,EAAG,kBAAmB,EAAG,WAAY,EAAK,QACtD,EAAG,QAAS,EAAG,kBAAmB,EAAS,KAAM,EAAM,QACvD,EAAI,OAAQ,EAAG,kBAAmB,EAAS,KAAM,EAAM,QACvD,EAAG,QAAS,EAAG,kBAAmB,EAAG,WAAY,EAAQ,QACzD,EAAI,OAAQ,EAAG,kBAAmB,EAAG,WAAY,EAAQ,MAC5D,KANG,EAAE,EAAU,GAAG,EAAmB,SAAS,EAAM,GAAG,EACpD,EAAE,EAAU,GAAG,EAAmB,GAAG,EAAY,KAAK,EACtD,GAAG,EAAS,GAAG,EAAmB,SAAS,EAAM,MAAM,EACvD,IAAI,EAAQ,GAAG,EAAmB,SAAS,EAAM,MAAM,EACvD,GAAG,EAAS,GAAG,EAAmB,GAAG,EAAY,QAAQ,EACzD,IAAI,EAAQ,GAAG,EAAmB,GAAG,EAAY,QAAQ,EAE3D,kEAAkE,EAClE,UAAC,EAAoD;YAAlD,SAAS,eAAA,EAAE,mBAAmB,yBAAA,EAAE,UAAU,gBAAA,EAAE,IAAI,UAAA;QACjD,IAAM,MAAM,GAAiB,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,UAAU,CAAC,CAAC;QACxG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC,CACF,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport {\r\n  ModuleSource,\r\n  GlobalSource,\r\n  Meaning,\r\n  ComponentRoot,\r\n  Navigation,\r\n  ComponentNavigation,\r\n  DeclarationReference,\r\n  SymbolReference,\r\n  ComponentReference\r\n} from '../DeclarationReference';\r\n\r\ndescribe('parser', () => {\r\n  it('parse component text', () => {\r\n    const ref: DeclarationReference = DeclarationReference.parse('abc');\r\n    expect(ref.source).toBeUndefined();\r\n    expect(ref.symbol).toBeInstanceOf(SymbolReference);\r\n    expect(ref.symbol!.componentPath).toBeDefined();\r\n    expect(ref.symbol!.componentPath!.component.toString()).toBe('abc');\r\n  });\r\n  it('parse component text string', () => {\r\n    const ref: DeclarationReference = DeclarationReference.parse('\"abc\"');\r\n    expect(ref.source).toBeUndefined();\r\n    expect(ref.symbol).toBeInstanceOf(SymbolReference);\r\n    expect(ref.symbol!.componentPath).toBeDefined();\r\n    expect(ref.symbol!.componentPath!.component.toString()).toBe('\"abc\"');\r\n  });\r\n  it('parse bracketed component text', () => {\r\n    const ref: DeclarationReference = DeclarationReference.parse('[abc.[def]]');\r\n    expect(ref.source).toBeUndefined();\r\n    expect(ref.symbol).toBeInstanceOf(SymbolReference);\r\n    expect(ref.symbol!.componentPath).toBeDefined();\r\n    expect(ref.symbol!.componentPath!.component.toString()).toBe('[abc.[def]]');\r\n  });\r\n  it.each`\r\n    text                                     | path                                    | navigation   | symbol\r\n    ${'abc!'}                                | ${'abc'}                                | ${undefined} | ${undefined}\r\n    ${'\"abc\"!'}                              | ${'\"abc\"'}                              | ${undefined} | ${undefined}\r\n    ${'@microsoft/rush-stack-compiler-3.5!'} | ${'@microsoft/rush-stack-compiler-3.5'} | ${undefined} | ${undefined}\r\n    ${'abc!def'}                             | ${'abc'}                                | ${'.'}       | ${'def'}\r\n    ${'abc!~def'}                            | ${'abc'}                                | ${'~'}       | ${'def'}\r\n  `('parse module source $text', ({ text, path, navigation, symbol }) => {\r\n    const ref: DeclarationReference = DeclarationReference.parse(text);\r\n    expect(ref.source).toBeInstanceOf(ModuleSource);\r\n    expect((ref.source as ModuleSource).escapedPath).toBe(path);\r\n    expect(ref.navigation).toBe(navigation);\r\n    if (symbol) {\r\n      expect(ref.symbol).toBeInstanceOf(SymbolReference);\r\n      expect(ref.symbol!.componentPath).toBeDefined();\r\n      expect(ref.symbol!.componentPath!.component.toString()).toBe(symbol);\r\n    } else {\r\n      expect(ref.symbol).toBeUndefined();\r\n    }\r\n  });\r\n  it.each`\r\n    text      | symbol\r\n    ${'!abc'} | ${'abc'}\r\n  `('parse global source $text', ({ text, symbol }) => {\r\n    const ref: DeclarationReference = DeclarationReference.parse(text);\r\n    expect(ref.source).toBe(GlobalSource.instance);\r\n    expect(ref.symbol).toBeInstanceOf(SymbolReference);\r\n    expect(ref.symbol!.componentPath).toBeDefined();\r\n    expect(ref.symbol!.componentPath!.component.toString()).toBe(symbol);\r\n  });\r\n  it.each`\r\n    text               | meaning\r\n    ${'a:class'}       | ${Meaning.Class}\r\n    ${'a:interface'}   | ${Meaning.Interface}\r\n    ${'a:type'}        | ${Meaning.TypeAlias}\r\n    ${'a:enum'}        | ${Meaning.Enum}\r\n    ${'a:namespace'}   | ${Meaning.Namespace}\r\n    ${'a:function'}    | ${Meaning.Function}\r\n    ${'a:var'}         | ${Meaning.Variable}\r\n    ${'a:constructor'} | ${Meaning.Constructor}\r\n    ${'a:member'}      | ${Meaning.Member}\r\n    ${'a:event'}       | ${Meaning.Event}\r\n    ${'a:call'}        | ${Meaning.CallSignature}\r\n    ${'a:new'}         | ${Meaning.ConstructSignature}\r\n    ${'a:index'}       | ${Meaning.IndexSignature}\r\n    ${'a:complex'}     | ${Meaning.ComplexType}\r\n  `('parse meaning $meaning', ({ text, meaning }) => {\r\n    const ref: DeclarationReference = DeclarationReference.parse(text);\r\n    expect(ref.symbol!.meaning).toBe(meaning);\r\n  });\r\n  it('parse complex', () => {\r\n    const ref: DeclarationReference = DeclarationReference.parse('foo/bar!N.C#z:member(1)');\r\n\r\n    const source: ModuleSource = ref.source as ModuleSource;\r\n    expect(source).toBeInstanceOf(ModuleSource);\r\n    expect(source.escapedPath).toBe('foo/bar');\r\n\r\n    expect(ref.navigation).toBe(Navigation.Exports);\r\n\r\n    const symbol: SymbolReference = ref.symbol!;\r\n    expect(symbol).toBeInstanceOf(SymbolReference);\r\n    expect(symbol.meaning).toBe('member');\r\n    expect(symbol.overloadIndex).toBe(1);\r\n\r\n    const component1: ComponentNavigation = symbol.componentPath as ComponentNavigation;\r\n    expect(component1).toBeInstanceOf(ComponentNavigation);\r\n    expect(component1.navigation).toBe(Navigation.Members);\r\n    expect(component1.component.toString()).toBe('z');\r\n\r\n    const component2: ComponentNavigation = component1.parent as ComponentNavigation;\r\n    expect(component2).toBeInstanceOf(ComponentNavigation);\r\n    expect(component2.navigation).toBe(Navigation.Exports);\r\n    expect(component2.component.toString()).toBe('C');\r\n\r\n    const component3: ComponentRoot = component2.parent as ComponentRoot;\r\n    expect(component3).toBeInstanceOf(ComponentRoot);\r\n    expect(component3.component.toString()).toBe('N');\r\n\r\n    expect(ref.toString()).toBe('foo/bar!N.C#z:member(1)');\r\n  });\r\n  it('parse invalid module reference', () => {\r\n    expect(() => {\r\n      DeclarationReference.parse('@scope/foo');\r\n    }).toThrow();\r\n  });\r\n});\r\nit('add navigation step', () => {\r\n  const ref: DeclarationReference = DeclarationReference.empty().addNavigationStep(\r\n    Navigation.Members,\r\n    ComponentReference.parse('[Symbol.iterator]')\r\n  );\r\n  const symbol: SymbolReference = ref.symbol!;\r\n  expect(symbol).toBeInstanceOf(SymbolReference);\r\n  expect(symbol.componentPath).toBeDefined();\r\n  expect(symbol.componentPath!.component.toString()).toBe('[Symbol.iterator]');\r\n});\r\ndescribe('DeclarationReference', () => {\r\n  it.each`\r\n    text           | expected\r\n    ${''}          | ${true}\r\n    ${'a'}         | ${true}\r\n    ${'a.b'}       | ${false}\r\n    ${'a~b'}       | ${false}\r\n    ${'a#b'}       | ${false}\r\n    ${'a:class'}   | ${false}\r\n    ${'a!'}        | ${false}\r\n    ${'@a'}        | ${false}\r\n    ${'a@'}        | ${false}\r\n    ${'['}         | ${false}\r\n    ${']'}         | ${false}\r\n    ${'{'}         | ${false}\r\n    ${'}'}         | ${false}\r\n    ${'('}         | ${false}\r\n    ${')'}         | ${false}\r\n    ${'[a]'}       | ${false}\r\n    ${'[a.b]'}     | ${false}\r\n    ${'[a!b]'}     | ${false}\r\n    ${'\"\"'}        | ${true}\r\n    ${'\"a\"'}       | ${true}\r\n    ${'\"a.b\"'}     | ${true}\r\n    ${'\"a~b\"'}     | ${true}\r\n    ${'\"a#b\"'}     | ${true}\r\n    ${'\"a:class\"'} | ${true}\r\n    ${'\"a!\"'}      | ${true}\r\n    ${'\"@a\"'}      | ${true}\r\n    ${'\"a@\"'}      | ${true}\r\n    ${'\"[\"'}       | ${true}\r\n    ${'\"]\"'}       | ${true}\r\n    ${'\"{\"'}       | ${true}\r\n    ${'\"}\"'}       | ${true}\r\n    ${'\"(\"'}       | ${true}\r\n    ${'\")\"'}       | ${true}\r\n  `('isWellFormedComponentString($text)', ({ text, expected }) => {\r\n    expect(DeclarationReference.isWellFormedComponentString(text)).toBe(expected);\r\n  });\r\n  it.each`\r\n    text         | expected\r\n    ${''}        | ${'\"\"'}\r\n    ${'a'}       | ${'a'}\r\n    ${'a.b'}     | ${'\"a.b\"'}\r\n    ${'a~b'}     | ${'\"a~b\"'}\r\n    ${'a#b'}     | ${'\"a#b\"'}\r\n    ${'a:class'} | ${'\"a:class\"'}\r\n    ${'a!'}      | ${'\"a!\"'}\r\n    ${'@a'}      | ${'\"@a\"'}\r\n    ${'a@'}      | ${'\"a@\"'}\r\n    ${'['}       | ${'\"[\"'}\r\n    ${']'}       | ${'\"]\"'}\r\n    ${'{'}       | ${'\"{\"'}\r\n    ${'}'}       | ${'\"}\"'}\r\n    ${'('}       | ${'\"(\"'}\r\n    ${')'}       | ${'\")\"'}\r\n    ${'[a]'}     | ${'\"[a]\"'}\r\n    ${'[a.b]'}   | ${'\"[a.b]\"'}\r\n    ${'[a!b]'}   | ${'\"[a!b]\"'}\r\n    ${'\"\"'}      | ${'\"\\\\\"\\\\\"\"'}\r\n    ${'\"a\"'}     | ${'\"\\\\\"a\\\\\"\"'}\r\n  `('escapeComponentString($text)', ({ text, expected }) => {\r\n    expect(DeclarationReference.escapeComponentString(text)).toBe(expected);\r\n  });\r\n  it.each`\r\n    text           | expected\r\n    ${''}          | ${''}\r\n    ${'\"\"'}        | ${''}\r\n    ${'a'}         | ${'a'}\r\n    ${'\"a\"'}       | ${'a'}\r\n    ${'\"a.b\"'}     | ${'a.b'}\r\n    ${'\"\\\\\"\\\\\"\"'}  | ${'\"\"'}\r\n    ${'\"\\\\\"a\\\\\"\"'} | ${'\"a\"'}\r\n  `('unescapeComponentString($text)', ({ text, expected }) => {\r\n    if (expected === undefined) {\r\n      expect(() => DeclarationReference.unescapeComponentString(text)).toThrow();\r\n    } else {\r\n      expect(DeclarationReference.unescapeComponentString(text)).toBe(expected);\r\n    }\r\n  });\r\n  it.each`\r\n    text           | expected\r\n    ${''}          | ${false}\r\n    ${'a'}         | ${true}\r\n    ${'a.b'}       | ${true}\r\n    ${'a~b'}       | ${true}\r\n    ${'a#b'}       | ${true}\r\n    ${'a:class'}   | ${true}\r\n    ${'a!'}        | ${false}\r\n    ${'@a'}        | ${true}\r\n    ${'a@'}        | ${true}\r\n    ${'['}         | ${true}\r\n    ${']'}         | ${true}\r\n    ${'{'}         | ${true}\r\n    ${'}'}         | ${true}\r\n    ${'('}         | ${true}\r\n    ${')'}         | ${true}\r\n    ${'[a]'}       | ${true}\r\n    ${'[a.b]'}     | ${true}\r\n    ${'[a!b]'}     | ${false}\r\n    ${'\"\"'}        | ${true}\r\n    ${'\"a\"'}       | ${true}\r\n    ${'\"a.b\"'}     | ${true}\r\n    ${'\"a~b\"'}     | ${true}\r\n    ${'\"a#b\"'}     | ${true}\r\n    ${'\"a:class\"'} | ${true}\r\n    ${'\"a!\"'}      | ${true}\r\n    ${'\"@a\"'}      | ${true}\r\n    ${'\"a@\"'}      | ${true}\r\n    ${'\"[\"'}       | ${true}\r\n    ${'\"]\"'}       | ${true}\r\n    ${'\"{\"'}       | ${true}\r\n    ${'\"}\"'}       | ${true}\r\n    ${'\"(\"'}       | ${true}\r\n    ${'\")\"'}       | ${true}\r\n    ${'\"[a!b]\"'}   | ${true}\r\n  `('isWellFormedModuleSourceString($text)', ({ text, expected }) => {\r\n    expect(DeclarationReference.isWellFormedModuleSourceString(text)).toBe(expected);\r\n  });\r\n  it.each`\r\n    text         | expected\r\n    ${''}        | ${'\"\"'}\r\n    ${'a'}       | ${'a'}\r\n    ${'a.b'}     | ${'a.b'}\r\n    ${'a~b'}     | ${'a~b'}\r\n    ${'a#b'}     | ${'a#b'}\r\n    ${'a:class'} | ${'a:class'}\r\n    ${'a!'}      | ${'\"a!\"'}\r\n    ${'@a'}      | ${'@a'}\r\n    ${'a@'}      | ${'a@'}\r\n    ${'['}       | ${'['}\r\n    ${']'}       | ${']'}\r\n    ${'{'}       | ${'{'}\r\n    ${'}'}       | ${'}'}\r\n    ${'('}       | ${'('}\r\n    ${')'}       | ${')'}\r\n    ${'[a]'}     | ${'[a]'}\r\n    ${'[a.b]'}   | ${'[a.b]'}\r\n    ${'[a!b]'}   | ${'\"[a!b]\"'}\r\n    ${'\"\"'}      | ${'\"\\\\\"\\\\\"\"'}\r\n    ${'\"a\"'}     | ${'\"\\\\\"a\\\\\"\"'}\r\n  `('escapeModuleSourceString($text)', ({ text, expected }) => {\r\n    expect(DeclarationReference.escapeModuleSourceString(text)).toBe(expected);\r\n  });\r\n  it.each`\r\n    text           | expected\r\n    ${''}          | ${undefined}\r\n    ${'\"\"'}        | ${''}\r\n    ${'a'}         | ${'a'}\r\n    ${'\"a\"'}       | ${'a'}\r\n    ${'\"a!\"'}      | ${'a!'}\r\n    ${'\"a.b\"'}     | ${'a.b'}\r\n    ${'\"\\\\\"\\\\\"\"'}  | ${'\"\"'}\r\n    ${'\"\\\\\"a\\\\\"\"'} | ${'\"a\"'}\r\n  `('unescapeModuleSourceString($text)', ({ text, expected }) => {\r\n    if (expected === undefined) {\r\n      expect(() => DeclarationReference.unescapeModuleSourceString(text)).toThrow();\r\n    } else {\r\n      expect(DeclarationReference.unescapeModuleSourceString(text)).toBe(expected);\r\n    }\r\n  });\r\n});\r\ndescribe('ModuleSource', () => {\r\n  it.each`\r\n    text        | packageName | scopeName | unscopedPackageName | importPath\r\n    ${'a'}      | ${'a'}      | ${''}     | ${'a'}              | ${''}\r\n    ${'a/b'}    | ${'a'}      | ${''}     | ${'a'}              | ${'b'}\r\n    ${'@a/b'}   | ${'@a/b'}   | ${'@a'}   | ${'b'}              | ${''}\r\n    ${'@a/b/c'} | ${'@a/b'}   | ${'@a'}   | ${'b'}              | ${'c'}\r\n  `('package parts of $text', ({ text, packageName, scopeName, unscopedPackageName, importPath }) => {\r\n    const source: ModuleSource = new ModuleSource(text);\r\n    expect(source.packageName).toBe(packageName);\r\n    expect(source.scopeName).toBe(scopeName);\r\n    expect(source.unscopedPackageName).toBe(unscopedPackageName);\r\n    expect(source.importPath).toBe(importPath);\r\n  });\r\n  it.each`\r\n    packageName | importPath   | text\r\n    ${'a'}      | ${undefined} | ${'a'}\r\n    ${'a'}      | ${'b'}       | ${'a/b'}\r\n    ${'@a/b'}   | ${undefined} | ${'@a/b'}\r\n    ${'@a/b'}   | ${'c'}       | ${'@a/b/c'}\r\n  `('fromPackage($packageName, $importPath)', ({ packageName, importPath, text }) => {\r\n    const source: ModuleSource = ModuleSource.fromPackage(packageName, importPath);\r\n    expect(source.path).toBe(text);\r\n  });\r\n  it.each`\r\n    scopeName | unscopedPackageName | importPath   | text\r\n    ${''}     | ${'a'}              | ${undefined} | ${'a'}\r\n    ${''}     | ${'a'}              | ${'b'}       | ${'a/b'}\r\n    ${'a'}    | ${'b'}              | ${undefined} | ${'@a/b'}\r\n    ${'@a'}   | ${'b'}              | ${undefined} | ${'@a/b'}\r\n    ${'a'}    | ${'b'}              | ${'c'}       | ${'@a/b/c'}\r\n    ${'@a'}   | ${'b'}              | ${'c'}       | ${'@a/b/c'}\r\n  `(\r\n    'fromScopedPackage($scopeName, $unscopedPackageName, $importPath)',\r\n    ({ scopeName, unscopedPackageName, importPath, text }) => {\r\n      const source: ModuleSource = ModuleSource.fromScopedPackage(scopeName, unscopedPackageName, importPath);\r\n      expect(source.path).toBe(text);\r\n    }\r\n  );\r\n});\r\n"]}