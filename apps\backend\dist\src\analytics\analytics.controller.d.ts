import { AnalyticsService } from './analytics.service';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getDashboardMetrics(productPageId: string, req: any): Promise<{
        overview: {
            totalProducts: any;
            totalOrders: any;
            totalRevenue: number;
            ordersLast30Days: any;
            ordersLast7Days: any;
            revenueLast30Days: number;
            revenueLast7Days: number;
        };
        topProducts: any[];
        recentOrders: any;
    }>;
    getRevenueChart(productPageId: string, req: any, days?: string): Promise<{
        date: string;
        revenue: any;
    }[]>;
    getOrderStatusDistribution(productPageId: string, req: any): Promise<any>;
    recordPageView(productPageId: string): Promise<void>;
    recordVisitor(productPageId: string): Promise<void>;
}
