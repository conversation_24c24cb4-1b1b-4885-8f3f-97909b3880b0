{"version": 3, "file": "DocParamBlock.js", "sourceRoot": "", "sources": ["../../src/nodes/DocParamBlock.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACjD,OAAO,EAAE,QAAQ,EAA4D,MAAM,YAAY,CAAC;AAEhG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAkCvD;;;GAGG;AACH;IAAmC,iCAAQ;IAoBzC;;;OAGG;IACH,uBAAmB,UAAqE;QACtF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;QAE/C,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,IAAI,UAAU,CAAC,iCAAiC,EAAE,CAAC;gBACjD,KAAI,CAAC,kCAAkC,GAAG,IAAI,UAAU,CAAC;oBACvD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,iCAAiC;iBACtD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,8CAA8C,EAAE,CAAC;gBAC9D,KAAI,CAAC,+CAA+C,GAAG,IAAI,UAAU,CAAC;oBACpE,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,eAAe;oBACxC,OAAO,EAAE,UAAU,CAAC,8CAA8C;iBACnE,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,8CAA8C,EAAE,CAAC;gBAC9D,KAAI,CAAC,+CAA+C,GAAG,IAAI,UAAU,CAAC;oBACpE,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,eAAe;oBACxC,OAAO,EAAE,UAAU,CAAC,8CAA8C;iBACnE,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,qBAAqB,GAAG,IAAI,UAAU,CAAC;gBAC1C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,wBAAwB;gBACjD,OAAO,EAAE,UAAU,CAAC,oBAAoB;aACzC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,uCAAuC,EAAE,CAAC;gBACvD,KAAI,CAAC,wCAAwC,GAAG,IAAI,UAAU,CAAC;oBAC7D,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,eAAe;oBACxC,OAAO,EAAE,UAAU,CAAC,uCAAuC;iBAC5D,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,gCAAgC,EAAE,CAAC;gBAChD,KAAI,CAAC,iCAAiC,GAAG,IAAI,UAAU,CAAC;oBACtD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,gCAAgC;iBACrD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,6CAA6C,EAAE,CAAC;gBAC7D,KAAI,CAAC,8CAA8C,GAAG,IAAI,UAAU,CAAC;oBACnE,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,eAAe;oBACxC,OAAO,EAAE,UAAU,CAAC,6CAA6C;iBAClE,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC7B,KAAI,CAAC,cAAc,GAAG,IAAI,UAAU,CAAC;oBACnC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,iBAAiB;oBAC1C,OAAO,EAAE,UAAU,CAAC,aAAa;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,yBAAyB,EAAE,CAAC;gBACzC,KAAI,CAAC,0BAA0B,GAAG,IAAI,UAAU,CAAC;oBAC/C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,yBAAyB;iBAC9C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,sCAAsC,EAAE,CAAC;gBACtD,KAAI,CAAC,uCAAuC,GAAG,IAAI,UAAU,CAAC;oBAC5D,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,eAAe;oBACxC,OAAO,EAAE,UAAU,CAAC,sCAAsC;iBAC3D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;;IACH,CAAC;IAGD,sBAAW,+BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,UAAU,CAAC;QAChC,CAAC;;;OAAA;IAMD,sBAAW,wCAAa;QAJxB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;;;OAAA;IAED,gBAAgB;IACN,uCAAe,GAAzB;QACE,OAAO;YACL,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,kCAAkC;YACvC,IAAI,CAAC,+CAA+C;YACpD,IAAI,CAAC,+CAA+C;YACpD,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,wCAAwC;YAC7C,IAAI,CAAC,iCAAiC;YACtC,IAAI,CAAC,8CAA8C;YACnD,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,0BAA0B;YAC/B,IAAI,CAAC,uCAAuC;YAC5C,IAAI,CAAC,OAAO;SACb,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AA5ID,CAAmC,QAAQ,GA4I1C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode } from './DocNode';\r\nimport { DocBlock, type IDocBlockParameters, type IDocBlockParsedParameters } from './DocBlock';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocParamBlock}.\r\n */\r\nexport interface IDocParamBlockParameters extends IDocBlockParameters {\r\n  parameterName: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocParamBlock}.\r\n */\r\nexport interface IDocParamBlockParsedParameters extends IDocBlockParsedParameters {\r\n  spacingBeforeParameterNameExcerpt?: TokenSequence;\r\n\r\n  unsupportedJsdocTypeBeforeParameterNameExcerpt?: TokenSequence;\r\n  unsupportedJsdocOptionalNameOpenBracketExcerpt?: TokenSequence;\r\n\r\n  parameterNameExcerpt: TokenSequence;\r\n  parameterName: string;\r\n\r\n  unsupportedJsdocOptionalNameRestExcerpt?: TokenSequence;\r\n\r\n  spacingAfterParameterNameExcerpt?: TokenSequence;\r\n\r\n  unsupportedJsdocTypeAfterParameterNameExcerpt?: TokenSequence;\r\n\r\n  hyphenExcerpt?: TokenSequence;\r\n\r\n  spacingAfterHyphenExcerpt?: TokenSequence;\r\n\r\n  unsupportedJsdocTypeAfterHyphenExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a parsed `@param` or `@typeParam` block, which provides a description for a\r\n * function parameter.\r\n */\r\nexport class DocParamBlock extends DocBlock {\r\n  private readonly _spacingBeforeParameterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _unsupportedJsdocTypeBeforeParameterNameExcerpt: DocExcerpt | undefined;\r\n  private readonly _unsupportedJsdocOptionalNameOpenBracketExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _parameterName: string;\r\n  private readonly _parameterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _unsupportedJsdocOptionalNameRestExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _spacingAfterParameterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _unsupportedJsdocTypeAfterParameterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _hyphenExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterHyphenExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _unsupportedJsdocTypeAfterHyphenExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocParamBlockParameters | IDocParamBlockParsedParameters) {\r\n    super(parameters);\r\n\r\n    this._parameterName = parameters.parameterName;\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      if (parameters.spacingBeforeParameterNameExcerpt) {\r\n        this._spacingBeforeParameterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingBeforeParameterNameExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.unsupportedJsdocTypeBeforeParameterNameExcerpt) {\r\n        this._unsupportedJsdocTypeBeforeParameterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.NonstandardText,\r\n          content: parameters.unsupportedJsdocTypeBeforeParameterNameExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.unsupportedJsdocOptionalNameOpenBracketExcerpt) {\r\n        this._unsupportedJsdocOptionalNameOpenBracketExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.NonstandardText,\r\n          content: parameters.unsupportedJsdocOptionalNameOpenBracketExcerpt\r\n        });\r\n      }\r\n\r\n      this._parameterNameExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.ParamBlock_ParameterName,\r\n        content: parameters.parameterNameExcerpt\r\n      });\r\n\r\n      if (parameters.unsupportedJsdocOptionalNameRestExcerpt) {\r\n        this._unsupportedJsdocOptionalNameRestExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.NonstandardText,\r\n          content: parameters.unsupportedJsdocOptionalNameRestExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.spacingAfterParameterNameExcerpt) {\r\n        this._spacingAfterParameterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterParameterNameExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.unsupportedJsdocTypeAfterParameterNameExcerpt) {\r\n        this._unsupportedJsdocTypeAfterParameterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.NonstandardText,\r\n          content: parameters.unsupportedJsdocTypeAfterParameterNameExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.hyphenExcerpt) {\r\n        this._hyphenExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.ParamBlock_Hyphen,\r\n          content: parameters.hyphenExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.spacingAfterHyphenExcerpt) {\r\n        this._spacingAfterHyphenExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterHyphenExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.unsupportedJsdocTypeAfterHyphenExcerpt) {\r\n        this._unsupportedJsdocTypeAfterHyphenExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.NonstandardText,\r\n          content: parameters.unsupportedJsdocTypeAfterHyphenExcerpt\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.ParamBlock;\r\n  }\r\n\r\n  /**\r\n   * The name of the parameter that is being documented.\r\n   * For example \"width\" in `@param width - the width of the object`.\r\n   */\r\n  public get parameterName(): string {\r\n    return this._parameterName;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this.blockTag,\r\n      this._spacingBeforeParameterNameExcerpt,\r\n      this._unsupportedJsdocTypeBeforeParameterNameExcerpt,\r\n      this._unsupportedJsdocOptionalNameOpenBracketExcerpt,\r\n      this._parameterNameExcerpt,\r\n      this._unsupportedJsdocOptionalNameRestExcerpt,\r\n      this._spacingAfterParameterNameExcerpt,\r\n      this._unsupportedJsdocTypeAfterParameterNameExcerpt,\r\n      this._hyphenExcerpt,\r\n      this._spacingAfterHyphenExcerpt,\r\n      this._unsupportedJsdocTypeAfterHyphenExcerpt,\r\n      this.content\r\n    ];\r\n  }\r\n}\r\n"]}