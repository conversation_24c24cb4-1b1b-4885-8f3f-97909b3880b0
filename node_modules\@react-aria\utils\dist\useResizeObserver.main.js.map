{"mappings": ";;;;;;;;;AAIA,SAAS;IACP,OAAO,OAAO,OAAO,cAAc,KAAK;AAC1C;AAQO,SAAS,0CAAqC,OAAwC;IAC3F,MAAM,OAAC,GAAG,OAAE,GAAG,YAAE,QAAQ,EAAC,GAAG;IAE7B,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,UAAU,gBAAA,0BAAA,IAAK,OAAO;QAC1B,IAAI,CAAC,SACH;QAGF,IAAI,CAAC,2CAAqB;YACxB,OAAO,gBAAgB,CAAC,UAAU,UAAU;YAC5C,OAAO;gBACL,OAAO,mBAAmB,CAAC,UAAU,UAAU;YACjD;QACF,OAAO;YAEL,MAAM,yBAAyB,IAAI,OAAO,cAAc,CAAC,CAAC;gBACxD,IAAI,CAAC,QAAQ,MAAM,EACjB;gBAGF;YACF;YACA,uBAAuB,OAAO,CAAC,SAAS;qBAAC;YAAG;YAE5C,OAAO;gBACL,IAAI,SACF,uBAAuB,SAAS,CAAC;YAErC;QACF;IAEF,GAAG;QAAC;QAAU;QAAK;KAAI;AACzB", "sources": ["packages/@react-aria/utils/src/useResizeObserver.ts"], "sourcesContent": ["\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\nfunction hasResizeObserver() {\n  return typeof window.ResizeObserver !== 'undefined';\n}\n\ntype useResizeObserverOptionsType<T> = {\n  ref: RefObject<T | undefined | null> | undefined,\n  box?: ResizeObserverBoxOptions,\n  onResize: () => void\n}\n\nexport function useResizeObserver<T extends Element>(options: useResizeObserverOptionsType<T>): void {\n  const {ref, box, onResize} = options;\n\n  useEffect(() => {\n    let element = ref?.current;\n    if (!element) {\n      return;\n    }\n\n    if (!hasResizeObserver()) {\n      window.addEventListener('resize', onResize, false);\n      return () => {\n        window.removeEventListener('resize', onResize, false);\n      };\n    } else {\n\n      const resizeObserverInstance = new window.ResizeObserver((entries) => {\n        if (!entries.length) {\n          return;\n        }\n\n        onResize();\n      });\n      resizeObserverInstance.observe(element, {box});\n\n      return () => {\n        if (element) {\n          resizeObserverInstance.unobserve(element);\n        }\n      };\n    }\n\n  }, [onResize, ref, box]);\n}\n"], "names": [], "version": 3, "file": "useResizeObserver.main.js.map"}