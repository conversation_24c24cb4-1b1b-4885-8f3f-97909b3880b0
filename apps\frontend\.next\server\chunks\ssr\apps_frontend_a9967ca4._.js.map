{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/frontend/src/components/layout/Header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gFACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/frontend/src/components/layout/Header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,4DACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">ش</span>\n              </div>\n              <span className=\"text-xl font-bold\">شاريو</span>\n            </div>\n            <p className=\"text-gray-300 mb-4 max-w-md\">\n              منصة التجارة الإلكترونية الجزائرية الرائدة. أنشئ متجرك الإلكتروني في دقائق واستفد من جميع الأدوات اللازمة لنجاح تجارتك الإلكترونية.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-300 hover:text-white\">\n                <span className=\"sr-only\">Facebook</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-300 hover:text-white\">\n                <span className=\"sr-only\">Instagram</span>\n                <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C3.85 14.724 3.85 12.78 5.126 11.504c1.276-1.276 3.22-1.276 4.496 0 1.276 1.276 1.276 3.22 0 4.496-.875.807-2.026 1.297-3.323 1.297z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">روابط سريعة</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/features\" className=\"text-gray-300 hover:text-white\">\n                  المميزات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white\">\n                  الأسعار\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/templates\" className=\"text-gray-300 hover:text-white\">\n                  القوالب\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white\">\n                  المساعدة\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">الدعم</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white\">\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/docs\" className=\"text-gray-300 hover:text-white\">\n                  الوثائق\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-300\">\n            © 2024 شاريو. جميع الحقوق محفوظة.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAG3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;sDAIpE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;sDAInE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAiC;;;;;;;;;;;sDAIrE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;sCAQpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;sDAInE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAiC;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAiC;;;;;;;;;;;sDAI/D,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQzE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency: string = 'DZD'): string {\n  return new Intl.NumberFormat('ar-DZ', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-DZ', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-DZ', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date));\n}\n\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-500', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAMA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700',\n      secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',\n      outline: 'border border-gray-300 bg-transparent hover:bg-gray-50',\n      ghost: 'hover:bg-gray-100',\n      danger: 'bg-red-600 text-white hover:bg-red-700',\n    };\n\n    const sizes = {\n      sm: 'h-9 px-3 text-sm',\n      md: 'h-10 px-4 py-2',\n      lg: 'h-11 px-8',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SHARYOU/apps/frontend/src/app/setup/page.tsx"], "sourcesContent": ["import { Header } from '@/components/layout/Header';\nimport { Footer } from '@/components/layout/Footer';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { \n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  CodeBracketIcon,\n  CogIcon\n} from '@heroicons/react/24/outline';\n\nexport default function SetupPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            إعداد منصة شاريو\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            اتبع هذه الخطوات لإعداد منصة شاريو بالكامل\n          </p>\n        </div>\n\n        <div className=\"space-y-8\">\n          {/* Current Status */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <ExclamationTriangleIcon className=\"w-6 h-6 text-yellow-500 ml-2\" />\n                الحالة الحالية\n              </CardTitle>\n              <CardDescription>\n                أنت حالياً في وضع التجربة. بعض المميزات محدودة.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-4\">\n                <div className=\"flex\">\n                  <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-400\" />\n                  <div className=\"mr-3\">\n                    <h3 className=\"text-sm font-medium text-yellow-800\">\n                      وضع التجربة نشط\n                    </h3>\n                    <div className=\"mt-2 text-sm text-yellow-700\">\n                      <p>\n                        لتفعيل جميع المميزات مثل التسجيل الحقيقي، قاعدة البيانات، والمدفوعات، \n                        يجب إعداد Supabase.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Step 1: Supabase Setup */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <span className=\"w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold ml-3\">\n                  1\n                </span>\n                إنشاء مشروع Supabase\n              </CardTitle>\n              <CardDescription>\n                إنشاء قاعدة بيانات مجانية وخدمات المصادقة\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-3\">\n                <div className=\"flex items-start\">\n                  <CheckCircleIcon className=\"w-5 h-5 text-green-500 mt-0.5 ml-2\" />\n                  <div>\n                    <p className=\"font-medium\">اذهب إلى Supabase</p>\n                    <p className=\"text-sm text-gray-600\">قم بزيارة supabase.com وأنشئ حساب مجاني</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start\">\n                  <CheckCircleIcon className=\"w-5 h-5 text-green-500 mt-0.5 ml-2\" />\n                  <div>\n                    <p className=\"font-medium\">أنشئ مشروع جديد</p>\n                    <p className=\"text-sm text-gray-600\">اختر اسم \"Sharyou\" ومنطقة أوروبا الغربية</p>\n                  </div>\n                </div>\n                <div className=\"flex items-start\">\n                  <CheckCircleIcon className=\"w-5 h-5 text-green-500 mt-0.5 ml-2\" />\n                  <div>\n                    <p className=\"font-medium\">احصل على المفاتيح</p>\n                    <p className=\"text-sm text-gray-600\">من الإعدادات → API، انسخ URL والمفاتيح</p>\n                  </div>\n                </div>\n              </div>\n              \n              <a \n                href=\"https://supabase.com\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n              >\n                <Button className=\"w-full\">\n                  فتح Supabase\n                </Button>\n              </a>\n            </CardContent>\n          </Card>\n\n          {/* Step 2: Environment Configuration */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <span className=\"w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold ml-3\">\n                  2\n                </span>\n                تكوين متغيرات البيئة\n              </CardTitle>\n              <CardDescription>\n                إضافة مفاتيح Supabase إلى ملفات التكوين\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"bg-gray-50 rounded-md p-4\">\n                <h4 className=\"font-medium mb-2\">Frontend (.env.local):</h4>\n                <pre className=\"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto\">\n{`NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co\nNEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key`}\n                </pre>\n              </div>\n              \n              <div className=\"bg-gray-50 rounded-md p-4\">\n                <h4 className=\"font-medium mb-2\">Backend (.env):</h4>\n                <pre className=\"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto\">\n{`DATABASE_URL=\"your-supabase-connection-string\"\nSUPABASE_URL=\"https://your-project.supabase.co\"\nSUPABASE_SERVICE_ROLE_KEY=\"your-service-role-key\"`}\n                </pre>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Step 3: Database Setup */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <span className=\"w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold ml-3\">\n                  3\n                </span>\n                إعداد قاعدة البيانات\n              </CardTitle>\n              <CardDescription>\n                تشغيل أوامر إنشاء الجداول والبيانات التجريبية\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"bg-gray-50 rounded-md p-4\">\n                <h4 className=\"font-medium mb-2\">الأوامر المطلوبة:</h4>\n                <pre className=\"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto\">\n{`cd apps/backend\nnpm run db:generate\nnpm run db:push\nnpm run db:seed`}\n                </pre>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Step 4: Launch */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <span className=\"w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-bold ml-3\">\n                  4\n                </span>\n                تشغيل المنصة\n              </CardTitle>\n              <CardDescription>\n                بدء تشغيل الخادم والواجهة الأمامية\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"bg-gray-50 rounded-md p-4\">\n                <h4 className=\"font-medium mb-2\">تشغيل كلا الخادمين:</h4>\n                <pre className=\"text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto\">\n{`npm run dev`}\n                </pre>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-blue-50 p-4 rounded-md\">\n                  <h5 className=\"font-medium text-blue-900\">الواجهة الأمامية</h5>\n                  <p className=\"text-sm text-blue-700\">http://localhost:3000</p>\n                </div>\n                <div className=\"bg-green-50 p-4 rounded-md\">\n                  <h5 className=\"font-medium text-green-900\">API الخلفية</h5>\n                  <p className=\"text-sm text-green-700\">http://localhost:3001/api</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Help Section */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <CogIcon className=\"w-6 h-6 text-gray-500 ml-2\" />\n                تحتاج مساعدة؟\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                <p className=\"text-gray-600\">\n                  إذا واجهت أي مشاكل في الإعداد، تحقق من:\n                </p>\n                <ul className=\"list-disc list-inside space-y-1 text-sm text-gray-600\">\n                  <li>صحة متغيرات البيئة</li>\n                  <li>اتصال الإنترنت</li>\n                  <li>أن المنافذ 3000 و 3001 متاحة</li>\n                  <li>تثبيت جميع التبعيات</li>\n                </ul>\n                \n                <div className=\"mt-4 p-4 bg-blue-50 rounded-md\">\n                  <p className=\"text-sm text-blue-800\">\n                    <strong>ملاحظة:</strong> يمكنك استخدام المنصة في وضع التجربة حتى لو لم تكمل الإعداد.\n                    ستتمكن من رؤية جميع الواجهات والتصميم.\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n      \n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;AAOe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0JAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,oJAAA,CAAA,OAAI;;kDACH,8OAAC,oJAAA,CAAA,aAAU;;0DACT,8OAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,6OAAA,CAAA,0BAAuB;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;0DAGtE,8OAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,oJAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6OAAA,CAAA,0BAAuB;wDAAC,WAAU;;;;;;kEACnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EAGpD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYf,8OAAC,oJAAA,CAAA,OAAI;;kDACH,8OAAC,oJAAA,CAAA,aAAU;;0DACT,8OAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAK,WAAU;kEAAyG;;;;;;oDAElH;;;;;;;0DAGT,8OAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,oJAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;0DAEJ,cAAA,8OAAC,sJAAA,CAAA,SAAM;oDAAC,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;0CAQjC,8OAAC,oJAAA,CAAA,OAAI;;kDACH,8OAAC,oJAAA,CAAA,aAAU;;0DACT,8OAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAK,WAAU;kEAA2G;;;;;;oDAEpH;;;;;;;0DAGT,8OAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,oJAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAI,WAAU;kEAC9B,CAAC;2CACyC,CAAC;;;;;;;;;;;;0DAI9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAI,WAAU;kEAC9B,CAAC;;iDAE+C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAOxC,8OAAC,oJAAA,CAAA,OAAI;;kDACH,8OAAC,oJAAA,CAAA,aAAU;;0DACT,8OAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAK,WAAU;kEAA6G;;;;;;oDAEtH;;;;;;;0DAGT,8OAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,oJAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAI,WAAU;8DAC9B,CAAC;;;eAGa,CAAC;;;;;;;;;;;;;;;;;;;;;;;0CAON,8OAAC,oJAAA,CAAA,OAAI;;kDACH,8OAAC,oJAAA,CAAA,aAAU;;0DACT,8OAAC,oJAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAK,WAAU;kEAA6G;;;;;;oDAEtH;;;;;;;0DAGT,8OAAC,oJAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,oJAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAI,WAAU;kEAC9B,CAAC,WAAW,CAAC;;;;;;;;;;;;0DAIA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA6B;;;;;;0EAC3C,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9C,8OAAC,oJAAA,CAAA,OAAI;;kDACH,8OAAC,oJAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,oJAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,6MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;kDAItD,8OAAC,oJAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAG7B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAGN,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;0EAAO;;;;;;4DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtC,8OAAC,0JAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}