"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductPagesModule = void 0;
const common_1 = require("@nestjs/common");
const product_pages_service_1 = require("./product-pages.service");
const product_pages_controller_1 = require("./product-pages.controller");
let ProductPagesModule = class ProductPagesModule {
};
exports.ProductPagesModule = ProductPagesModule;
exports.ProductPagesModule = ProductPagesModule = __decorate([
    (0, common_1.Module)({
        controllers: [product_pages_controller_1.ProductPagesController],
        providers: [product_pages_service_1.ProductPagesService],
        exports: [product_pages_service_1.ProductPagesService],
    })
], ProductPagesModule);
//# sourceMappingURL=product-pages.module.js.map