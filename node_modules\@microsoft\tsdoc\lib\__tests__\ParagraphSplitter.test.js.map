{"version": 3, "file": "ParagraphSplitter.test.js", "sourceRoot": "", "sources": ["../../src/__tests__/ParagraphSplitter.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAC7F,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,IAAI,CAAC,8BAA8B,EAAE;IACnC,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,QAAQ;QACR,gBAAgB;QAChB,qBAAqB;QACrB,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,kCAAkC;QAClC,IAAI;QACJ,iCAAiC;QACjC,IAAI;QACJ,OAAO;QACP,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,wCAAwC,EAAE;IAC7C,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClG,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+BAA+B,EAAE;IACpC,WAAW,CAAC,+BAA+B,CACzC,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE,EAAE,uBAAuB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACpE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0CAA0C,EAAE;IAC/C,IAAM,aAAa,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IAEnE,IAAM,UAAU,GAAe,IAAI,UAAU,CAAC,EAAE,aAAa,eAAA,EAAE,EAAE;QAC/D,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,EAAE;YAClC,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;YACtD,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,CAAC;YACnC,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YAChD,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,CAAC;YACnC,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;YAClD,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YAChD,IAAI,WAAW,CAAC,EAAE,aAAa,eAAA,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;YACtD,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;YACtD,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,CAAC;YACnC,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,CAAC;YACnC,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;SACxD,CAAC;QACF,+CAA+C;QAC/C,IAAI,YAAY,CAAC,EAAE,aAAa,eAAA,EAAE,CAAC;KACpC,CAAC,CAAC;IAEH,iBAAiB,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;IACxD,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;AACvE,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { TestHelpers } from '../parser/__tests__/TestHelpers';\r\nimport { ParagraphSplitter } from '../parser/ParagraphSplitter';\r\nimport { DocSection, DocPlainText, DocSoftBreak, DocParagraph, DocBlockTag } from '../index';\r\nimport { TSDocConfiguration } from '../configuration/TSDocConfiguration';\r\n\r\ntest('01 Basic paragraph splitting', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' *    ',\r\n      ' * This is the',\r\n      ' * first paragraph.',\r\n      ' *   \\t   ',\r\n      ' *  ',\r\n      ' *   \\t   ',\r\n      ' * This is the second paragraph.',\r\n      ' *',\r\n      ' * This is the third paragraph.',\r\n      ' *',\r\n      ' *   ',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('02 Basic paragraph splitting in blocks', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    ['/**', ' * P1', ' * @remarks P2', ' *', ' * P3 @deprecated P4', ' *', ' * P5', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('03 Degenerate comment framing', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    ['/** line 1', ' * line 2', '', ' *   @public line 3*/'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 Degenerate manually constructed nodes', () => {\r\n  const configuration: TSDocConfiguration = new TSDocConfiguration();\r\n\r\n  const docSection: DocSection = new DocSection({ configuration }, [\r\n    new DocParagraph({ configuration }, [\r\n      new DocPlainText({ configuration, text: '  para 1 ' }),\r\n      new DocSoftBreak({ configuration }),\r\n      new DocPlainText({ configuration, text: '   ' }),\r\n      new DocSoftBreak({ configuration }),\r\n      new DocPlainText({ configuration, text: ' \\t  ' }),\r\n      new DocPlainText({ configuration, text: '   ' }),\r\n      new DocBlockTag({ configuration, tagName: '@public' }),\r\n      new DocPlainText({ configuration, text: '  para 2 ' }),\r\n      new DocSoftBreak({ configuration }),\r\n      new DocSoftBreak({ configuration }),\r\n      new DocPlainText({ configuration, text: '  para 3  ' })\r\n    ]),\r\n    // Currently we do not discard empty paragraphs\r\n    new DocParagraph({ configuration })\r\n  ]);\r\n\r\n  ParagraphSplitter.splitParagraphsForSection(docSection);\r\n  expect(TestHelpers.getDocNodeSnapshot(docSection)).toMatchSnapshot();\r\n});\r\n"]}