(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{65:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(4568),a=s(9344),l=s(2921),n=s(9080),i=s(7261),c=s.n(i),o=s(9088),d=s(7620);let u=d.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?d.createElement("title",{id:r},s):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var h=s(329);let m=d.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?d.createElement("title",{id:r},s):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});var x=s(516);function g(){var e;let{user:t}=(0,a.A)(),s=[{name:"إجمالي المتاجر",value:"2",icon:o.A,color:"bg-blue-500"},{name:"إجمالي المبيعات",value:"45,230 د.ج",icon:u,color:"bg-green-500"},{name:"الطلبات هذا الشهر",value:"12",icon:h.A,color:"bg-purple-500"},{name:"الزوار هذا الشهر",value:"1,234",icon:m,color:"bg-orange-500"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:["مرحباً، ",(null==t||null==(e=t.user_metadata)?void 0:e.firstName)||"صديقي","! \uD83D\uDC4B"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"إليك نظرة سريعة على أداء متاجرك الإلكترونية"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map(e=>(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"p-2 rounded-md ".concat(e.color),children:(0,r.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"mr-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.name}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]})]})})},e.name))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"متاجري الأخيرة"}),(0,r.jsx)(l.BT,{children:"المتاجر التي تم إنشاؤها أو تحديثها مؤخراً"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[[{id:"1",name:"متجر الإلكترونيات",slug:"electronics-store",status:"منشور",lastUpdated:"2024-01-15"},{id:"2",name:"متجر الأزياء",slug:"fashion-store",status:"مسودة",lastUpdated:"2024-01-10"}].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["/",e.slug]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:["آخر تحديث: ",e.lastUpdated]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("منشور"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.status}),(0,r.jsx)(c(),{href:"/dashboard/stores/".concat(e.id),children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",children:"إدارة"})})]})]},e.id)),(0,r.jsx)(c(),{href:"/dashboard/stores",children:(0,r.jsx)(n.$,{variant:"ghost",className:"w-full",children:"عرض جميع المتاجر"})})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"إجراءات سريعة"}),(0,r.jsx)(l.BT,{children:"الإجراءات الأكثر استخداماً"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c(),{href:"/dashboard/stores/new",children:(0,r.jsxs)(n.$,{className:"w-full justify-start",size:"lg",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 ml-2"}),"إنشاء متجر جديد"]})}),(0,r.jsx)(c(),{href:"/dashboard/products/new",children:(0,r.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",size:"lg",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 ml-2"}),"إضافة منتج جديد"]})}),(0,r.jsx)(c(),{href:"/dashboard/analytics",children:(0,r.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",size:"lg",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 ml-2"}),"عرض التحليلات"]})}),(0,r.jsx)(c(),{href:"/dashboard/settings",children:(0,r.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",size:"lg",children:"إعدادات الحساب"})})]})})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"البدء مع شاريو"}),(0,r.jsx)(l.BT,{children:"خطوات بسيطة لإطلاق متجرك الإلكتروني"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-blue-600 font-bold",children:"1"})}),(0,r.jsx)("h3",{className:"font-medium mb-2",children:"أنشئ متجرك"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"اختر اسماً وتصميماً لمتجرك"})]}),(0,r.jsxs)("div",{className:"text-center p-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-green-600 font-bold",children:"2"})}),(0,r.jsx)("h3",{className:"font-medium mb-2",children:"أضف منتجاتك"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"ارفع صور منتجاتك وأضف الأوصاف"})]}),(0,r.jsxs)("div",{className:"text-center p-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("span",{className:"text-purple-600 font-bold",children:"3"})}),(0,r.jsx)("h3",{className:"font-medium mb-2",children:"انشر وابدأ البيع"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"شارك رابط متجرك واستقبل الطلبات"})]})]})})]})]})}},329:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7620);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7620);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},2921:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>i});var r=s(4568),a=s(7620),l=s(3928);let n=a.forwardRef((e,t)=>{let{className:s,children:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm",s),...n,children:a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-gray-500",s),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},3928:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(2987),a=s(607);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},4884:(e,t,s)=>{Promise.resolve().then(s.bind(s,65))},9080:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(4568),a=s(7620),l=s(3928);let n=a.forwardRef((e,t)=>{let{className:s,variant:a="primary",size:n="md",loading:i,children:c,disabled:o,...d}=e;return(0,r.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[a],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[n],s),ref:t,disabled:o||i,...d,children:[i&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]})});n.displayName="Button"},9088:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7620);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))})},9344:(e,t,s)=>{"use strict";s.d(t,{O:()=>h,A:()=>m});var r=s(4568),a=s(7620);let l=(0,s(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),n=async()=>{let{error:e}=await l.auth.signOut();return{error:e}},i=async()=>{let{data:{user:e},error:t}=await l.auth.getUser();return{user:e,error:t}},c=s(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});c.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),c.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let o={validateToken:e=>c.post("/auth/validate",{token:e})};var d=s(5317);let u=(0,a.createContext)(void 0);function h(e){let{children:t}=e,[s,c]=(0,a.useState)(null),[h,m]=(0,a.useState)(!0),x=async()=>{try{let{user:e}=await i();if(c(e),e){let{data:{session:e}}=await l.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await o.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),c(null),localStorage.removeItem("auth_token")}};(0,a.useEffect)(()=>{x().finally(()=>m(!1));let{data:{subscription:e}}=l.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){c(t.user);try{let e=await o.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(c(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let g=async(e,t)=>{try{var s;let{data:r,error:a}=await l.auth.signInWithPassword({email:e,password:t});if(a)throw a;if(null==(s=r.session)?void 0:s.access_token){let e=await o.validateToken(r.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الدخول";throw d.Ay.error(e),t}},f=async(e,t,s)=>{try{let{error:r}=await l.auth.signUp({email:e,password:t,options:{data:s}});if(r)throw r;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(t){let e=t instanceof Error?t.message:"فشل في إنشاء الحساب";throw d.Ay.error(e),t}},p=async()=>{try{await n(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الخروج";throw d.Ay.error(e),t}};return(0,r.jsx)(u.Provider,{value:{user:s,loading:h,signIn:g,signUp:f,signOut:p,refreshUser:x},children:t})}function m(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[286,661,587,315,358],()=>t(4884)),_N_E=e.O()}]);