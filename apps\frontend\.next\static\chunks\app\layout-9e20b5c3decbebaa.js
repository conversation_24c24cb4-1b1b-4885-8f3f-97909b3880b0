(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3139:()=>{},5179:(e,t,a)=>{Promise.resolve().then(a.bind(a,6939)),Promise.resolve().then(a.t.bind(a,5408,23)),Promise.resolve().then(a.t.bind(a,3139,23))},6939:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>l});var r=a(4568),o=a(8299),s=a(7606),n=a(5317),i=a(9344),c=a(7620);function l(e){let{children:t}=e,[a]=(0,c.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,r.jsx)(s.Ht,{client:a,children:(0,r.jsxs)(i.O,{children:[t,(0,r.jsx)(n.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})}},9344:(e,t,a)=>{"use strict";a.d(t,{O:()=>d,A:()=>k});var r=a(4568),o=a(7620);let s=(0,a(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),n=async()=>{let{error:e}=await s.auth.signOut();return{error:e}},i=async()=>{let{data:{user:e},error:t}=await s.auth.getUser();return{user:e,error:t}},c=a(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});c.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),c.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let l={validateToken:e=>c.post("/auth/validate",{token:e})};var u=a(5317);let h=(0,o.createContext)(void 0);function d(e){let{children:t}=e,[a,c]=(0,o.useState)(null),[d,k]=(0,o.useState)(!0),y=async()=>{try{let{user:e}=await i();if(c(e),e){let{data:{session:e}}=await s.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await l.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),c(null),localStorage.removeItem("auth_token")}};(0,o.useEffect)(()=>{y().finally(()=>k(!1));let{data:{subscription:e}}=s.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){c(t.user);try{let e=await l.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(c(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let m=async(e,t)=>{try{var a;let{data:r,error:o}=await s.auth.signInWithPassword({email:e,password:t});if(o)throw o;if(null==(a=r.session)?void 0:a.access_token){let e=await l.validateToken(r.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}u.Ay.success("تم تسجيل الدخول بنجاح")}catch(e){throw u.Ay.error(e.message||"فشل في تسجيل الدخول"),e}},v=async(e,t,a)=>{try{let{data:r,error:o}=await s.auth.signUp({email:e,password:t,options:{data:a}});if(o)throw o;u.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(e){throw u.Ay.error(e.message||"فشل في إنشاء الحساب"),e}},p=async()=>{try{await n(),localStorage.removeItem("auth_token"),u.Ay.success("تم تسجيل الخروج بنجاح")}catch(e){throw u.Ay.error(e.message||"فشل في تسجيل الخروج"),e}};return(0,r.jsx)(h.Provider,{value:{user:a,loading:d,signIn:m,signUp:v,signOut:p,refreshUser:y},children:t})}function k(){let e=(0,o.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[171,286,173,587,315,358],()=>t(5179)),_N_E=e.O()}]);