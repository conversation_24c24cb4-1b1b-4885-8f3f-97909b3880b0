import { AnalyticsService } from './analytics.service';
import { Request as ExpressRequest } from 'express';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getDashboardMetrics(productPageId: string, req: ExpressRequest): Promise<{
        overview: {
            totalProducts: number;
            totalOrders: number;
            totalRevenue: number;
            ordersLast30Days: number;
            ordersLast7Days: number;
            revenueLast30Days: number;
            revenueLast7Days: number;
        };
        topProducts: {
            totalSold: number;
            orderCount: number;
            id?: string | undefined;
            name?: string | undefined;
            price?: import("@prisma/client/runtime/library").Decimal | undefined;
            imageUrl?: string | null | undefined;
        }[];
        recentOrders: ({
            items: ({
                product: {
                    id: string;
                    name: string;
                    imageUrl: string | null;
                };
            } & {
                id: string;
                name: string;
                price: import("@prisma/client/runtime/library").Decimal;
                productId: string;
                variantId: string | null;
                quantity: number;
                total: import("@prisma/client/runtime/library").Decimal;
                orderId: string;
            })[];
            buyer: {
                id: string;
                firstName: string | null;
                lastName: string | null;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            productPageId: string;
            customerInfo: import("@prisma/client/runtime/library").JsonValue;
            paymentMethod: string;
            shippingAddress: import("@prisma/client/runtime/library").JsonValue;
            billingAddress: import("@prisma/client/runtime/library").JsonValue | null;
            notes: string | null;
            shippingAmount: import("@prisma/client/runtime/library").Decimal;
            discountAmount: import("@prisma/client/runtime/library").Decimal;
            status: string;
            paymentStatus: string;
            orderNumber: string;
            buyerUserId: string | null;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
            subtotal: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
        })[];
    }>;
    getRevenueChart(productPageId: string, req: ExpressRequest, days?: string): Promise<{
        date: string;
        revenue: number;
    }[]>;
    getOrderStatusDistribution(productPageId: string, req: ExpressRequest): Promise<{
        status: string;
        count: number;
    }[]>;
    recordPageView(productPageId: string): Promise<void>;
    recordVisitor(productPageId: string): Promise<void>;
}
