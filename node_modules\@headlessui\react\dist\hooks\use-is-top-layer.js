import{useCallback as n,useId as u}from"react";import{stackMachines as p}from'../machines/stack-machine.js';import{useSlice as f}from'../react-glue.js';import{useIsoMorphicEffect as a}from'./use-iso-morphic-effect.js';function I(o,s){let t=u(),r=p.get(s),[i,c]=f(r,n(e=>[r.selectors.isTop(e,t),r.selectors.inStack(e,t)],[r,t]));return a(()=>{if(o)return r.actions.push(t),()=>r.actions.pop(t)},[r,o,t]),o?c?i:!0:!1}export{I as useIsTopLayer};
