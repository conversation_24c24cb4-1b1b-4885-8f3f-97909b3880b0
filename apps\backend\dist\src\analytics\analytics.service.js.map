{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../src/analytics/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAgE;AAChE,oEAAgE;AAChE,kFAA6E;AAGtE,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAEjB;IACA;IAFV,YACU,MAAqB,EACrB,mBAAwC;QADxC,WAAM,GAAN,MAAM,CAAe;QACrB,wBAAmB,GAAnB,mBAAmB,CAAqB;IAC/C,CAAC;IAEJ,KAAK,CAAC,mBAAmB,CAAC,aAAqB,EAAE,MAAc;QAE7D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEvF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,wDAAwD,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC3E,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAGzE,MAAM,CACJ,aAAa,EACb,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,WAAW,EACX,YAAY,EACb,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAEpB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACxB,KAAK,EAAE,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE;aACzC,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACtB,KAAK,EAAE,EAAE,aAAa,EAAE;aACzB,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC1B,KAAK,EAAE;oBACL,aAAa;oBACb,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE;iBACpE;gBACD,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;aAC5B,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACtB,KAAK,EAAE;oBACL,aAAa;oBACb,SAAS,EAAE,EAAE,GAAG,EAAE,aAAa,EAAE;iBAClC;aACF,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACtB,KAAK,EAAE;oBACL,aAAa;oBACb,SAAS,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE;iBACjC;aACF,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC1B,KAAK,EAAE;oBACL,aAAa;oBACb,SAAS,EAAE,EAAE,GAAG,EAAE,aAAa,EAAE;oBACjC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE;iBACpE;gBACD,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;aAC5B,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC1B,KAAK,EAAE;oBACL,aAAa;oBACb,SAAS,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE;oBAChC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE;iBACpE;gBACD,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;aAC5B,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC5B,EAAE,EAAE,CAAC,WAAW,CAAC;gBACjB,KAAK,EAAE;oBACL,KAAK,EAAE,EAAE,aAAa,EAAE;iBACzB;gBACD,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACxB,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC3B,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE;gBACvC,IAAI,EAAE,CAAC;aACR,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACzB,KAAK,EAAE,EAAE,aAAa,EAAE;gBACxB,OAAO,EAAE;oBACP,KAAK,EAAE;wBACL,OAAO,EAAE;4BACP,OAAO,EAAE;gCACP,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,IAAI,EAAE,IAAI;oCACV,QAAQ,EAAE,IAAI;iCACf;6BACF;yBACF;qBACF;oBACD,KAAK,EAAE;wBACL,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,SAAS,EAAE,IAAI;4BACf,QAAQ,EAAE,IAAI;yBACf;qBACF;iBACF;gBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;gBAC9B,IAAI,EAAE,EAAE;aACT,CAAC;SACH,CAAC,CAAC;QAGH,MAAM,sBAAsB,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9C,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YAC7B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;gBAC7B,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAC;YACH,OAAO;gBACL,GAAG,OAAO;gBACV,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;gBAClC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;aAClC,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;QAEF,OAAO;YACL,QAAQ,EAAE;gBACR,aAAa;gBACb,WAAW;gBACX,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC;gBACxD,gBAAgB;gBAChB,eAAe;gBACf,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC;gBAClE,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC;aACjE;YACD,WAAW,EAAE,sBAAsB;YACnC,YAAY;SACb,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,aAAqB,EAAE,MAAc,EAAE,OAAe,EAAE;QAE5E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEvF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,wDAAwD,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC9C,KAAK,EAAE;gBACL,aAAa;gBACb,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;gBAC7B,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE;aACpE;YACD,MAAM,EAAE;gBACN,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;aAClB;SACF,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACf,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC;YACD,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACvC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC;aACrC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,aAAqB,EAAE,MAAc;QAEpE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEvF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,wDAAwD,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YACnD,EAAE,EAAE,CAAC,QAAQ,CAAC;YACd,KAAK,EAAE,EAAE,aAAa,EAAE;YACxB,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;SACzB,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;SAC1B,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB;QACxC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE;gBACL,kBAAkB,EAAE;oBAClB,aAAa;oBACb,IAAI,EAAE,KAAK;iBACZ;aACF;YACD,MAAM,EAAE;gBACN,SAAS,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;aAC5B;YACD,MAAM,EAAE;gBACN,aAAa;gBACb,IAAI,EAAE,KAAK;gBACX,SAAS,EAAE,CAAC;gBACZ,QAAQ,EAAE,CAAC;aACZ;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,aAAqB;QACvC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE;gBACL,kBAAkB,EAAE;oBAClB,aAAa;oBACb,IAAI,EAAE,KAAK;iBACZ;aACF;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;aAC3B;YACD,MAAM,EAAE;gBACN,aAAa;gBACb,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;aACb;SACF,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAjRY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAGO,8BAAa;QACA,2CAAmB;GAHvC,gBAAgB,CAiR5B"}