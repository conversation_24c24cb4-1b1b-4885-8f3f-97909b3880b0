import { JwtService } from '@nestjs/jwt';
import { SupabaseService } from './supabase.service';
import { UsersService } from '../users/users.service';
export declare class AuthService {
    private jwtService;
    private supabaseService;
    private usersService;
    constructor(jwtService: JwtService, supabaseService: SupabaseService, usersService: UsersService);
    validateUser(token: string): Promise<any>;
    generateToken(user: any): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            firstName: any;
            lastName: any;
            role: any;
        };
    }>;
}
