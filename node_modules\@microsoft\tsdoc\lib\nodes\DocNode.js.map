{"version": 3, "file": "DocNode.js", "sourceRoot": "", "sources": ["../../src/nodes/DocNode.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAI3D;;;;;;;GAOG;AACH,MAAM,CAAN,IAAY,WA0BX;AA1BD,WAAY,WAAW;IACrB,8BAAe,CAAA;IACf,oCAAqB,CAAA;IACrB,kCAAmB,CAAA;IACnB,wCAAyB,CAAA;IACzB,oCAAqB,CAAA;IACrB,kCAAmB,CAAA;IACnB,4DAA6C,CAAA;IAC7C,sCAAuB,CAAA;IACvB,0CAA2B,CAAA;IAC3B,8CAA+B,CAAA;IAC/B,wCAAyB,CAAA;IACzB,4CAA6B,CAAA;IAC7B,8CAA+B,CAAA;IAC/B,sCAAuB,CAAA;IACvB,kCAAmB,CAAA;IACnB,oDAAqC,CAAA;IACrC,kDAAmC,CAAA;IACnC,gDAAiC,CAAA;IACjC,4CAA6B,CAAA;IAC7B,sCAAuB,CAAA;IACvB,wCAAyB,CAAA;IACzB,kDAAmC,CAAA;IACnC,sCAAuB,CAAA;IACvB,kCAAmB,CAAA;IACnB,sCAAuB,CAAA;AACzB,CAAC,EA1BW,WAAW,KAAX,WAAW,QA0BtB;AAkCD;;GAEG;AACH;IAGE,iBAAmB,UAAyD;QAC1E,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;IAChD,CAAC;IAQD;;;;OAIG;IACI,+BAAa,GAApB;QACE,kFAAkF;QAClF,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtE,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,KAAK,SAAS,EAAf,CAAe,CAA2B,CAAC;IACzF,CAAC;IAED;;;OAGG;IACO,iCAAe,GAAzB;QACE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;;;;;;;OAQG;IACW,0BAAkB,GAAhC,UACE,UAAyD;QAEzD,OAAQ,UAAuC,CAAC,MAAM,KAAK,IAAI,CAAC;IAClE,CAAC;IACH,cAAC;AAAD,CAAC,AA/CD,IA+CC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport type { TSDocConfiguration } from '../configuration/TSDocConfiguration';\r\n\r\n/**\r\n * Indicates the type of {@link DocNode}.\r\n *\r\n * @remarks\r\n * When creating custom subclasses of `DocNode`, it's recommended to create your own enum to identify them.\r\n * To avoid naming conflicts between projects, the enum value should be a string comprised of your full\r\n * NPM package name, followed by a \"#\" symbol, followed by the class name (without the \"Doc\" prefix).\r\n */\r\nexport enum DocNodeKind {\r\n  Block = 'Block',\r\n  BlockTag = 'BlockTag',\r\n  Excerpt = 'Excerpt',\r\n  FencedCode = 'FencedCode',\r\n  CodeSpan = 'CodeSpan',\r\n  Comment = 'Comment',\r\n  DeclarationReference = 'DeclarationReference',\r\n  ErrorText = 'ErrorText',\r\n  EscapedText = 'EscapedText',\r\n  HtmlAttribute = 'HtmlAttribute',\r\n  HtmlEndTag = 'HtmlEndTag',\r\n  HtmlStartTag = 'HtmlStartTag',\r\n  InheritDocTag = 'InheritDocTag',\r\n  InlineTag = 'InlineTag',\r\n  LinkTag = 'LinkTag',\r\n  MemberIdentifier = 'MemberIdentifier',\r\n  MemberReference = 'MemberReference',\r\n  MemberSelector = 'MemberSelector',\r\n  MemberSymbol = 'MemberSymbol',\r\n  Paragraph = 'Paragraph',\r\n  ParamBlock = 'ParamBlock',\r\n  ParamCollection = 'ParamCollection',\r\n  PlainText = 'PlainText',\r\n  Section = 'Section',\r\n  SoftBreak = 'SoftBreak'\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocNode}.\r\n *\r\n * @remarks\r\n * There are two scenarios for constructing `DocNode` objects.  The \"builder scenario\" constructs the object based on\r\n * literal strings, does NOT create DocExcerpt child nodes, and generally uses the `IDocNodeParameters`\r\n * hierarchy for its constructor parameters.  The \"parser scenario\" constructs the object by parsing a TypeScript\r\n * source file, does create DocExcerpt child nodes, and generally uses the {@link IDocNodeParsedParameters} hierarchy.\r\n */\r\nexport interface IDocNodeParameters {\r\n  configuration: TSDocConfiguration;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocNode}.\r\n *\r\n * @remarks\r\n * There are two scenarios for constructing `DocNode` objects.  The \"builder scenario\" constructs the object based on\r\n * literal strings, does NOT create DocExcerpt child nodes, and generally uses the {@link IDocNodeParameters}\r\n * hierarchy for its constructor parameters.  The \"parser scenario\" constructs the object by parsing a TypeScript\r\n * source file, does create DocExcerpt child nodes, and generally uses the `IDocNodeParsedParameters` hierarchy.\r\n */\r\nexport interface IDocNodeParsedParameters {\r\n  configuration: TSDocConfiguration;\r\n\r\n  /**\r\n   * This is a marker used by {@link DocNode.isParsedParameters} to determine whether the constructor was\r\n   * invoked using `IDocNodeParameters` (builder scenario) or `IDocNodeParsedParameters` (parser scenario).\r\n   */\r\n  parsed: true;\r\n}\r\n\r\n/**\r\n * The base class for the parser's Abstract Syntax Tree nodes.\r\n */\r\nexport abstract class DocNode {\r\n  public readonly configuration: TSDocConfiguration;\r\n\r\n  public constructor(parameters: IDocNodeParameters | IDocNodeParsedParameters) {\r\n    this.configuration = parameters.configuration;\r\n  }\r\n\r\n  /**\r\n   * Returns a text string that uniquely identifies the child class type.  This is used for example by\r\n   * switch statements to efficiently determine the kind of node.\r\n   */\r\n  public abstract get kind(): DocNodeKind | string;\r\n\r\n  /**\r\n   * Returns the list of child nodes for this node.  This is useful for visitors that want\r\n   * to scan the tree looking for nodes of a specific type, without having to process\r\n   * intermediary nodes.\r\n   */\r\n  public getChildNodes(): ReadonlyArray<DocNode> {\r\n    // Do this sanity check here, since the constructor cannot access abstract members\r\n    this.configuration.docNodeManager.throwIfNotRegisteredKind(this.kind);\r\n\r\n    return this.onGetChildNodes().filter((x) => x !== undefined) as ReadonlyArray<DocNode>;\r\n  }\r\n\r\n  /**\r\n   * Overridden by child classes to implement {@link DocNode.getChildNodes}.\r\n   * @virtual\r\n   */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * A type guard that returns true if the input uses the `IDocNodeParsedParameters` (parser scenario).\r\n   *\r\n   * @remarks\r\n   * There are two scenarios for constructing `DocNode` objects.  The \"builder scenario\" constructs the object based on\r\n   * literal strings, does NOT create DocExcerpt child nodes, and generally uses the {@link IDocNodeParameters}\r\n   * hierarchy for its constructor parameters.  The \"parser scenario\" constructs the object by parsing a TypeScript\r\n   * source file, does create DocExcerpt child nodes, and generally uses the {@link IDocNodeParsedParameters} hierarchy.\r\n   */\r\n  public static isParsedParameters(\r\n    parameters: IDocNodeParameters | IDocNodeParsedParameters\r\n  ): parameters is IDocNodeParsedParameters {\r\n    return (parameters as IDocNodeParsedParameters).parsed === true;\r\n  }\r\n}\r\n"]}