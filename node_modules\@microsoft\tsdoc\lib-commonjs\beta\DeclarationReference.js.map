{"version": 3, "file": "DeclarationReference.js", "sourceRoot": "", "sources": ["../../src/beta/DeclarationReference.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,4DAA4D;AAC5D,yDAAyD;AAEzD,yFAAyF;AAEzF,uDAAsD;AAEtD;;;GAGG;AACH;IAKE,8BACE,MAAoC,EACpC,UAAmD,EACnD,MAAwB;QAExB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,sBAAW,wCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAED,sBAAW,4CAAU;aAArB;YACE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,KAAK,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC3C,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YACD,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACnC,OAAO,UAAU,CAAC,OAAO,CAAC;YAC5B,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAED,sBAAW,wCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAED,sBAAW,yCAAO;aAAlB;YACE,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;QAChE,CAAC;;;OAAA;IAEa,0BAAK,GAAnB,UAAoB,IAAY;QAC9B,IAAM,MAAM,GAAW,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;QACxC,IAAM,SAAS,GAAyB,MAAM,CAAC,yBAAyB,EAAE,CAAC;QAC3E,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,IAAI,WAAW,CAAC,wCAAiC,IAAI,mBAAS,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAE,CAAC,CAAC;QACpG,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAChB,MAAM,IAAI,WAAW,CAAC,wCAAiC,IAAI,MAAG,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEa,mCAAc,GAA5B,UAA6B,IAAY;QACvC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpB,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACW,gDAA2B,GAAzC,UAA0C,IAAY;QACpD,IAAM,OAAO,GAAY,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3C,OAAO,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,MAAM;YACpC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,QAAQ;YACnC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,IAAI;gBAC9B,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,QAAQ;gBACnC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACW,0CAAqB,GAAnC,UAAoC,IAAY;QAC9C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAM,EAAE,GAAW,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;YACxE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACW,4CAAuB,GAArC,UAAsC,IAAY;QAChD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACvF,IAAI,CAAC;gBACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YAAC,WAAM,CAAC;gBACP,MAAM,IAAI,WAAW,CAAC,6BAAsB,IAAI,MAAG,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,WAAW,CAAC,6BAAsB,IAAI,MAAG,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACW,mDAA8B,GAA5C,UAA6C,IAAY;QACvD,IAAM,OAAO,GAAY,IAAI,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;QACjD,OAAO,CACL,OAAO,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,YAAY;YACnD,CAAC,OAAO,CAAC,oBAAoB;YAC7B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,QAAQ,CAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACW,6CAAwB,GAAtC,UAAuC,IAAY;QACjD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAM,EAAE,GAAW,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACW,+CAA0B,GAAxC,UAAyC,IAAY;QACnD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACvF,IAAI,CAAC;gBACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YAAC,WAAM,CAAC;gBACP,MAAM,IAAI,WAAW,CAAC,iCAA0B,IAAI,MAAG,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,WAAW,CAAC,iCAA0B,IAAI,MAAG,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEa,0BAAK,GAAnB;QACE,OAAO,IAAI,oBAAoB,EAAE,CAAC;IACpC,CAAC;IAEa,4BAAO,GAArB,UAAsB,WAAmB,EAAE,UAAmB;QAC5D,OAAO,IAAI,oBAAoB,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;IACrF,CAAC;IAEa,2BAAM,GAApB,UAAqB,IAAY,EAAE,WAAqB;QACtD,OAAO,IAAI,oBAAoB,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;IACvE,CAAC;IAEa,2BAAM,GAApB;QACE,OAAO,IAAI,oBAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAEa,yBAAI,GAAlB,UAAmB,IAAsC;QACvD,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAEM,yCAAU,GAAjB,UAAkB,MAA+C;QAC/D,OAAO,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3G,CAAC;IAEM,6CAAc,GAArB,UACE,UAA8D;QAE9D,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU;YACpC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACvE,CAAC;IAEM,yCAAU,GAAjB,UAAkB,MAAmC;QACnD,OAAO,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IAC3G,CAAC;IAEM,gDAAiB,GAAxB,UAAyB,aAA4B;QACnD,OAAO,IAAI,CAAC,UAAU,CACpB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,aAAa,CAAC,CAChG,CAAC;IACJ,CAAC;IAEM,0CAAW,GAAlB,UAAmB,OAA4B;QAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACvE,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEM,gDAAiB,GAAxB,UAAyB,aAAiC;QACxD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC;QACnF,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC;IACvE,CAAC;IAEM,gDAAiB,GAAxB,UAAyB,UAAsB,EAAE,SAAwB;QACvE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;QAC/E,CAAC;QACD,IAAI,UAAU,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC;YACtC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC;QAClC,CAAC;QACD,IAAM,MAAM,GAAoB,IAAI,eAAe,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClG,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAEM,uCAAQ,GAAf;QACE,IAAM,UAAU,GACd,IAAI,CAAC,OAAO,YAAY,YAAY,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,MAAM;YAC3F,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,EAAE,CAAC;QACT,OAAO,UAAG,IAAI,CAAC,MAAM,IAAI,EAAE,SAAG,UAAU,SAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAE,CAAC;IACjE,CAAC;IACH,2BAAC;AAAD,CAAC,AArOD,IAqOC;AArOY,oDAAoB;AAuOjC;;;GAGG;AACH,IAAY,UAIX;AAJD,WAAY,UAAU;IACpB,2BAAa,CAAA;IACb,2BAAa,CAAA;IACb,0BAAY,CAAA;AACd,CAAC,EAJW,UAAU,0BAAV,UAAU,QAIrB;AAED;;;GAGG;AACH;IAME,sBAAmB,IAAY,EAAE,WAA2B;QAA3B,4BAAA,EAAA,kBAA2B;QAC1D,IAAI,CAAC,WAAW;YACd,IAAI,YAAY,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAC9F,CAAC;IAED,sBAAW,8BAAI;aAAf;YACE,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,oBAAoB,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACxG,CAAC;;;OAAA;IAED,sBAAW,qCAAW;aAAtB;YACE,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC,WAAW,CAAC;QACtD,CAAC;;;OAAA;IAED,sBAAW,mCAAS;aAApB;YACE,IAAM,SAAS,GAAW,IAAI,CAAC,yBAAyB,EAAE,CAAC,SAAS,CAAC;YACrE,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1C,CAAC;;;OAAA;IAED,sBAAW,6CAAmB;aAA9B;YACE,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC,mBAAmB,CAAC;QAC9D,CAAC;;;OAAA;IAED,sBAAW,oCAAU;aAArB;YACE,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC;QAC3D,CAAC;;;OAAA;IAEa,8BAAiB,GAA/B,UACE,SAA6B,EAC7B,mBAA2B,EAC3B,UAAmB;QAEnB,IAAI,WAAW,GAAW,mBAAmB,CAAC;QAC9C,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAChC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC;YACD,WAAW,GAAG,WAAI,SAAS,cAAI,mBAAmB,CAAE,CAAC;QACvD,CAAC;QAED,IAAM,MAAM,GAAmB,EAAE,WAAW,aAAA,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,EAAE,mBAAmB,qBAAA,EAAE,CAAC;QAChG,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAChE,CAAC;IAEa,wBAAW,GAAzB,UAA0B,WAAmB,EAAE,UAAmB;QAChE,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IACvF,CAAC;IAEc,6BAAgB,GAA/B,UACE,MAA6B,EAC7B,WAAmB,EACnB,UAAmB;QAEnB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,IAAM,gBAAgB,GAAuB,2BAAY,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;QACnG,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,WAAW,CAAC,oCAA6B,gBAAgB,CAAE,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,GAAW,WAAW,CAAC;QAC/B,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,WAAW,CAAC,+BAAwB,UAAU,CAAE,CAAC,CAAC;YAC9D,CAAC;YACD,IAAI,IAAI,GAAG,GAAG,UAAU,CAAC;YACzB,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;QACjC,CAAC;QAED,IAAM,MAAM,GAAiB,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC;QAChC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,+BAAQ,GAAf;QACE,OAAO,UAAG,IAAI,CAAC,WAAW,MAAG,CAAC;IAChC,CAAC;IAEO,gDAAyB,GAAjC;QACE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAM,IAAI,GAAW,IAAI,CAAC,IAAI,CAAC;YAC/B,IAAM,MAAM,GAA0B,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC7D,IAAI,MAAM,IAAI,CAAC,2BAAY,CAAC,2BAA2B,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5E,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,eAAe,GAAG;oBACrB,WAAW,EAAE,EAAE;oBACf,SAAS,EAAE,EAAE;oBACb,mBAAmB,EAAE,EAAE;oBACvB,UAAU,EAAE,IAAI;iBACjB,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IACH,mBAAC;AAAD,CAAC,AAtGD,IAsGC;AAtGY,oCAAY;AAwGzB;IAAiC,sCAAY;IAA7C;;IAA+C,CAAC;IAAD,yBAAC;AAAD,CAAC,AAAhD,CAAiC,YAAY,GAAG;AAEhD,yBAAyB;AACzB,oEAAoE;AACpE,oEAAoE;AACpE,gFAAgF;AAChF,gFAAgF;AAChF,kBAAkB;AAClB,QAAQ;AACR,SAAS;AACT,cAAc;AACd,kBAAkB;AAClB,0CAA0C;AAC1C,kDAAkD;AAClD,iCAAiC;AACjC,wCAAwC;AACxC,IAAM,iBAAiB,GAAW,yCAAyC,CAAC;AAE5E,0BAA0B;AAC1B,4BAA4B;AAC5B,wBAAwB;AACxB,kBAAkB;AAClB,IAAM,uBAAuB,GAAW,yBAAyB,CAAC;AASlE,kDAAkD;AAClD,SAAS,gBAAgB,CAAC,IAAY;IACpC,IAAM,KAAK,GAA2B,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnE,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IACQ,IAAA,KAA2F,KAAK,GAAhF,EAAhB,WAAW,mBAAG,EAAE,KAAA,EAAE,KAAyE,KAAK,GAAhE,EAAd,SAAS,mBAAG,EAAE,KAAA,EAAE,KAAyD,KAAK,GAAtC,EAAxB,mBAAmB,mBAAG,EAAE,KAAA,EAAE,UAAU,GAAqB,KAAK,GAA1B,CAA2B;IAC1G,OAAO,EAAE,WAAW,aAAA,EAAE,SAAS,WAAA,EAAE,mBAAmB,qBAAA,EAAE,UAAU,YAAA,EAAE,CAAC;AACrE,CAAC;AAED;;;GAGG;AACH;IAGE;IAAuB,CAAC;IAEjB,+BAAQ,GAAf;QACE,OAAO,GAAG,CAAC;IACb,CAAC;IANsB,qBAAQ,GAAiB,IAAI,YAAY,EAAE,CAAC;IAOrE,mBAAC;CAAA,AARD,IAQC;AARY,oCAAY;AAezB;;GAEG;AACH,2DAA2D;AAC3D,IAAiB,SAAS,CAUzB;AAVD,WAAiB,SAAS;IACxB,SAAgB,IAAI,CAAC,KAAoB;QACvC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,KAAK,YAAY,oBAAoB,EAAE,CAAC;YAC1C,OAAO,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IARe,cAAI,OAQnB,CAAA;AACH,CAAC,EAVgB,SAAS,yBAAT,SAAS,QAUzB;AAOD;;GAEG;AACH;IAGE,yBAAmB,IAAY,EAAE,WAAqB;QACpD,IAAI,CAAC,IAAI,GAAG,IAAI,YAAY,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACxG,CAAC;IAEM,kCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IACH,sBAAC;AAAD,CAAC,AAVD,IAUC;AAVY,0CAAe;AAY5B;IAAoC,yCAAe;IAAnD;;IAAqD,CAAC;IAAD,4BAAC;AAAD,CAAC,AAAtD,CAAoC,eAAe,GAAG;AAEtD;;GAEG;AACH;IAGE,4BAAmB,SAA+B;QAChD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEa,wBAAK,GAAnB,UAAoB,IAAY;QAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACtF,OAAO,IAAI,kBAAkB,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC;QACD,MAAM,IAAI,WAAW,CAAC,wCAAiC,IAAI,MAAG,CAAC,CAAC;IAClE,CAAC;IAEM,0CAAa,GAApB,UAAqB,SAA+B;QAClD,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;IACjF,CAAC;IAEM,qCAAQ,GAAf;QACE,OAAO,WAAI,IAAI,CAAC,SAAS,MAAG,CAAC;IAC/B,CAAC;IACH,yBAAC;AAAD,CAAC,AArBD,IAqBC;AArBY,gDAAkB;AA4B/B;;GAEG;AACH;IAGE,2BAAmB,SAAoB;QACrC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEM,6CAAiB,GAAxB,UAEE,UAAsB,EACtB,SAAwB;QAExB,iDAAiD;QACjD,OAAO,IAAI,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC9E,CAAC;IAGH,wBAAC;AAAD,CAAC,AAjBD,IAiBC;AAjBqB,8CAAiB;AAmBvC;;GAEG;AACH;IAAmC,iCAAiB;IAApD;;IAQA,CAAC;IAPQ,qCAAa,GAApB,UAAqB,SAAwB;QAC3C,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5F,CAAC;IAEM,gCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IACH,oBAAC;AAAD,CAAC,AARD,CAAmC,iBAAiB,GAQnD;AARY,sCAAa;AAU1B;;GAEG;AACH;IAAyC,uCAAiB;IAIxD,6BAAmB,MAAqB,EAAE,UAAsB,EAAE,SAAoB;QACpF,YAAA,MAAK,YAAC,SAAS,CAAC,SAAC;QACjB,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC;;IAC/B,CAAC;IAEM,wCAAU,GAAjB,UAAkB,MAAqB;QACrC,OAAO,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1G,CAAC;IAEM,4CAAc,GAArB,UAAsB,UAAsB;QAC1C,OAAO,IAAI,CAAC,UAAU,KAAK,UAAU;YACnC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACvE,CAAC;IAEM,2CAAa,GAApB,UAAqB,SAAwB;QAC3C,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS;YACjC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACvF,CAAC;IAEM,sCAAQ,GAAf;QACE,OAAO,UAAG,IAAI,CAAC,MAAM,SAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,SAAG,IAAI,CAAC,SAAS,CAAE,CAAC;IAC/E,CAAC;IACH,0BAAC;AAAD,CAAC,AA7BD,CAAyC,iBAAiB,GA6BzD;AA7BY,kDAAmB;AA+BhC;;GAEG;AACH,IAAY,OAeX;AAfD,WAAY,OAAO;IACjB,0BAAe,CAAA;IACf,kCAAuB,CAAA;IACvB,6BAAkB,CAAA;IAClB,wBAAa,CAAA;IACb,kCAAuB,CAAA;IACvB,gCAAqB,CAAA;IACrB,2BAAgB,CAAA;IAChB,sCAA2B,CAAA;IAC3B,4BAAiB,CAAA;IACjB,0BAAe,CAAA;IACf,iCAAsB,CAAA;IACtB,qCAA0B,CAAA;IAC1B,mCAAwB,CAAA;IACxB,kCAAuB,CAAA,CAAC,mBAAmB;AAC7C,CAAC,EAfW,OAAO,uBAAP,OAAO,QAelB;AAUD;;;GAGG;AACH;IAKE,yBACE,SAAoC,EACpC,EAAwD;YAAxD,qBAAsD,EAAE,KAAA,EAAtD,OAAO,aAAA,EAAE,aAAa,mBAAA;QAExB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAEa,qBAAK,GAAnB;QACE,OAAO,IAAI,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAEM,2CAAiB,GAAxB,UAAyB,aAAwC;QAC/D,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;YACzC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,eAAe,CAAC,aAAa,EAAE;gBACjC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC;IACT,CAAC;IAEM,qCAAW,GAAlB,UAAmB,OAA4B;QAC7C,OAAO,IAAI,CAAC,OAAO,KAAK,OAAO;YAC7B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE;gBACtC,OAAO,SAAA;gBACP,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC;IACT,CAAC;IAEM,2CAAiB,GAAxB,UAAyB,aAAiC;QACxD,OAAO,IAAI,CAAC,aAAa,KAAK,aAAa;YACzC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE;gBACtC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,aAAa,eAAA;aACd,CAAC,CAAC;IACT,CAAC;IAEM,2CAAiB,GAAxB,UAAyB,UAAsB,EAAE,SAAwB;QACvE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,CAAC;QACD,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1F,CAAC;IAEM,kCAAQ,GAAf;QACE,IAAI,MAAM,GAAW,UAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAE,CAAC;QACnD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACrD,MAAM,IAAI,WAAI,IAAI,CAAC,OAAO,cAAI,IAAI,CAAC,aAAa,MAAG,CAAC;QACtD,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,IAAI,WAAI,IAAI,CAAC,OAAO,CAAE,CAAC;QAC/B,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5C,MAAM,IAAI,WAAI,IAAI,CAAC,aAAa,CAAE,CAAC;QACrC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACH,sBAAC;AAAD,CAAC,AA/DD,IA+DC;AA/DY,0CAAe;AAiE5B,IAAW,KAoCV;AApCD,WAAW,KAAK;IACd,iCAAI,CAAA;IACJ,yCAAQ,CAAA;IACR,aAAa;IACb,qDAAc,CAAA;IACd,uDAAe,CAAA;IACf,qDAAc,CAAA;IACd,uDAAe,CAAA;IACf,yDAAgB,CAAA;IAChB,2DAAiB,CAAA;IACjB,yDAAgB,CAAA;IAChB,yCAAQ,CAAA;IACR,4CAAS,CAAA;IACT,8CAAU,CAAA;IACV,8CAAU,CAAA;IACV,8CAAU,CAAA;IACV,wCAAO,CAAA;IACP,oDAAa,CAAA;IACb,sCAAM,CAAA;IACN,kCAAI,CAAA;IACJ,kDAAY,CAAA;IACZ,WAAW;IACX,kDAAY,CAAA;IACZ,0DAAgB,CAAA;IAChB,gDAAW,CAAA;IACX,gDAAW,CAAA;IACX,0DAAgB,CAAA;IAChB,wDAAe,CAAA;IACf,8CAAU,CAAA;IACV,8DAAkB,CAAA;IAClB,oDAAa,CAAA;IACb,kDAAY,CAAA;IACZ,gDAAW,CAAA;IACX,8CAAU,CAAA;IACV,kDAAY,CAAA;IACZ,sDAAc,CAAA,CAAC,YAAY;AAC7B,CAAC,EApCU,KAAK,KAAL,KAAK,QAoCf;AAED,SAAS,aAAa,CAAC,KAAY;IACjC,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,KAAK,CAAC,cAAc;YACvB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,eAAe;YACxB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,cAAc;YACvB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,eAAe;YACxB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,gBAAgB;YACzB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,iBAAiB;YAC1B,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,gBAAgB;YACzB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,QAAQ;YACjB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,SAAS;YAClB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,UAAU;YACnB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,UAAU;YACnB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,UAAU;YACnB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,OAAO;YAChB,OAAO,GAAG,CAAC;QACb,KAAK,KAAK,CAAC,YAAY;YACrB,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK,CAAC,gBAAgB;YACzB,OAAO,WAAW,CAAC;QACrB,KAAK,KAAK,CAAC,WAAW;YACpB,OAAO,MAAM,CAAC;QAChB,KAAK,KAAK,CAAC,WAAW;YACpB,OAAO,MAAM,CAAC;QAChB,KAAK,KAAK,CAAC,gBAAgB;YACzB,OAAO,WAAW,CAAC;QACrB,KAAK,KAAK,CAAC,eAAe;YACxB,OAAO,UAAU,CAAC;QACpB,KAAK,KAAK,CAAC,UAAU;YACnB,OAAO,KAAK,CAAC;QACf,KAAK,KAAK,CAAC,kBAAkB;YAC3B,OAAO,aAAa,CAAC;QACvB,KAAK,KAAK,CAAC,aAAa;YACtB,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK,CAAC,YAAY;YACrB,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK,CAAC,WAAW;YACpB,OAAO,MAAM,CAAC;QAChB,KAAK,KAAK,CAAC,UAAU;YACnB,OAAO,KAAK,CAAC;QACf,KAAK,KAAK,CAAC,YAAY;YACrB,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK,CAAC,cAAc;YACvB,OAAO,SAAS,CAAC;QACnB,KAAK,KAAK,CAAC,IAAI;YACb,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK,CAAC,QAAQ;YACjB,OAAO,OAAO,CAAC;QACjB,KAAK,KAAK,CAAC,aAAa;YACtB,OAAO,kBAAkB,CAAC;QAC5B,KAAK,KAAK,CAAC,MAAM;YACf,OAAO,UAAU,CAAC;QACpB,KAAK,KAAK,CAAC,IAAI;YACb,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK,CAAC,YAAY;YACrB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;AACH,CAAC;AAED;IAOE,iBAAmB,IAAY;QAC7B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,sBAAW,yCAAoB;aAA/B;YACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;;;OAAA;IAED,sBAAW,yBAAI;aAAf;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAED,sBAAW,8BAAS;aAApB;YACE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;;;OAAA;IAED,sBAAW,wBAAG;aAAd;YACE,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACxC,CAAC;;;OAAA;IAEM,uBAAK,GAAZ;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,2BAAS,GAAhB,UAAoB,EAA6B;QAC/C,IAAM,QAAQ,GAAW,IAAI,CAAC,SAAS,CAAC;QACxC,IAAM,GAAG,GAAW,IAAI,CAAC,IAAI,CAAC;QAC9B,IAAM,IAAI,GAAW,IAAI,CAAC,KAAK,CAAC;QAChC,IAAM,KAAK,GAAU,IAAI,CAAC,MAAM,CAAC;QACjC,IAAM,oBAAoB,GAAY,IAAI,CAAC,qBAAqB,CAAC;QACjE,IAAI,QAAQ,GAAY,KAAK,CAAC;QAC9B,IAAI,CAAC;YACH,IAAM,MAAM,GAAe;gBACzB,QAAQ,GAAG,IAAI,CAAC;YAClB,CAAC,CAAC;YACF,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;gBAChB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;gBACpB,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC;IAEM,sBAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACjB,IAAM,EAAE,GAAW,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAClD,QAAQ,EAAE,EAAE,CAAC;oBACX,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;oBAC9C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;oBAC/C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;oBAC9C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;oBAC/C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBAChD,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC;oBACjD,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBAChD,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACxC,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;oBACzC,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;oBAC1C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;oBAC1C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;oBAC1C,KAAK,GAAG;wBACN,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;oBACvC,KAAK,GAAG;wBACN,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;oBACtC;wBACE,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChB,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAEM,oCAAkB,GAAzB;QAAA,iBA2CC;QA1CC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,KAAK,CAAC,YAAY,CAAC;YACxB,KAAK,KAAK,CAAC,gBAAgB,CAAC;YAC5B,KAAK,KAAK,CAAC,QAAQ;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;QACvB,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAC,MAAM;YAC3B,IAAI,CAAC,KAAI,CAAC,GAAG,EAAE,CAAC;gBACd,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,SAAS,CAAC;gBAC3B,KAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;gBACnC,IAAI,OAAO,GAAgC,MAAM,CAAC;gBAClD,OAAO,CAAC,KAAI,CAAC,GAAG,EAAE,CAAC;oBACjB,IAAM,EAAE,GAAW,KAAI,CAAC,KAAK,CAAC,KAAI,CAAC,IAAI,CAAC,CAAC;oBACzC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;wBACf,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;4BACvB,OAAO,KAAI,CAAC,MAAM,CAAC;wBACrB,CAAC;wBACD,MAAM,EAAE,CAAC;wBACT,OAAO,CAAC,KAAI,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;oBAC5C,CAAC;oBACD,KAAI,CAAC,IAAI,EAAE,CAAC;oBACZ,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;wBACf,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;4BACxB,0DAA0D;4BAC1D,OAAO,KAAI,CAAC,MAAM,CAAC;wBACrB,CAAC;wBACD,OAAO,GAAG,QAAQ,CAAC;wBACnB,KAAI,CAAC,UAAU,EAAE,CAAC;oBACpB,CAAC;yBAAM,CAAC;wBACN,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;4BACzB,uCAAuC;4BACvC,OAAO,KAAI,CAAC,MAAM,CAAC;wBACrB,CAAC;wBACD,OAAO,GAAG,OAAO,CAAC;wBAClB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC;4BACtB,KAAI,CAAC,QAAQ,EAAE,CAAC;wBAClB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,KAAI,CAAC,MAAM,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,+BAAa,GAApB;QACE,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAM,SAAS,GAAW,IAAI,CAAC,SAAS,CAAC;YACzC,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,OAAO;oBACV,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC5C,KAAK,WAAW;oBACd,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChD,KAAK,MAAM;oBACT,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC3C,KAAK,MAAM;oBACT,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC3C,KAAK,WAAW;oBACd,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAChD,KAAK,UAAU;oBACb,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC/C,KAAK,KAAK;oBACR,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC1C,KAAK,aAAa;oBAChB,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBAClD,KAAK,QAAQ;oBACX,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;gBAC7C,KAAK,OAAO;oBACV,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC5C,KAAK,MAAM;oBACT,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC3C,KAAK,KAAK;oBACR,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC1C,KAAK,OAAO;oBACV,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;gBAC5C,KAAK,SAAS;oBACZ,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,qCAAmB,GAA1B;QACE,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAM,SAAS,GAAW,IAAI,CAAC,SAAS,CAAC;YACzC,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEO,4BAAU,GAAlB;QACE,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACjB,IAAM,EAAE,GAAW,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAClD,QAAQ,EAAE,EAAE,CAAC;gBACX,KAAK,GAAG;oBACN,OAAO;gBACT,KAAK,IAAI;oBACP,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,MAAM;gBACR;oBACE,IAAI,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;wBACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;wBAClC,OAAO;oBACT,CAAC;YACL,CAAC;QACH,CAAC;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACpC,CAAC;IAEO,oCAAkB,GAA1B;QACE,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,OAAO;QACT,CAAC;QAED,IAAM,EAAE,GAAW,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhD,2CAA2C;QAC3C,IAAI,yBAAyB,CAAC,EAAE,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,mDAAmD;QACnD,IACE,EAAE,KAAK,GAAG;YACV,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAC1F,CAAC;YACD,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,qCAAqC;QACrC,IACE,EAAE,KAAK,GAAG;YACV,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;YAClC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAC5C,CAAC;YACD,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YACf,OAAO;QACT,CAAC;QAED,yCAAyC;QACzC,yCAAyC;QACzC,IACE,EAAE,KAAK,GAAG;YACV,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;YAClC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YAC5C,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAC5C,CAAC;YACD,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YACf,OAAO;QACT,CAAC;QAED,yCAAyC;QACzC,gDAAgD;QAChD,IAAI,EAAE,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACjG,IAAI,SAAS,GAAW,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;YACzD,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1B,KAAK,IAAI,CAAC,GAAW,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC/D,IAAM,GAAG,GAAW,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACzC,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;wBAChB,IAAM,EAAE,GAAW,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;wBAC3C,IAAI,EAAE,IAAI,QAAQ,EAAE,CAAC;4BACnB,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;4BAClB,OAAO;wBACT,CAAC;wBACD,MAAM;oBACR,CAAC;oBACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACrB,SAAS,IAAI,GAAG,CAAC;wBACjB,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACpC,CAAC;IAEO,0BAAQ,GAAhB;QACE,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACrC,IAAM,EAAE,GAAW,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,IAAI,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;gBACnC,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IACH,cAAC;AAAD,CAAC,AAxSD,IAwSC;AAED;IAIE,gBAAmB,IAAY;QAC7B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,sBAAW,uBAAG;aAAd;YACE,OAAO,IAAI,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;QACzC,CAAC;;;OAAA;IAED,sBAAW,0BAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IAEM,0CAAyB,GAAhC;QACE,IAAI,MAA+C,CAAC;QACpD,IAAI,UAAyC,CAAC;QAC9C,IAAI,MAAmC,CAAC;QACxC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC/C,6BAA6B;YAC7B,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC;QACjC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,YAAY,EAAE,CAAC;YACrE,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC,2CAA2C;YAC3C,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;YACjC,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC9B,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,UAAU,EAAE,CAAC;YAC7C,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,aAAa,CAAC,IAAI,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClG,CAAC;QACD,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAEM,wCAAuB,GAA9B;QACE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IACpE,CAAC;IAEM,qCAAoB,GAA3B;QACE,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;YAC9B,KAAK,KAAK,CAAC,MAAM;gBACf,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B;gBACE,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC3C,CAAC;IACH,CAAC;IAEO,sBAAK,GAAb;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAEO,kCAAiB,GAAzB;QACE,IAAM,MAAM,GAAW,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACzC,OAAO,IAAI,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAEO,4BAAW,GAAnB;QACE,IAAM,SAAS,GAAkB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAEO,gCAAe,GAAvB,UAAwB,SAAwB;QAC9C,IAAI,OAA4B,CAAC;QACjC,IAAI,aAAiC,CAAC;QACtC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACjC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,EAAE,OAAO,SAAA,EAAE,aAAa,eAAA,EAAE,CAAC,CAAC;IACpE,CAAC;IAEO,mCAAkB,GAA1B;QACE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,IAAI,CACd,oBAAoB,EACpB,IAAI,aAAa,CAAC,IAAI,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC,CACjE,CAAC;QACJ,CAAC;QAED,IAAM,SAAS,GAAc,IAAI,CAAC,cAAc,EAAE,CAAC;QACnD,OAAO,IAAI,aAAa,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAEO,mCAAkB,GAA1B,UAA2B,SAAwB;QACjD,SAAS,CAAC;YACR,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;gBACrB,KAAK,KAAK,CAAC,QAAQ,CAAC;gBACpB,KAAK,KAAK,CAAC,SAAS,CAAC;gBACrB,KAAK,KAAK,CAAC,UAAU;oBACnB,IAAM,UAAU,GAAe,IAAI,CAAC,eAAe,EAAE,CAAC;oBACtD,IAAM,KAAK,GAAc,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC/C,SAAS,GAAG,IAAI,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;oBAClE,MAAM;gBACR;oBACE,OAAO,SAAS,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gCAAe,GAAvB;QACE,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;YAC9B,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,UAAU,CAAC,OAAO,CAAC;YAC5B,CAAC;YAED,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,UAAU,CAAC,OAAO,CAAC;YAC5B,CAAC;YAED,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,UAAU,CAAC,MAAM,CAAC;YAC3B,CAAC;YAED,OAAO,CAAC,CAAC,CAAC;gBACR,OAAO,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gCAAe,GAAvB;QACE,QAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC;YACtC,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,KAAK,CAAC;YACvB,CAAC;YAED,KAAK,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,SAAS,CAAC;YAC3B,CAAC;YAED,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;gBACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,SAAS,CAAC;YAC3B,CAAC;YAED,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;gBACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,IAAI,CAAC;YACtB,CAAC;YAED,KAAK,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,SAAS,CAAC;YAC3B,CAAC;YAED,KAAK,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;gBAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAC1B,CAAC;YAED,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAC1B,CAAC;YAED,KAAK,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,WAAW,CAAC;YAC7B,CAAC;YAED,KAAK,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;gBACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,MAAM,CAAC;YACxB,CAAC;YAED,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,KAAK,CAAC;YACvB,CAAC;YAED,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;gBACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,aAAa,CAAC;YAC/B,CAAC;YAED,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,kBAAkB,CAAC;YACpC,CAAC;YAED,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,cAAc,CAAC;YAChC,CAAC;YAED,KAAK,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,WAAW,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,sCAAqB,GAA7B,UAA8B,UAAmB;QAC/C,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAC7C,IAAM,aAAa,GAAW,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YACxC,OAAO,aAAa,CAAC;QACvB,CAAC;aAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,mCAAkB,GAA1B;QACE,QAAQ,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC5C,KAAK,KAAK,CAAC,aAAa;gBACtB,IAAM,KAAK,GAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC/C,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf;gBACE,OAAO,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,mCAAkB,GAA1B;QACE,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YACrB,KAAK,KAAK,CAAC,IAAI,CAAC;YAChB,KAAK,KAAK,CAAC,MAAM,CAAC;YAClB,KAAK,KAAK,CAAC,gBAAgB;gBACzB,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,yCAAwB,GAAhC;QACE,IAAI,IAAI,GAAW,EAAE,CAAC;QACtB,SAAS,CAAC;YACR,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;gBAC9B,KAAK,KAAK,CAAC,IAAI;oBACb,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACzB,MAAM;gBACR;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iCAAgB,GAAxB,UAAyB,KAAY,EAAE,WAAoB;QACzD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE,CAAC;YACpC,IAAM,IAAI,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC7C,IAAM,oBAAoB,GAAY,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;YACzE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACrB,IAAI,oBAAoB,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAG,WAAW,IAAI,aAAa,CAAC,KAAK,CAAC,qBAAkB,EAAE,IAAI,CAAC,CAAC;YACnF,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,UAAG,WAAW,IAAI,aAAa,CAAC,KAAK,CAAC,cAAW,EAAE,EAAE,CAAC,CAAC;IAC1E,CAAC;IAEO,0BAAS,GAAjB;QACE,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAEO,4BAAW,GAAnB;QACE,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAEO,+BAAc,GAAtB;QACE,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;YAC9B,KAAK,KAAK,CAAC,gBAAgB;gBACzB,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACxC;gBACE,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAEO,wCAAuB,GAA/B;QACE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACzC,IAAM,SAAS,GAAyB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACzE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC1C,OAAO,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEO,8BAAa,GAArB,UAAsB,KAAY;QAChC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,4BAAW,GAAnB,UAAoB,KAAY,EAAE,OAAgB;QAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE,CAAC;YACpC,IAAM,QAAQ,GAAW,aAAa,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAM,MAAM,GAAW,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,0BAAmB,QAAQ,0BAAgB,MAAM,eAAY,EAAE,SAAS,CAAC,CAAC;QACxG,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAEO,qBAAI,GAAZ,UAAgB,OAAe,EAAE,QAAW;QAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,OAAO,QAAQ,CAAC;IAClB,CAAC;IACH,aAAC;AAAD,CAAC,AArTD,IAqTC;AAED,SAAS,gBAAgB,CAAC,UAAkC;IAC1D,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,UAAU,CAAC,OAAO;YACrB,OAAO,GAAG,CAAC;QACb,KAAK,UAAU,CAAC,OAAO;YACrB,OAAO,GAAG,CAAC;QACb,KAAK,UAAU,CAAC,MAAM;YACpB,OAAO,GAAG,CAAC;QACb;YACE,OAAO,EAAE,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAS,yBAAyB,CAAC,EAAU;IAC3C,OAAO,uBAAuB,CAAC,EAAE,CAAC,IAAI,oBAAoB,CAAC,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,uBAAuB,CAAC,EAAU;IACzC,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,IAAI,CAAC;QACV,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,EAAU;IACtC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,iBAAiB,CAAC,EAAU;IACnC,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,IAAI,CAAC;QACd;YACE,OAAO,uBAAuB,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,EAAU;IAClC,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACP,mBAAmB;YACnB,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,EAAU;IAChC,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,UAAU,CAAC,EAAU;IAC5B,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,IAAI,CAAC;QACd;YACE,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,EAAU;IAC9B,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,IAAI,CAAC;QACd;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,uBAAuB,CAAC,IAAY,EAAE,WAAqB;IAClE,IAAI,WAAW,EAAE,CAAC;QAChB,IAAI,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,WAAW,CAAC,6BAAsB,IAAI,MAAG,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,oBAAoB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAY,EAAE,WAAqB;IACrE,IAAI,WAAW,EAAE,CAAC;QAChB,IAAI,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/D,MAAM,IAAI,WAAW,CAAC,iCAA0B,IAAI,MAAG,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,oBAAoB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;AAC7D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See L<PERSON>EN<PERSON> in the project root for license information.\r\n\r\n/* eslint-disable @typescript-eslint/no-use-before-define */\r\n/* eslint-disable @typescript-eslint/naming-convention */\r\n\r\n// NOTE: See DeclarationReference.grammarkdown for information on the underlying grammar.\r\n\r\nimport { StringChecks } from '../parser/StringChecks';\r\n\r\n/**\r\n * Represents a reference to a declaration.\r\n * @beta\r\n */\r\nexport class DeclarationReference {\r\n  private _source: ModuleSource | GlobalSource | undefined;\r\n  private _navigation: Navigation.Locals | Navigation.Exports | undefined;\r\n  private _symbol: SymbolReference | undefined;\r\n\r\n  public constructor(\r\n    source?: ModuleSource | GlobalSource,\r\n    navigation?: Navigation.Locals | Navigation.Exports,\r\n    symbol?: SymbolReference\r\n  ) {\r\n    this._source = source;\r\n    this._navigation = navigation;\r\n    this._symbol = symbol;\r\n  }\r\n\r\n  public get source(): ModuleSource | GlobalSource | undefined {\r\n    return this._source;\r\n  }\r\n\r\n  public get navigation(): Navigation.Locals | Navigation.Exports | undefined {\r\n    if (!this._source || !this._symbol) {\r\n      return undefined;\r\n    }\r\n    if (this._source === GlobalSource.instance) {\r\n      return Navigation.Locals;\r\n    }\r\n    if (this._navigation === undefined) {\r\n      return Navigation.Exports;\r\n    }\r\n    return this._navigation;\r\n  }\r\n\r\n  public get symbol(): SymbolReference | undefined {\r\n    return this._symbol;\r\n  }\r\n\r\n  public get isEmpty(): boolean {\r\n    return this.source === undefined && this.symbol === undefined;\r\n  }\r\n\r\n  public static parse(text: string): DeclarationReference {\r\n    const parser: Parser = new Parser(text);\r\n    const reference: DeclarationReference = parser.parseDeclarationReference();\r\n    if (parser.errors.length) {\r\n      throw new SyntaxError(`Invalid DeclarationReference '${text}':\\n  ${parser.errors.join('\\n  ')}`);\r\n    }\r\n    if (!parser.eof) {\r\n      throw new SyntaxError(`Invalid DeclarationReference '${text}'`);\r\n    }\r\n    return reference;\r\n  }\r\n\r\n  public static parseComponent(text: string): Component {\r\n    if (text[0] === '[') {\r\n      return ComponentReference.parse(text);\r\n    } else {\r\n      return new ComponentString(text, true);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determines whether the provided string is a well-formed symbol navigation component string.\r\n   */\r\n  public static isWellFormedComponentString(text: string): boolean {\r\n    const scanner: Scanner = new Scanner(text);\r\n    return scanner.scan() === Token.String\r\n      ? scanner.scan() === Token.EofToken\r\n      : scanner.token() === Token.Text\r\n        ? scanner.scan() === Token.EofToken\r\n        : scanner.token() === Token.EofToken;\r\n  }\r\n\r\n  /**\r\n   * Escapes a string for use as a symbol navigation component. If the string contains any of `!.#~:,\"{}()@` or starts\r\n   * with `[`, it is enclosed in quotes.\r\n   */\r\n  public static escapeComponentString(text: string): string {\r\n    if (text.length === 0) {\r\n      return '\"\"';\r\n    }\r\n    const ch: string = text.charAt(0);\r\n    if (ch === '[' || ch === '\"' || !this.isWellFormedComponentString(text)) {\r\n      return JSON.stringify(text);\r\n    }\r\n    return text;\r\n  }\r\n\r\n  /**\r\n   * Unescapes a string used as a symbol navigation component.\r\n   */\r\n  public static unescapeComponentString(text: string): string {\r\n    if (text.length >= 2 && text.charAt(0) === '\"' && text.charAt(text.length - 1) === '\"') {\r\n      try {\r\n        return JSON.parse(text);\r\n      } catch {\r\n        throw new SyntaxError(`Invalid Component '${text}'`);\r\n      }\r\n    }\r\n    if (!this.isWellFormedComponentString(text)) {\r\n      throw new SyntaxError(`Invalid Component '${text}'`);\r\n    }\r\n    return text;\r\n  }\r\n\r\n  /**\r\n   * Determines whether the provided string is a well-formed module source string. The string may not\r\n   * have a trailing `!` character.\r\n   */\r\n  public static isWellFormedModuleSourceString(text: string): boolean {\r\n    const scanner: Scanner = new Scanner(text + '!');\r\n    return (\r\n      scanner.rescanModuleSource() === Token.ModuleSource &&\r\n      !scanner.stringIsUnterminated &&\r\n      scanner.scan() === Token.ExclamationToken &&\r\n      scanner.scan() === Token.EofToken\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Escapes a string for use as a module source. If the string contains any of `!\"` it is enclosed in quotes.\r\n   */\r\n  public static escapeModuleSourceString(text: string): string {\r\n    if (text.length === 0) {\r\n      return '\"\"';\r\n    }\r\n    const ch: string = text.charAt(0);\r\n    if (ch === '\"' || !this.isWellFormedModuleSourceString(text)) {\r\n      return JSON.stringify(text);\r\n    }\r\n    return text;\r\n  }\r\n\r\n  /**\r\n   * Unescapes a string used as a module source. The string may not have a trailing `!` character.\r\n   */\r\n  public static unescapeModuleSourceString(text: string): string {\r\n    if (text.length >= 2 && text.charAt(0) === '\"' && text.charAt(text.length - 1) === '\"') {\r\n      try {\r\n        return JSON.parse(text);\r\n      } catch {\r\n        throw new SyntaxError(`Invalid Module source '${text}'`);\r\n      }\r\n    }\r\n    if (!this.isWellFormedModuleSourceString(text)) {\r\n      throw new SyntaxError(`Invalid Module source '${text}'`);\r\n    }\r\n    return text;\r\n  }\r\n\r\n  public static empty(): DeclarationReference {\r\n    return new DeclarationReference();\r\n  }\r\n\r\n  public static package(packageName: string, importPath?: string): DeclarationReference {\r\n    return new DeclarationReference(ModuleSource.fromPackage(packageName, importPath));\r\n  }\r\n\r\n  public static module(path: string, userEscaped?: boolean): DeclarationReference {\r\n    return new DeclarationReference(new ModuleSource(path, userEscaped));\r\n  }\r\n\r\n  public static global(): DeclarationReference {\r\n    return new DeclarationReference(GlobalSource.instance);\r\n  }\r\n\r\n  public static from(base: DeclarationReference | undefined): DeclarationReference {\r\n    return base || this.empty();\r\n  }\r\n\r\n  public withSource(source: ModuleSource | GlobalSource | undefined): DeclarationReference {\r\n    return this._source === source ? this : new DeclarationReference(source, this._navigation, this._symbol);\r\n  }\r\n\r\n  public withNavigation(\r\n    navigation: Navigation.Locals | Navigation.Exports | undefined\r\n  ): DeclarationReference {\r\n    return this._navigation === navigation\r\n      ? this\r\n      : new DeclarationReference(this._source, navigation, this._symbol);\r\n  }\r\n\r\n  public withSymbol(symbol: SymbolReference | undefined): DeclarationReference {\r\n    return this._symbol === symbol ? this : new DeclarationReference(this._source, this._navigation, symbol);\r\n  }\r\n\r\n  public withComponentPath(componentPath: ComponentPath): DeclarationReference {\r\n    return this.withSymbol(\r\n      this.symbol ? this.symbol.withComponentPath(componentPath) : new SymbolReference(componentPath)\r\n    );\r\n  }\r\n\r\n  public withMeaning(meaning: Meaning | undefined): DeclarationReference {\r\n    if (!this.symbol) {\r\n      if (meaning === undefined) {\r\n        return this;\r\n      }\r\n      return this.withSymbol(SymbolReference.empty().withMeaning(meaning));\r\n    }\r\n    return this.withSymbol(this.symbol.withMeaning(meaning));\r\n  }\r\n\r\n  public withOverloadIndex(overloadIndex: number | undefined): DeclarationReference {\r\n    if (!this.symbol) {\r\n      if (overloadIndex === undefined) {\r\n        return this;\r\n      }\r\n      return this.withSymbol(SymbolReference.empty().withOverloadIndex(overloadIndex));\r\n    }\r\n    return this.withSymbol(this.symbol.withOverloadIndex(overloadIndex));\r\n  }\r\n\r\n  public addNavigationStep(navigation: Navigation, component: ComponentLike): DeclarationReference {\r\n    if (this.symbol) {\r\n      return this.withSymbol(this.symbol.addNavigationStep(navigation, component));\r\n    }\r\n    if (navigation === Navigation.Members) {\r\n      navigation = Navigation.Exports;\r\n    }\r\n    const symbol: SymbolReference = new SymbolReference(new ComponentRoot(Component.from(component)));\r\n    return new DeclarationReference(this.source, navigation, symbol);\r\n  }\r\n\r\n  public toString(): string {\r\n    const navigation: string =\r\n      this._source instanceof ModuleSource && this._symbol && this.navigation === Navigation.Locals\r\n        ? '~'\r\n        : '';\r\n    return `${this.source || ''}${navigation}${this.symbol || ''}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Indicates the symbol table from which to resolve the next symbol component.\r\n * @beta\r\n */\r\nexport enum Navigation {\r\n  Exports = '.',\r\n  Members = '#',\r\n  Locals = '~'\r\n}\r\n\r\n/**\r\n * Represents a module.\r\n * @beta\r\n */\r\nexport class ModuleSource {\r\n  public readonly escapedPath: string;\r\n  private _path: string | undefined;\r\n\r\n  private _pathComponents: IParsedPackage | undefined;\r\n\r\n  public constructor(path: string, userEscaped: boolean = true) {\r\n    this.escapedPath =\r\n      this instanceof ParsedModuleSource ? path : escapeModuleSourceIfNeeded(path, userEscaped);\r\n  }\r\n\r\n  public get path(): string {\r\n    return this._path || (this._path = DeclarationReference.unescapeModuleSourceString(this.escapedPath));\r\n  }\r\n\r\n  public get packageName(): string {\r\n    return this._getOrParsePathComponents().packageName;\r\n  }\r\n\r\n  public get scopeName(): string {\r\n    const scopeName: string = this._getOrParsePathComponents().scopeName;\r\n    return scopeName ? '@' + scopeName : '';\r\n  }\r\n\r\n  public get unscopedPackageName(): string {\r\n    return this._getOrParsePathComponents().unscopedPackageName;\r\n  }\r\n\r\n  public get importPath(): string {\r\n    return this._getOrParsePathComponents().importPath || '';\r\n  }\r\n\r\n  public static fromScopedPackage(\r\n    scopeName: string | undefined,\r\n    unscopedPackageName: string,\r\n    importPath?: string\r\n  ): ModuleSource {\r\n    let packageName: string = unscopedPackageName;\r\n    if (scopeName) {\r\n      if (scopeName.charAt(0) === '@') {\r\n        scopeName = scopeName.slice(1);\r\n      }\r\n      packageName = `@${scopeName}/${unscopedPackageName}`;\r\n    }\r\n\r\n    const parsed: IParsedPackage = { packageName, scopeName: scopeName || '', unscopedPackageName };\r\n    return this._fromPackageName(parsed, packageName, importPath);\r\n  }\r\n\r\n  public static fromPackage(packageName: string, importPath?: string): ModuleSource {\r\n    return this._fromPackageName(parsePackageName(packageName), packageName, importPath);\r\n  }\r\n\r\n  private static _fromPackageName(\r\n    parsed: IParsedPackage | null,\r\n    packageName: string,\r\n    importPath?: string\r\n  ): ModuleSource {\r\n    if (!parsed) {\r\n      throw new Error('Parsed package must be provided.');\r\n    }\r\n\r\n    const packageNameError: string | undefined = StringChecks.explainIfInvalidPackageName(packageName);\r\n    if (packageNameError) {\r\n      throw new SyntaxError(`Invalid NPM package name: ${packageNameError}`);\r\n    }\r\n\r\n    let path: string = packageName;\r\n    if (importPath) {\r\n      if (invalidImportPathRegExp.test(importPath)) {\r\n        throw new SyntaxError(`Invalid import path '${importPath}`);\r\n      }\r\n      path += '/' + importPath;\r\n      parsed.importPath = importPath;\r\n    }\r\n\r\n    const source: ModuleSource = new ModuleSource(path);\r\n    source._pathComponents = parsed;\r\n    return source;\r\n  }\r\n\r\n  public toString(): string {\r\n    return `${this.escapedPath}!`;\r\n  }\r\n\r\n  private _getOrParsePathComponents(): IParsedPackage {\r\n    if (!this._pathComponents) {\r\n      const path: string = this.path;\r\n      const parsed: IParsedPackage | null = parsePackageName(path);\r\n      if (parsed && !StringChecks.explainIfInvalidPackageName(parsed.packageName)) {\r\n        this._pathComponents = parsed;\r\n      } else {\r\n        this._pathComponents = {\r\n          packageName: '',\r\n          scopeName: '',\r\n          unscopedPackageName: '',\r\n          importPath: path\r\n        };\r\n      }\r\n    }\r\n    return this._pathComponents;\r\n  }\r\n}\r\n\r\nclass ParsedModuleSource extends ModuleSource {}\r\n\r\n// matches the following:\r\n//   'foo'            -> [\"foo\", \"foo\", undefined, \"foo\", undefined]\r\n//   'foo/bar'        -> [\"foo/bar\", \"foo\", undefined, \"foo\", \"bar\"]\r\n//   '@scope/foo'     -> [\"@scope/foo\", \"@scope/foo\", \"scope\", \"foo\", undefined]\r\n//   '@scope/foo/bar' -> [\"@scope/foo/bar\", \"@scope/foo\", \"scope\", \"foo\", \"bar\"]\r\n// does not match:\r\n//   '/'\r\n//   '@/'\r\n//   '@scope/'\r\n// capture groups:\r\n//   1. The package name (including scope)\r\n//   2. The scope name (excluding the leading '@')\r\n//   3. The unscoped package name\r\n//   4. The package-relative import path\r\nconst packageNameRegExp: RegExp = /^((?:@([^/]+?)\\/)?([^/]+?))(?:\\/(.+))?$/;\r\n\r\n// no leading './' or '.\\'\r\n// no leading '../' or '..\\'\r\n// no leading '/' or '\\'\r\n// not '.' or '..'\r\nconst invalidImportPathRegExp: RegExp = /^(\\.\\.?([\\\\/]|$)|[\\\\/])/;\r\n\r\ninterface IParsedPackage {\r\n  packageName: string;\r\n  scopeName: string;\r\n  unscopedPackageName: string;\r\n  importPath?: string;\r\n}\r\n\r\n// eslint-disable-next-line @rushstack/no-new-null\r\nfunction parsePackageName(text: string): IParsedPackage | null {\r\n  const match: RegExpExecArray | null = packageNameRegExp.exec(text);\r\n  if (!match) {\r\n    return match;\r\n  }\r\n  const [, packageName = '', scopeName = '', unscopedPackageName = '', importPath]: RegExpExecArray = match;\r\n  return { packageName, scopeName, unscopedPackageName, importPath };\r\n}\r\n\r\n/**\r\n * Represents the global scope.\r\n * @beta\r\n */\r\nexport class GlobalSource {\r\n  public static readonly instance: GlobalSource = new GlobalSource();\r\n\r\n  private constructor() {}\r\n\r\n  public toString(): string {\r\n    return '!';\r\n  }\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport type Component = ComponentString | ComponentReference;\r\n\r\n/**\r\n * @beta\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-namespace\r\nexport namespace Component {\r\n  export function from(value: ComponentLike): Component {\r\n    if (typeof value === 'string') {\r\n      return new ComponentString(value);\r\n    }\r\n    if (value instanceof DeclarationReference) {\r\n      return new ComponentReference(value);\r\n    }\r\n    return value;\r\n  }\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport type ComponentLike = Component | DeclarationReference | string;\r\n\r\n/**\r\n * @beta\r\n */\r\nexport class ComponentString {\r\n  public readonly text: string;\r\n\r\n  public constructor(text: string, userEscaped?: boolean) {\r\n    this.text = this instanceof ParsedComponentString ? text : escapeComponentIfNeeded(text, userEscaped);\r\n  }\r\n\r\n  public toString(): string {\r\n    return this.text;\r\n  }\r\n}\r\n\r\nclass ParsedComponentString extends ComponentString {}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport class ComponentReference {\r\n  public readonly reference: DeclarationReference;\r\n\r\n  public constructor(reference: DeclarationReference) {\r\n    this.reference = reference;\r\n  }\r\n\r\n  public static parse(text: string): ComponentReference {\r\n    if (text.length > 2 && text.charAt(0) === '[' && text.charAt(text.length - 1) === ']') {\r\n      return new ComponentReference(DeclarationReference.parse(text.slice(1, -1)));\r\n    }\r\n    throw new SyntaxError(`Invalid component reference: '${text}'`);\r\n  }\r\n\r\n  public withReference(reference: DeclarationReference): ComponentReference {\r\n    return this.reference === reference ? this : new ComponentReference(reference);\r\n  }\r\n\r\n  public toString(): string {\r\n    return `[${this.reference}]`;\r\n  }\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport type ComponentPath = ComponentRoot | ComponentNavigation;\r\n\r\n/**\r\n * @beta\r\n */\r\nexport abstract class ComponentPathBase {\r\n  public readonly component: Component;\r\n\r\n  public constructor(component: Component) {\r\n    this.component = component;\r\n  }\r\n\r\n  public addNavigationStep(\r\n    this: ComponentPath,\r\n    navigation: Navigation,\r\n    component: ComponentLike\r\n  ): ComponentPath {\r\n    // tslint:disable-next-line:no-use-before-declare\r\n    return new ComponentNavigation(this, navigation, Component.from(component));\r\n  }\r\n\r\n  public abstract toString(): string;\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport class ComponentRoot extends ComponentPathBase {\r\n  public withComponent(component: ComponentLike): ComponentRoot {\r\n    return this.component === component ? this : new ComponentRoot(Component.from(component));\r\n  }\r\n\r\n  public toString(): string {\r\n    return this.component.toString();\r\n  }\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport class ComponentNavigation extends ComponentPathBase {\r\n  public readonly parent: ComponentPath;\r\n  public readonly navigation: Navigation;\r\n\r\n  public constructor(parent: ComponentPath, navigation: Navigation, component: Component) {\r\n    super(component);\r\n    this.parent = parent;\r\n    this.navigation = navigation;\r\n  }\r\n\r\n  public withParent(parent: ComponentPath): ComponentNavigation {\r\n    return this.parent === parent ? this : new ComponentNavigation(parent, this.navigation, this.component);\r\n  }\r\n\r\n  public withNavigation(navigation: Navigation): ComponentNavigation {\r\n    return this.navigation === navigation\r\n      ? this\r\n      : new ComponentNavigation(this.parent, navigation, this.component);\r\n  }\r\n\r\n  public withComponent(component: ComponentLike): ComponentNavigation {\r\n    return this.component === component\r\n      ? this\r\n      : new ComponentNavigation(this.parent, this.navigation, Component.from(component));\r\n  }\r\n\r\n  public toString(): string {\r\n    return `${this.parent}${formatNavigation(this.navigation)}${this.component}`;\r\n  }\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport enum Meaning {\r\n  Class = 'class', // SymbolFlags.Class\r\n  Interface = 'interface', // SymbolFlags.Interface\r\n  TypeAlias = 'type', // SymbolFlags.TypeAlias\r\n  Enum = 'enum', // SymbolFlags.Enum\r\n  Namespace = 'namespace', // SymbolFlags.Module\r\n  Function = 'function', // SymbolFlags.Function\r\n  Variable = 'var', // SymbolFlags.Variable\r\n  Constructor = 'constructor', // SymbolFlags.Constructor\r\n  Member = 'member', // SymbolFlags.ClassMember | SymbolFlags.EnumMember\r\n  Event = 'event', //\r\n  CallSignature = 'call', // SymbolFlags.Signature (for __call)\r\n  ConstructSignature = 'new', // SymbolFlags.Signature (for __new)\r\n  IndexSignature = 'index', // SymbolFlags.Signature (for __index)\r\n  ComplexType = 'complex' // Any complex type\r\n}\r\n\r\n/**\r\n * @beta\r\n */\r\nexport interface ISymbolReferenceOptions {\r\n  meaning?: Meaning;\r\n  overloadIndex?: number;\r\n}\r\n\r\n/**\r\n * Represents a reference to a TypeScript symbol.\r\n * @beta\r\n */\r\nexport class SymbolReference {\r\n  public readonly componentPath: ComponentPath | undefined;\r\n  public readonly meaning: Meaning | undefined;\r\n  public readonly overloadIndex: number | undefined;\r\n\r\n  public constructor(\r\n    component: ComponentPath | undefined,\r\n    { meaning, overloadIndex }: ISymbolReferenceOptions = {}\r\n  ) {\r\n    this.componentPath = component;\r\n    this.overloadIndex = overloadIndex;\r\n    this.meaning = meaning;\r\n  }\r\n\r\n  public static empty(): SymbolReference {\r\n    return new SymbolReference(/*component*/ undefined);\r\n  }\r\n\r\n  public withComponentPath(componentPath: ComponentPath | undefined): SymbolReference {\r\n    return this.componentPath === componentPath\r\n      ? this\r\n      : new SymbolReference(componentPath, {\r\n          meaning: this.meaning,\r\n          overloadIndex: this.overloadIndex\r\n        });\r\n  }\r\n\r\n  public withMeaning(meaning: Meaning | undefined): SymbolReference {\r\n    return this.meaning === meaning\r\n      ? this\r\n      : new SymbolReference(this.componentPath, {\r\n          meaning,\r\n          overloadIndex: this.overloadIndex\r\n        });\r\n  }\r\n\r\n  public withOverloadIndex(overloadIndex: number | undefined): SymbolReference {\r\n    return this.overloadIndex === overloadIndex\r\n      ? this\r\n      : new SymbolReference(this.componentPath, {\r\n          meaning: this.meaning,\r\n          overloadIndex\r\n        });\r\n  }\r\n\r\n  public addNavigationStep(navigation: Navigation, component: ComponentLike): SymbolReference {\r\n    if (!this.componentPath) {\r\n      throw new Error('Cannot add a navigation step to an empty symbol reference.');\r\n    }\r\n    return new SymbolReference(this.componentPath.addNavigationStep(navigation, component));\r\n  }\r\n\r\n  public toString(): string {\r\n    let result: string = `${this.componentPath || ''}`;\r\n    if (this.meaning && this.overloadIndex !== undefined) {\r\n      result += `:${this.meaning}(${this.overloadIndex})`;\r\n    } else if (this.meaning) {\r\n      result += `:${this.meaning}`;\r\n    } else if (this.overloadIndex !== undefined) {\r\n      result += `:${this.overloadIndex}`;\r\n    }\r\n    return result;\r\n  }\r\n}\r\n\r\nconst enum Token {\r\n  None,\r\n  EofToken,\r\n  // Punctuator\r\n  OpenBraceToken, // '{'\r\n  CloseBraceToken, // '}'\r\n  OpenParenToken, // '('\r\n  CloseParenToken, // ')'\r\n  OpenBracketToken, // '['\r\n  CloseBracketToken, // ']'\r\n  ExclamationToken, // '!'\r\n  DotToken, // '.'\r\n  HashToken, // '#'\r\n  TildeToken, // '~'\r\n  ColonToken, // ':'\r\n  CommaToken, // ','\r\n  AtToken, // '@'\r\n  DecimalDigits, // '12345'\r\n  String, // '\"abc\"'\r\n  Text, // 'abc'\r\n  ModuleSource, // 'abc/def!' (excludes '!')\r\n  // Keywords\r\n  ClassKeyword, // 'class'\r\n  InterfaceKeyword, // 'interface'\r\n  TypeKeyword, // 'type'\r\n  EnumKeyword, // 'enum'\r\n  NamespaceKeyword, // 'namespace'\r\n  FunctionKeyword, // 'function'\r\n  VarKeyword, // 'var'\r\n  ConstructorKeyword, // 'constructor'\r\n  MemberKeyword, // 'member'\r\n  EventKeyword, // 'event'\r\n  CallKeyword, // 'call'\r\n  NewKeyword, // 'new'\r\n  IndexKeyword, // 'index'\r\n  ComplexKeyword // 'complex'\r\n}\r\n\r\nfunction tokenToString(token: Token): string {\r\n  switch (token) {\r\n    case Token.OpenBraceToken:\r\n      return '{';\r\n    case Token.CloseBraceToken:\r\n      return '}';\r\n    case Token.OpenParenToken:\r\n      return '(';\r\n    case Token.CloseParenToken:\r\n      return ')';\r\n    case Token.OpenBracketToken:\r\n      return '[';\r\n    case Token.CloseBracketToken:\r\n      return ']';\r\n    case Token.ExclamationToken:\r\n      return '!';\r\n    case Token.DotToken:\r\n      return '.';\r\n    case Token.HashToken:\r\n      return '#';\r\n    case Token.TildeToken:\r\n      return '~';\r\n    case Token.ColonToken:\r\n      return ':';\r\n    case Token.CommaToken:\r\n      return ',';\r\n    case Token.AtToken:\r\n      return '@';\r\n    case Token.ClassKeyword:\r\n      return 'class';\r\n    case Token.InterfaceKeyword:\r\n      return 'interface';\r\n    case Token.TypeKeyword:\r\n      return 'type';\r\n    case Token.EnumKeyword:\r\n      return 'enum';\r\n    case Token.NamespaceKeyword:\r\n      return 'namespace';\r\n    case Token.FunctionKeyword:\r\n      return 'function';\r\n    case Token.VarKeyword:\r\n      return 'var';\r\n    case Token.ConstructorKeyword:\r\n      return 'constructor';\r\n    case Token.MemberKeyword:\r\n      return 'member';\r\n    case Token.EventKeyword:\r\n      return 'event';\r\n    case Token.CallKeyword:\r\n      return 'call';\r\n    case Token.NewKeyword:\r\n      return 'new';\r\n    case Token.IndexKeyword:\r\n      return 'index';\r\n    case Token.ComplexKeyword:\r\n      return 'complex';\r\n    case Token.None:\r\n      return '<none>';\r\n    case Token.EofToken:\r\n      return '<eof>';\r\n    case Token.DecimalDigits:\r\n      return '<decimal digits>';\r\n    case Token.String:\r\n      return '<string>';\r\n    case Token.Text:\r\n      return '<text>';\r\n    case Token.ModuleSource:\r\n      return '<module source>';\r\n  }\r\n}\r\n\r\nclass Scanner {\r\n  private _tokenPos: number;\r\n  private _pos: number;\r\n  private _text: string;\r\n  private _token: Token;\r\n  private _stringIsUnterminated: boolean;\r\n\r\n  public constructor(text: string) {\r\n    this._pos = 0;\r\n    this._tokenPos = 0;\r\n    this._stringIsUnterminated = false;\r\n    this._token = Token.None;\r\n    this._text = text;\r\n  }\r\n\r\n  public get stringIsUnterminated(): boolean {\r\n    return this._stringIsUnterminated;\r\n  }\r\n\r\n  public get text(): string {\r\n    return this._text;\r\n  }\r\n\r\n  public get tokenText(): string {\r\n    return this._text.slice(this._tokenPos, this._pos);\r\n  }\r\n\r\n  public get eof(): boolean {\r\n    return this._pos >= this._text.length;\r\n  }\r\n\r\n  public token(): Token {\r\n    return this._token;\r\n  }\r\n\r\n  public speculate<T>(cb: (accept: () => void) => T): T {\r\n    const tokenPos: number = this._tokenPos;\r\n    const pos: number = this._pos;\r\n    const text: string = this._text;\r\n    const token: Token = this._token;\r\n    const stringIsUnterminated: boolean = this._stringIsUnterminated;\r\n    let accepted: boolean = false;\r\n    try {\r\n      const accept: () => void = () => {\r\n        accepted = true;\r\n      };\r\n      return cb(accept);\r\n    } finally {\r\n      if (!accepted) {\r\n        this._tokenPos = tokenPos;\r\n        this._pos = pos;\r\n        this._text = text;\r\n        this._token = token;\r\n        this._stringIsUnterminated = stringIsUnterminated;\r\n      }\r\n    }\r\n  }\r\n\r\n  public scan(): Token {\r\n    if (!this.eof) {\r\n      this._tokenPos = this._pos;\r\n      this._stringIsUnterminated = false;\r\n      while (!this.eof) {\r\n        const ch: string = this._text.charAt(this._pos++);\r\n        switch (ch) {\r\n          case '{':\r\n            return (this._token = Token.OpenBraceToken);\r\n          case '}':\r\n            return (this._token = Token.CloseBraceToken);\r\n          case '(':\r\n            return (this._token = Token.OpenParenToken);\r\n          case ')':\r\n            return (this._token = Token.CloseParenToken);\r\n          case '[':\r\n            return (this._token = Token.OpenBracketToken);\r\n          case ']':\r\n            return (this._token = Token.CloseBracketToken);\r\n          case '!':\r\n            return (this._token = Token.ExclamationToken);\r\n          case '.':\r\n            return (this._token = Token.DotToken);\r\n          case '#':\r\n            return (this._token = Token.HashToken);\r\n          case '~':\r\n            return (this._token = Token.TildeToken);\r\n          case ':':\r\n            return (this._token = Token.ColonToken);\r\n          case ',':\r\n            return (this._token = Token.CommaToken);\r\n          case '@':\r\n            return (this._token = Token.AtToken);\r\n          case '\"':\r\n            this.scanString();\r\n            return (this._token = Token.String);\r\n          default:\r\n            this.scanText();\r\n            return (this._token = Token.Text);\r\n        }\r\n      }\r\n    }\r\n    return (this._token = Token.EofToken);\r\n  }\r\n\r\n  public rescanModuleSource(): Token {\r\n    switch (this._token) {\r\n      case Token.ModuleSource:\r\n      case Token.ExclamationToken:\r\n      case Token.EofToken:\r\n        return this._token;\r\n    }\r\n    return this.speculate((accept) => {\r\n      if (!this.eof) {\r\n        this._pos = this._tokenPos;\r\n        this._stringIsUnterminated = false;\r\n        let scanned: 'string' | 'other' | 'none' = 'none';\r\n        while (!this.eof) {\r\n          const ch: string = this._text[this._pos];\r\n          if (ch === '!') {\r\n            if (scanned === 'none') {\r\n              return this._token;\r\n            }\r\n            accept();\r\n            return (this._token = Token.ModuleSource);\r\n          }\r\n          this._pos++;\r\n          if (ch === '\"') {\r\n            if (scanned === 'other') {\r\n              // strings not allowed after scanning any other characters\r\n              return this._token;\r\n            }\r\n            scanned = 'string';\r\n            this.scanString();\r\n          } else {\r\n            if (scanned === 'string') {\r\n              // no other tokens allowed after string\r\n              return this._token;\r\n            }\r\n            scanned = 'other';\r\n            if (!isPunctuator(ch)) {\r\n              this.scanText();\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return this._token;\r\n    });\r\n  }\r\n\r\n  public rescanMeaning(): Token {\r\n    if (this._token === Token.Text) {\r\n      const tokenText: string = this.tokenText;\r\n      switch (tokenText) {\r\n        case 'class':\r\n          return (this._token = Token.ClassKeyword);\r\n        case 'interface':\r\n          return (this._token = Token.InterfaceKeyword);\r\n        case 'type':\r\n          return (this._token = Token.TypeKeyword);\r\n        case 'enum':\r\n          return (this._token = Token.EnumKeyword);\r\n        case 'namespace':\r\n          return (this._token = Token.NamespaceKeyword);\r\n        case 'function':\r\n          return (this._token = Token.FunctionKeyword);\r\n        case 'var':\r\n          return (this._token = Token.VarKeyword);\r\n        case 'constructor':\r\n          return (this._token = Token.ConstructorKeyword);\r\n        case 'member':\r\n          return (this._token = Token.MemberKeyword);\r\n        case 'event':\r\n          return (this._token = Token.EventKeyword);\r\n        case 'call':\r\n          return (this._token = Token.CallKeyword);\r\n        case 'new':\r\n          return (this._token = Token.NewKeyword);\r\n        case 'index':\r\n          return (this._token = Token.IndexKeyword);\r\n        case 'complex':\r\n          return (this._token = Token.ComplexKeyword);\r\n      }\r\n    }\r\n    return this._token;\r\n  }\r\n\r\n  public rescanDecimalDigits(): Token {\r\n    if (this._token === Token.Text) {\r\n      const tokenText: string = this.tokenText;\r\n      if (/^\\d+$/.test(tokenText)) {\r\n        return (this._token = Token.DecimalDigits);\r\n      }\r\n    }\r\n    return this._token;\r\n  }\r\n\r\n  private scanString(): void {\r\n    while (!this.eof) {\r\n      const ch: string = this._text.charAt(this._pos++);\r\n      switch (ch) {\r\n        case '\"':\r\n          return;\r\n        case '\\\\':\r\n          this.scanEscapeSequence();\r\n          break;\r\n        default:\r\n          if (isLineTerminator(ch)) {\r\n            this._stringIsUnterminated = true;\r\n            return;\r\n          }\r\n      }\r\n    }\r\n    this._stringIsUnterminated = true;\r\n  }\r\n\r\n  private scanEscapeSequence(): void {\r\n    if (this.eof) {\r\n      this._stringIsUnterminated = true;\r\n      return;\r\n    }\r\n\r\n    const ch: string = this._text.charAt(this._pos);\r\n\r\n    // EscapeSequence:: CharacterEscapeSequence\r\n    if (isCharacterEscapeSequence(ch)) {\r\n      this._pos++;\r\n      return;\r\n    }\r\n\r\n    // EscapeSequence:: `0` [lookahead != DecimalDigit]\r\n    if (\r\n      ch === '0' &&\r\n      (this._pos + 1 === this._text.length || !isDecimalDigit(this._text.charAt(this._pos + 1)))\r\n    ) {\r\n      this._pos++;\r\n      return;\r\n    }\r\n\r\n    // EscapeSequence:: HexEscapeSequence\r\n    if (\r\n      ch === 'x' &&\r\n      this._pos + 3 <= this._text.length &&\r\n      isHexDigit(this._text.charAt(this._pos + 1)) &&\r\n      isHexDigit(this._text.charAt(this._pos + 2))\r\n    ) {\r\n      this._pos += 3;\r\n      return;\r\n    }\r\n\r\n    // EscapeSequence:: UnicodeEscapeSequence\r\n    // UnicodeEscapeSequence:: `u` Hex4Digits\r\n    if (\r\n      ch === 'u' &&\r\n      this._pos + 5 <= this._text.length &&\r\n      isHexDigit(this._text.charAt(this._pos + 1)) &&\r\n      isHexDigit(this._text.charAt(this._pos + 2)) &&\r\n      isHexDigit(this._text.charAt(this._pos + 3)) &&\r\n      isHexDigit(this._text.charAt(this._pos + 4))\r\n    ) {\r\n      this._pos += 5;\r\n      return;\r\n    }\r\n\r\n    // EscapeSequence:: UnicodeEscapeSequence\r\n    // UnicodeEscapeSequence:: `u` `{` CodePoint `}`\r\n    if (ch === 'u' && this._pos + 4 <= this._text.length && this._text.charAt(this._pos + 1) === '{') {\r\n      let hexDigits: string = this._text.charAt(this._pos + 2);\r\n      if (isHexDigit(hexDigits)) {\r\n        for (let i: number = this._pos + 3; i < this._text.length; i++) {\r\n          const ch2: string = this._text.charAt(i);\r\n          if (ch2 === '}') {\r\n            const mv: number = parseInt(hexDigits, 16);\r\n            if (mv <= 0x10ffff) {\r\n              this._pos = i + 1;\r\n              return;\r\n            }\r\n            break;\r\n          }\r\n          if (!isHexDigit(ch2)) {\r\n            hexDigits += ch2;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    this._stringIsUnterminated = true;\r\n  }\r\n\r\n  private scanText(): void {\r\n    while (this._pos < this._text.length) {\r\n      const ch: string = this._text.charAt(this._pos);\r\n      if (isPunctuator(ch) || ch === '\"') {\r\n        return;\r\n      }\r\n      this._pos++;\r\n    }\r\n  }\r\n}\r\n\r\nclass Parser {\r\n  private _errors: string[];\r\n  private _scanner: Scanner;\r\n\r\n  public constructor(text: string) {\r\n    this._errors = [];\r\n    this._scanner = new Scanner(text);\r\n    this._scanner.scan();\r\n  }\r\n\r\n  public get eof(): boolean {\r\n    return this.token() === Token.EofToken;\r\n  }\r\n\r\n  public get errors(): readonly string[] {\r\n    return this._errors;\r\n  }\r\n\r\n  public parseDeclarationReference(): DeclarationReference {\r\n    let source: ModuleSource | GlobalSource | undefined;\r\n    let navigation: Navigation.Locals | undefined;\r\n    let symbol: SymbolReference | undefined;\r\n    if (this.optionalToken(Token.ExclamationToken)) {\r\n      // Reference to global symbol\r\n      source = GlobalSource.instance;\r\n    } else if (this._scanner.rescanModuleSource() === Token.ModuleSource) {\r\n      source = this.parseModuleSource();\r\n      // Check for optional `~` navigation token.\r\n      if (this.optionalToken(Token.TildeToken)) {\r\n        navigation = Navigation.Locals;\r\n      }\r\n    }\r\n    if (this.isStartOfComponent()) {\r\n      symbol = this.parseSymbol();\r\n    } else if (this.token() === Token.ColonToken) {\r\n      symbol = this.parseSymbolRest(new ComponentRoot(new ComponentString('', /*userEscaped*/ true)));\r\n    }\r\n    return new DeclarationReference(source, navigation, symbol);\r\n  }\r\n\r\n  public parseModuleSourceString(): string {\r\n    this._scanner.rescanModuleSource();\r\n    return this.parseTokenString(Token.ModuleSource, 'Module source');\r\n  }\r\n\r\n  public parseComponentString(): string {\r\n    switch (this._scanner.token()) {\r\n      case Token.String:\r\n        return this.parseString();\r\n      default:\r\n        return this.parseComponentCharacters();\r\n    }\r\n  }\r\n\r\n  private token(): Token {\r\n    return this._scanner.token();\r\n  }\r\n\r\n  private parseModuleSource(): ModuleSource | undefined {\r\n    const source: string = this.parseModuleSourceString();\r\n    this.expectToken(Token.ExclamationToken);\r\n    return new ParsedModuleSource(source, /*userEscaped*/ true);\r\n  }\r\n\r\n  private parseSymbol(): SymbolReference {\r\n    const component: ComponentPath = this.parseComponentRest(this.parseRootComponent());\r\n    return this.parseSymbolRest(component);\r\n  }\r\n\r\n  private parseSymbolRest(component: ComponentPath): SymbolReference {\r\n    let meaning: Meaning | undefined;\r\n    let overloadIndex: number | undefined;\r\n    if (this.optionalToken(Token.ColonToken)) {\r\n      meaning = this.tryParseMeaning();\r\n      overloadIndex = this.tryParseOverloadIndex(!!meaning);\r\n    }\r\n\r\n    return new SymbolReference(component, { meaning, overloadIndex });\r\n  }\r\n\r\n  private parseRootComponent(): ComponentPath {\r\n    if (!this.isStartOfComponent()) {\r\n      return this.fail(\r\n        'Component expected',\r\n        new ComponentRoot(new ComponentString('', /*userEscaped*/ true))\r\n      );\r\n    }\r\n\r\n    const component: Component = this.parseComponent();\r\n    return new ComponentRoot(component);\r\n  }\r\n\r\n  private parseComponentRest(component: ComponentPath): ComponentPath {\r\n    for (;;) {\r\n      switch (this.token()) {\r\n        case Token.DotToken:\r\n        case Token.HashToken:\r\n        case Token.TildeToken:\r\n          const navigation: Navigation = this.parseNavigation();\r\n          const right: Component = this.parseComponent();\r\n          component = new ComponentNavigation(component, navigation, right);\r\n          break;\r\n        default:\r\n          return component;\r\n      }\r\n    }\r\n  }\r\n\r\n  private parseNavigation(): Navigation {\r\n    switch (this._scanner.token()) {\r\n      case Token.DotToken: {\r\n        this._scanner.scan();\r\n        return Navigation.Exports;\r\n      }\r\n\r\n      case Token.HashToken: {\r\n        this._scanner.scan();\r\n        return Navigation.Members;\r\n      }\r\n\r\n      case Token.TildeToken: {\r\n        this._scanner.scan();\r\n        return Navigation.Locals;\r\n      }\r\n\r\n      default: {\r\n        return this.fail(\"Expected '.', '#', or '~'\", Navigation.Exports);\r\n      }\r\n    }\r\n  }\r\n\r\n  private tryParseMeaning(): Meaning | undefined {\r\n    switch (this._scanner.rescanMeaning()) {\r\n      case Token.ClassKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Class;\r\n      }\r\n\r\n      case Token.InterfaceKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Interface;\r\n      }\r\n\r\n      case Token.TypeKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.TypeAlias;\r\n      }\r\n\r\n      case Token.EnumKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Enum;\r\n      }\r\n\r\n      case Token.NamespaceKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Namespace;\r\n      }\r\n\r\n      case Token.FunctionKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Function;\r\n      }\r\n\r\n      case Token.VarKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Variable;\r\n      }\r\n\r\n      case Token.ConstructorKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Constructor;\r\n      }\r\n\r\n      case Token.MemberKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Member;\r\n      }\r\n\r\n      case Token.EventKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.Event;\r\n      }\r\n\r\n      case Token.CallKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.CallSignature;\r\n      }\r\n\r\n      case Token.NewKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.ConstructSignature;\r\n      }\r\n\r\n      case Token.IndexKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.IndexSignature;\r\n      }\r\n\r\n      case Token.ComplexKeyword: {\r\n        this._scanner.scan();\r\n        return Meaning.ComplexType;\r\n      }\r\n    }\r\n  }\r\n\r\n  private tryParseOverloadIndex(hasMeaning: boolean): number | undefined {\r\n    if (this.optionalToken(Token.OpenParenToken)) {\r\n      const overloadIndex: number = this.parseDecimalDigits();\r\n      this.expectToken(Token.CloseParenToken);\r\n      return overloadIndex;\r\n    } else if (!hasMeaning) {\r\n      return this.parseDecimalDigits();\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  private parseDecimalDigits(): number {\r\n    switch (this._scanner.rescanDecimalDigits()) {\r\n      case Token.DecimalDigits:\r\n        const value: number = +this._scanner.tokenText;\r\n        this._scanner.scan();\r\n        return value;\r\n      default:\r\n        return this.fail('Decimal digit expected', 0);\r\n    }\r\n  }\r\n\r\n  private isStartOfComponent(): boolean {\r\n    switch (this.token()) {\r\n      case Token.Text:\r\n      case Token.String:\r\n      case Token.OpenBracketToken:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  private parseComponentCharacters(): string {\r\n    let text: string = '';\r\n    for (;;) {\r\n      switch (this._scanner.token()) {\r\n        case Token.Text:\r\n          text += this.parseText();\r\n          break;\r\n        default:\r\n          return text;\r\n      }\r\n    }\r\n  }\r\n\r\n  private parseTokenString(token: Token, tokenString?: string): string {\r\n    if (this._scanner.token() === token) {\r\n      const text: string = this._scanner.tokenText;\r\n      const stringIsUnterminated: boolean = this._scanner.stringIsUnterminated;\r\n      this._scanner.scan();\r\n      if (stringIsUnterminated) {\r\n        return this.fail(`${tokenString || tokenToString(token)} is unterminated`, text);\r\n      }\r\n      return text;\r\n    }\r\n    return this.fail(`${tokenString || tokenToString(token)} expected`, '');\r\n  }\r\n\r\n  private parseText(): string {\r\n    return this.parseTokenString(Token.Text, 'Text');\r\n  }\r\n\r\n  private parseString(): string {\r\n    return this.parseTokenString(Token.String, 'String');\r\n  }\r\n\r\n  private parseComponent(): Component {\r\n    switch (this._scanner.token()) {\r\n      case Token.OpenBracketToken:\r\n        return this.parseBracketedComponent();\r\n      default:\r\n        return new ParsedComponentString(this.parseComponentString(), /*userEscaped*/ true);\r\n    }\r\n  }\r\n\r\n  private parseBracketedComponent(): ComponentReference {\r\n    this.expectToken(Token.OpenBracketToken);\r\n    const reference: DeclarationReference = this.parseDeclarationReference();\r\n    this.expectToken(Token.CloseBracketToken);\r\n    return new ComponentReference(reference);\r\n  }\r\n\r\n  private optionalToken(token: Token): boolean {\r\n    if (this._scanner.token() === token) {\r\n      this._scanner.scan();\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  private expectToken(token: Token, message?: string): void {\r\n    if (this._scanner.token() !== token) {\r\n      const expected: string = tokenToString(token);\r\n      const actual: string = tokenToString(this._scanner.token());\r\n      return this.fail(message || `Expected token '${expected}', received '${actual}' instead.`, undefined);\r\n    }\r\n    this._scanner.scan();\r\n  }\r\n\r\n  private fail<T>(message: string, fallback: T): T {\r\n    this._errors.push(message);\r\n    return fallback;\r\n  }\r\n}\r\n\r\nfunction formatNavigation(navigation: Navigation | undefined): string {\r\n  switch (navigation) {\r\n    case Navigation.Exports:\r\n      return '.';\r\n    case Navigation.Members:\r\n      return '#';\r\n    case Navigation.Locals:\r\n      return '~';\r\n    default:\r\n      return '';\r\n  }\r\n}\r\n\r\nfunction isCharacterEscapeSequence(ch: string): boolean {\r\n  return isSingleEscapeCharacter(ch) || isNonEscapeCharacter(ch);\r\n}\r\n\r\nfunction isSingleEscapeCharacter(ch: string): boolean {\r\n  switch (ch) {\r\n    case \"'\":\r\n    case '\"':\r\n    case '\\\\':\r\n    case 'b':\r\n    case 'f':\r\n    case 'n':\r\n    case 'r':\r\n    case 't':\r\n    case 'v':\r\n      return true;\r\n    default:\r\n      return false;\r\n  }\r\n}\r\n\r\nfunction isNonEscapeCharacter(ch: string): boolean {\r\n  return !isEscapeCharacter(ch) && !isLineTerminator(ch);\r\n}\r\n\r\nfunction isEscapeCharacter(ch: string): boolean {\r\n  switch (ch) {\r\n    case 'x':\r\n    case 'u':\r\n      return true;\r\n    default:\r\n      return isSingleEscapeCharacter(ch) || isDecimalDigit(ch);\r\n  }\r\n}\r\n\r\nfunction isLineTerminator(ch: string): boolean {\r\n  switch (ch) {\r\n    case '\\r':\r\n    case '\\n':\r\n      // TODO: <LS>, <PS>\r\n      return true;\r\n    default:\r\n      return false;\r\n  }\r\n}\r\n\r\nfunction isDecimalDigit(ch: string): boolean {\r\n  switch (ch) {\r\n    case '0':\r\n    case '1':\r\n    case '2':\r\n    case '3':\r\n    case '4':\r\n    case '5':\r\n    case '6':\r\n    case '7':\r\n    case '8':\r\n    case '9':\r\n      return true;\r\n    default:\r\n      return false;\r\n  }\r\n}\r\n\r\nfunction isHexDigit(ch: string): boolean {\r\n  switch (ch) {\r\n    case 'a':\r\n    case 'b':\r\n    case 'c':\r\n    case 'd':\r\n    case 'e':\r\n    case 'f':\r\n    case 'A':\r\n    case 'B':\r\n    case 'C':\r\n    case 'D':\r\n    case 'E':\r\n    case 'F':\r\n      return true;\r\n    default:\r\n      return isDecimalDigit(ch);\r\n  }\r\n}\r\n\r\nfunction isPunctuator(ch: string): boolean {\r\n  switch (ch) {\r\n    case '{':\r\n    case '}':\r\n    case '(':\r\n    case ')':\r\n    case '[':\r\n    case ']':\r\n    case '!':\r\n    case '.':\r\n    case '#':\r\n    case '~':\r\n    case ':':\r\n    case ',':\r\n    case '@':\r\n      return true;\r\n    default:\r\n      return false;\r\n  }\r\n}\r\n\r\nfunction escapeComponentIfNeeded(text: string, userEscaped?: boolean): string {\r\n  if (userEscaped) {\r\n    if (!DeclarationReference.isWellFormedComponentString(text)) {\r\n      throw new SyntaxError(`Invalid Component '${text}'`);\r\n    }\r\n    return text;\r\n  }\r\n  return DeclarationReference.escapeComponentString(text);\r\n}\r\n\r\nfunction escapeModuleSourceIfNeeded(text: string, userEscaped?: boolean): string {\r\n  if (userEscaped) {\r\n    if (!DeclarationReference.isWellFormedModuleSourceString(text)) {\r\n      throw new SyntaxError(`Invalid Module source '${text}'`);\r\n    }\r\n    return text;\r\n  }\r\n  return DeclarationReference.escapeModuleSourceString(text);\r\n}\r\n"]}