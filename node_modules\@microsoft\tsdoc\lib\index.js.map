{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EACL,cAAc,EAGf,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAEL,kBAAkB,EAClB,kBAAkB,EACnB,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,4BAA4B,EAAE,MAAM,8CAA8C,CAAC;AAE5F,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAC;AAC5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,kCAAkC,CAAC;AAC1E,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAE1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAuB,MAAM,0BAA0B,CAAC;AAC9E,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,cAAc,SAAS,CAAC;AAExB,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,aAAa,EAAiC,MAAM,wBAAwB,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,SAAS,EAAsB,MAAM,oBAAoB,CAAC;AACnE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAiC,MAAM,wBAAwB,CAAC;AACtF,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAEnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nexport {\r\n  DocNodeManager,\r\n  type IDocNodeDefinition,\r\n  type DocNodeConstructor\r\n} from './configuration/DocNodeManager';\r\nexport { TSDocConfiguration } from './configuration/TSDocConfiguration';\r\nexport {\r\n  type ITSDocTagDefinitionParameters,\r\n  TSDocTagSyntaxKind,\r\n  TSDocTagDefinition\r\n} from './configuration/TSDocTagDefinition';\r\nexport { TSDocValidationConfiguration } from './configuration/TSDocValidationConfiguration';\r\n\r\nexport { StandardTags } from './details/StandardTags';\r\nexport { Standardization } from './details/Standardization';\r\nexport { StandardModifierTagSet } from './details/StandardModifierTagSet';\r\nexport { ModifierTagSet } from './details/ModifierTagSet';\r\n\r\nexport { PlainTextEmitter } from './emitters/PlainTextEmitter';\r\nexport { StringBuilder, type IStringBuilder } from './emitters/StringBuilder';\r\nexport { TSDocEmitter } from './emitters/TSDocEmitter';\r\n\r\nexport * from './nodes';\r\n\r\nexport { ParserContext } from './parser/ParserContext';\r\nexport { ParserMessage, type IParserMessageParameters } from './parser/ParserMessage';\r\nexport { ParserMessageLog } from './parser/ParserMessageLog';\r\nexport { TextRange, type ITextLocation } from './parser/TextRange';\r\nexport { Token, TokenKind } from './parser/Token';\r\nexport { TokenSequence, type ITokenSequenceParameters } from './parser/TokenSequence';\r\nexport { TSDocMessageId } from './parser/TSDocMessageId';\r\nexport { TSDocParser } from './parser/TSDocParser';\r\n\r\nexport { DocNodeTransforms } from './transforms/DocNodeTransforms';\r\n"]}