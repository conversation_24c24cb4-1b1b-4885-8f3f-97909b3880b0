# 🚀 Sharyou Platform Setup Guide

## Prerequisites
- Node.js 18+ installed
- Git installed
- A Supabase account (free tier is sufficient)

## Step 1: Supabase Setup

### 1.1 Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Create a new organization (if needed)
4. Create a new project:
   - **Name**: Sharyou
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to Algeria (Europe West recommended)

### 1.2 Get Project Credentials
Once your project is created:
1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)
   - **Service role key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

### 1.3 Get Database URL
1. Go to **Settings** → **Database**
2. Scroll down to **Connection string**
3. Copy the **URI** format connection string
4. Replace `[YOUR-PASSWORD]` with your database password

## Step 2: Environment Configuration

### 2.1 Backend Environment
Update `apps/backend/.env`:
```env
# Database
DATABASE_URL="your-supabase-database-url-here"

# Supabase Configuration
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Application Configuration
NODE_ENV="development"
PORT=3001
CORS_ORIGIN="http://localhost:3000"

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp"

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100
```

### 2.2 Frontend Environment
Update `apps/frontend/.env.local`:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001/api

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=شاريو
```

## Step 3: Database Setup

### 3.1 Install Dependencies & Generate Database
```bash
# Install backend dependencies (if not already done)
cd apps/backend
npm install

# Generate Prisma client
npm run db:generate

# Push database schema to Supabase
npm run db:push

# Seed database with sample data
npm run db:seed
```

### 3.2 Configure Supabase Auth
1. Go to **Authentication** → **Settings** in your Supabase dashboard
2. Under **Site URL**, add: `http://localhost:3000`
3. Under **Redirect URLs**, add: `http://localhost:3000/auth/callback`

## Step 4: Install Frontend Dependencies
```bash
# Install frontend dependencies (if not already done)
cd apps/frontend
npm install
```

## Step 5: Start Development Servers

### Option 1: Start Both Servers Separately
```bash
# Terminal 1 - Backend
cd apps/backend
npm run start:dev

# Terminal 2 - Frontend
cd apps/frontend
npm run dev
```

### Option 2: Start Both Servers Together
```bash
# From project root
npm run dev
```

## Step 6: Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001/api
- **API Documentation**: http://localhost:3001/api/docs

## Step 7: Test the Setup

### 7.1 Test Registration
1. Go to http://localhost:3000
2. Click "إنشاء حساب" (Create Account)
3. Fill in the registration form
4. Check your email for verification link

### 7.2 Test Login
1. After email verification, go to login page
2. Use your credentials to log in
3. You should be redirected to the dashboard

### 7.3 Test API
1. Go to http://localhost:3001/api/docs
2. Explore the API endpoints
3. Test authentication endpoints

## Troubleshooting

### Common Issues

#### 1. Database Connection Error
- Verify your DATABASE_URL is correct
- Make sure your Supabase project is active
- Check if your IP is allowed (Supabase allows all IPs by default)

#### 2. Authentication Not Working
- Verify SUPABASE_URL and SUPABASE_ANON_KEY are correct
- Check if Site URL is configured in Supabase Auth settings
- Make sure JWT_SECRET is set in backend

#### 3. CORS Errors
- Verify CORS_ORIGIN is set to http://localhost:3000 in backend
- Check if frontend is running on port 3000

#### 4. Prisma Errors
- Run `npm run db:generate` to regenerate Prisma client
- Run `npm run db:push` to sync schema with database

### Getting Help
If you encounter issues:
1. Check the console logs in both frontend and backend
2. Verify all environment variables are set correctly
3. Make sure all dependencies are installed
4. Check if ports 3000 and 3001 are available

## Next Steps
Once everything is working:
1. Create your first product page
2. Add some products
3. Test the order flow
4. Explore the analytics dashboard
5. Customize the design and branding

## Production Deployment
For production deployment, you'll need to:
1. Set up proper environment variables for production
2. Configure a production database
3. Set up proper domain and SSL
4. Configure email services
5. Set up monitoring and logging

Happy coding! 🎉
