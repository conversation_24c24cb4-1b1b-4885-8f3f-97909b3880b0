"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../common/prisma/prisma.service");
const product_pages_service_1 = require("../product-pages/product-pages.service");
let ProductsService = class ProductsService {
    prisma;
    productPagesService;
    constructor(prisma, productPagesService) {
        this.prisma = prisma;
        this.productPagesService = productPagesService;
    }
    async create(userId, createProductDto) {
        const hasAccess = await this.productPagesService.checkOwnership(createProductDto.productPageId, userId);
        if (!hasAccess) {
            throw new common_1.ForbiddenException('You can only create products in your own product pages');
        }
        return this.prisma.product.create({
            data: createProductDto,
            include: {
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
                images: true,
                variants: true,
                productCategories: {
                    include: {
                        category: true,
                    },
                },
            },
        });
    }
    async findAll(productPageId) {
        const where = productPageId ? { productPageId } : {};
        return this.prisma.product.findMany({
            where,
            include: {
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
                images: true,
                variants: true,
                productCategories: {
                    include: {
                        category: true,
                    },
                },
                _count: {
                    select: {
                        reviews: true,
                        orderItems: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findByProductPage(productPageId, isActive) {
        const where = { productPageId };
        if (isActive !== undefined) {
            where.isActive = isActive;
        }
        return this.prisma.product.findMany({
            where,
            include: {
                images: true,
                variants: {
                    where: { isActive: true },
                    include: {
                        options: true,
                    },
                },
                productCategories: {
                    include: {
                        category: true,
                    },
                },
                reviews: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                    take: 5,
                },
                _count: {
                    select: {
                        reviews: true,
                        orderItems: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findOne(id) {
        const product = await this.prisma.product.findUnique({
            where: { id },
            include: {
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                        userId: true,
                    },
                },
                images: {
                    orderBy: {
                        position: 'asc',
                    },
                },
                variants: {
                    where: { isActive: true },
                    include: {
                        options: true,
                    },
                    orderBy: {
                        createdAt: 'asc',
                    },
                },
                productCategories: {
                    include: {
                        category: true,
                    },
                },
                reviews: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                },
                _count: {
                    select: {
                        reviews: true,
                        orderItems: true,
                    },
                },
            },
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with ID ${id} not found`);
        }
        return product;
    }
    async update(id, userId, updateProductDto) {
        const product = await this.prisma.product.findUnique({
            where: { id },
            include: {
                productPage: {
                    select: {
                        userId: true,
                    },
                },
            },
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with ID ${id} not found`);
        }
        if (product.productPage.userId !== userId) {
            throw new common_1.ForbiddenException('You can only update products in your own product pages');
        }
        return this.prisma.product.update({
            where: { id },
            data: updateProductDto,
            include: {
                productPage: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
                images: true,
                variants: true,
                productCategories: {
                    include: {
                        category: true,
                    },
                },
            },
        });
    }
    async remove(id, userId) {
        const product = await this.prisma.product.findUnique({
            where: { id },
            include: {
                productPage: {
                    select: {
                        userId: true,
                    },
                },
            },
        });
        if (!product) {
            throw new common_1.NotFoundException(`Product with ID ${id} not found`);
        }
        if (product.productPage.userId !== userId) {
            throw new common_1.ForbiddenException('You can only delete products in your own product pages');
        }
        return this.prisma.product.delete({
            where: { id },
        });
    }
    async search(productPageId, query) {
        return this.prisma.product.findMany({
            where: {
                productPageId,
                isActive: true,
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { nameAr: { contains: query, mode: 'insensitive' } },
                    { nameFr: { contains: query, mode: 'insensitive' } },
                    { description: { contains: query, mode: 'insensitive' } },
                    { descriptionAr: { contains: query, mode: 'insensitive' } },
                    { descriptionFr: { contains: query, mode: 'insensitive' } },
                    { tags: { has: query } },
                ],
            },
            include: {
                images: true,
                variants: {
                    where: { isActive: true },
                },
                productCategories: {
                    include: {
                        category: true,
                    },
                },
                _count: {
                    select: {
                        reviews: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        product_pages_service_1.ProductPagesService])
], ProductsService);
//# sourceMappingURL=products.service.js.map