exports.id=884,exports.ids=[884],exports.modules={1241:()=>{},2518:(e,t,r)=>{"use strict";r.d(t,{O:()=>m,A:()=>u});var s=r(13486),o=r(60159);let a=(0,r(95681).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),i=async()=>{let{error:e}=await a.auth.signOut();return{error:e}},n=async()=>{let{data:{user:e},error:t}=await a.auth.getUser();return{user:e,error:t}},l=r(93153).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)));let c={validateToken:e=>l.post("/auth/validate",{token:e})};var d=r(85499);let h=(0,o.createContext)(void 0);function m({children:e}){let[t,r]=(0,o.useState)(null),[l,m]=(0,o.useState)(!0),u=async()=>{try{let{user:e}=await n();if(r(e),e){let{data:{session:e}}=await a.auth.getSession();if(e?.access_token)try{let t=await c.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),r(null),localStorage.removeItem("auth_token")}},p=async(e,t)=>{try{let{data:r,error:s}=await a.auth.signInWithPassword({email:e,password:t});if(s)throw s;if(r.session?.access_token){let e=await c.validateToken(r.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الدخول"),e}},f=async(e,t,r)=>{try{let{data:s,error:o}=await a.auth.signUp({email:e,password:t,options:{data:r}});if(o)throw o;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(e){throw d.Ay.error(e.message||"فشل في إنشاء الحساب"),e}},v=async()=>{try{await i(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الخروج"),e}};return(0,s.jsx)(h.Provider,{value:{user:t,loading:l,signIn:p,signUp:f,signOut:v,refreshUser:u},children:e})}function u(){let e=(0,o.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4573:(e,t,r)=>{Promise.resolve().then(r.bind(r,49937))},4946:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69355,23)),Promise.resolve().then(r.t.bind(r,54439,23)),Promise.resolve().then(r.t.bind(r,67851,23)),Promise.resolve().then(r.t.bind(r,94730,23)),Promise.resolve().then(r.t.bind(r,19774,23)),Promise.resolve().then(r.t.bind(r,53170,23)),Promise.resolve().then(r.t.bind(r,20968,23)),Promise.resolve().then(r.t.bind(r,78298,23))},9699:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(41253);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},14276:()=>{},20685:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n});var s=r(38828),o=r(93390),a=r.n(o);r(14276);var i=r(52079);let n={title:"Sharyou - منصة التجارة الإلكترونية الجزائرية",description:"أنشئ متجرك الإلكتروني في دقائق مع شاريو - المنصة الجزائرية الرائدة للتجارة الإلكترونية",keywords:"تجارة إلكترونية, متجر إلكتروني, الجزائر, شاريو, بيع أونلاين"};function l({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",children:(0,s.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,s.jsx)(i.Providers,{children:e})})})}},26518:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(4627),o=r(55855);function a(...e){return(0,o.QP)((0,s.$)(e))}},26973:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(13486),o=r(60159),a=r.n(o),i=r(26518);let n=a().forwardRef(({className:e,variant:t="primary",size:r="md",loading:o,children:a,disabled:n,...l},c)=>(0,s.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[t],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[r],e),ref:c,disabled:n||o,...l,children:[o&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]}));n.displayName="Button"},49937:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>c});var s=r(13486),o=r(10803),a=r(84364),i=r(85499),n=r(2518),l=r(60159);function c({children:e}){let[t]=(0,l.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,s.jsx)(a.Ht,{client:t,children:(0,s.jsxs)(n.O,{children:[e,(0,s.jsx)(i.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})}},51802:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30385,23)),Promise.resolve().then(r.t.bind(r,33737,23)),Promise.resolve().then(r.t.bind(r,86081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,35856,23)),Promise.resolve().then(r.t.bind(r,55492,23)),Promise.resolve().then(r.t.bind(r,89082,23)),Promise.resolve().then(r.t.bind(r,45812,23))},52079:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx","Providers")},72088:()=>{},73115:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>h,ZB:()=>c,Zp:()=>n,aR:()=>l});var s=r(13486),o=r(60159),a=r.n(o),i=r(26518);let n=a().forwardRef(({className:e,children:t,...r},o)=>(0,s.jsx)("div",{ref:o,className:(0,i.cn)("rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm",e),...r,children:t}));n.displayName="Card";let l=a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let c=a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let d=a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,i.cn)("text-sm text-gray-500",e),...t}));d.displayName="CardDescription";let h=a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));h.displayName="CardContent",a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},76029:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=76029,e.exports=t},86013:(e,t,r)=>{Promise.resolve().then(r.bind(r,52079))}};