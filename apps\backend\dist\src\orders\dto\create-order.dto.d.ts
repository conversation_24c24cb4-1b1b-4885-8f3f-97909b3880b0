export declare class OrderItemDto {
    productId: string;
    variantId?: string;
    quantity: number;
}
export declare class CustomerInfoDto {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
}
export declare class AddressDto {
    firstName: string;
    lastName: string;
    company?: string;
    address1: string;
    address2?: string;
    city: string;
    wilaya: string;
    daira: string;
    commune: string;
    postalCode: string;
    phone: string;
}
export declare class CreateOrderDto {
    productPageId: string;
    items: OrderItemDto[];
    customerInfo?: CustomerInfoDto;
    paymentMethod: string;
    shippingAddress: AddressDto;
    billingAddress?: AddressDto;
    notes?: string;
    shippingAmount?: number;
    discountAmount?: number;
}
