'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { EnvelopeIcon } from '@heroicons/react/24/outline';

export default function VerifyEmailPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo */}
        <div className="text-center">
          <Link href="/" className="flex items-center justify-center space-x-2">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-2xl">ش</span>
            </div>
            <span className="text-3xl font-bold text-gray-900">شاريو</span>
          </Link>
        </div>

        <Card>
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <EnvelopeIcon className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl font-bold">تحقق من بريدك الإلكتروني</CardTitle>
            <CardDescription>
              لقد أرسلنا رابط التفعيل إلى بريدك الإلكتروني
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-6">
            <div className="text-sm text-gray-600">
              <p className="mb-4">
                يرجى فتح بريدك الإلكتروني والنقر على رابط التفعيل لإكمال إنشاء حسابك.
              </p>
              <p className="mb-4">
                إذا لم تجد الرسالة، تحقق من مجلد الرسائل غير المرغوب فيها (Spam).
              </p>
            </div>

            <div className="space-y-4">
              <Button variant="outline" className="w-full">
                إعادة إرسال رابط التفعيل
              </Button>
              
              <Link href="/auth/login">
                <Button variant="ghost" className="w-full">
                  العودة إلى تسجيل الدخول
                </Button>
              </Link>
            </div>

            <div className="text-xs text-gray-500">
              <p>
                إذا واجهت مشاكل في التفعيل، يرجى{' '}
                <Link href="/contact" className="text-blue-600 hover:text-blue-500">
                  التواصل معنا
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
