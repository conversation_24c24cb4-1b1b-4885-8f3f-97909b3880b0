import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create sample users (these will be created via Supabase Auth in real usage)
  const sampleUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001',
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'بن علي',
      phoneNumber: '+213555123456',
      role: 'vendor',
    },
  });

  console.log('✅ Created sample user:', sampleUser.email);

  // Create sample product page
  const sampleProductPage = await prisma.productPage.upsert({
    where: { slug: 'demo-store' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001',
      userId: sampleUser.id,
      slug: 'demo-store',
      name: 'متجر أحمد للإلكترونيات',
      description: 'متجر متخصص في بيع الأجهزة الإلكترونية والهواتف الذكية بأفضل الأسعار في الجزائر',
      templateData: {
        theme: 'modern',
        primaryColor: '#3B82F6',
        secondaryColor: '#1E40AF',
        layout: 'grid',
      },
      published: true,
    },
  });

  console.log('✅ Created sample product page:', sampleProductPage.name);

  // Create sample categories
  const electronicsCategory = await prisma.category.create({
    data: {
      productPageId: sampleProductPage.id,
      name: 'الإلكترونيات',
      nameAr: 'الإلكترونيات',
      nameFr: 'Électronique',
      slug: 'electronics',
      description: 'جميع الأجهزة الإلكترونية',
      position: 1,
    },
  });

  const phonesCategory = await prisma.category.create({
    data: {
      productPageId: sampleProductPage.id,
      name: 'الهواتف الذكية',
      nameAr: 'الهواتف الذكية',
      nameFr: 'Smartphones',
      slug: 'smartphones',
      description: 'أحدث الهواتف الذكية',
      parentId: electronicsCategory.id,
      position: 1,
    },
  });

  const accessoriesCategory = await prisma.category.create({
    data: {
      productPageId: sampleProductPage.id,
      name: 'الإكسسوارات',
      nameAr: 'الإكسسوارات',
      nameFr: 'Accessoires',
      slug: 'accessories',
      description: 'إكسسوارات الهواتف والأجهزة',
      parentId: electronicsCategory.id,
      position: 2,
    },
  });

  console.log('✅ Created sample categories');

  // Create sample products
  const sampleProducts = [
    {
      name: 'iPhone 15 Pro',
      nameAr: 'آيفون 15 برو',
      nameFr: 'iPhone 15 Pro',
      description: 'أحدث هاتف من آبل بتقنيات متطورة وكاميرا احترافية',
      descriptionAr: 'أحدث هاتف من آبل بتقنيات متطورة وكاميرا احترافية',
      descriptionFr: 'Le dernier iPhone d\'Apple avec des technologies avancées',
      price: 180000, // 180,000 DZD
      compareAtPrice: 200000,
      sku: 'IPH15PRO-128',
      stock: 10,
      tags: ['آيفون', 'آبل', 'هاتف ذكي', 'جديد'],
      categoryId: phonesCategory.id,
    },
    {
      name: 'Samsung Galaxy S24',
      nameAr: 'سامسونغ غالاكسي S24',
      nameFr: 'Samsung Galaxy S24',
      description: 'هاتف سامسونغ الرائد بشاشة Dynamic AMOLED وكاميرا 200 ميجابكسل',
      descriptionAr: 'هاتف سامسونغ الرائد بشاشة Dynamic AMOLED وكاميرا 200 ميجابكسل',
      descriptionFr: 'Smartphone Samsung haut de gamme avec écran Dynamic AMOLED',
      price: 150000, // 150,000 DZD
      compareAtPrice: 170000,
      sku: 'SGS24-256',
      stock: 15,
      tags: ['سامسونغ', 'غالاكسي', 'هاتف ذكي', 'أندرويد'],
      categoryId: phonesCategory.id,
    },
    {
      name: 'شاحن لاسلكي سريع',
      nameAr: 'شاحن لاسلكي سريع',
      nameFr: 'Chargeur sans fil rapide',
      description: 'شاحن لاسلكي سريع 15W متوافق مع جميع الهواتف الذكية',
      descriptionAr: 'شاحن لاسلكي سريع 15W متوافق مع جميع الهواتف الذكية',
      descriptionFr: 'Chargeur sans fil rapide 15W compatible avec tous les smartphones',
      price: 3500, // 3,500 DZD
      compareAtPrice: 4000,
      sku: 'WC-15W-001',
      stock: 50,
      tags: ['شاحن', 'لاسلكي', 'إكسسوار'],
      categoryId: accessoriesCategory.id,
    },
  ];

  for (const productData of sampleProducts) {
    const { categoryId, ...productCreateData } = productData;
    
    const product = await prisma.product.create({
      data: {
        ...productCreateData,
        productPageId: sampleProductPage.id,
      },
    });

    // Link product to category
    await prisma.productCategory.create({
      data: {
        productId: product.id,
        categoryId: categoryId,
      },
    });

    console.log('✅ Created product:', product.name);
  }

  // Create sample analytics data for the last 30 days
  const today = new Date();
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    date.setHours(0, 0, 0, 0);

    const visitors = Math.floor(Math.random() * 100) + 20;
    const pageViews = Math.floor(visitors * (1.2 + Math.random() * 0.8));
    const orders = Math.floor(Math.random() * 5);
    const revenue = orders * (50000 + Math.random() * 100000);

    await prisma.analyticMetric.create({
      data: {
        productPageId: sampleProductPage.id,
        date: date,
        visitors: visitors,
        pageViews: pageViews,
        orders: orders,
        revenue: revenue,
        conversionRate: orders > 0 ? (orders / visitors) * 100 : 0,
      },
    });
  }

  console.log('✅ Created sample analytics data');

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
