// Core User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Product Page Types
export interface ProductPage {
  id: string;
  userId: string;
  name: string;
  slug: string;
  description?: string;
  subdomain: string;
  customDomain?: string;
  isActive: boolean;
  theme: ProductPageTheme;
  settings: ProductPageSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductPageTheme {
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
  layout: 'grid' | 'list' | 'masonry';
  headerStyle: 'minimal' | 'classic' | 'modern';
}

export interface ProductPageSettings {
  currency: 'DZD';
  language: 'ar-DZ' | 'fr-DZ';
  paymentMethods: PaymentMethod[];
  shippingZones: ShippingZone[];
  taxRate?: number;
  enableInventoryTracking: boolean;
  enableReviews: boolean;
  enableWishlist: boolean;
}

// Product Types
export interface Product {
  id: string;
  productPageId: string;
  name: string;
  nameAr?: string;
  nameFr?: string;
  description: string;
  descriptionAr?: string;
  descriptionFr?: string;
  price: number;
  compareAtPrice?: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  dimensions?: ProductDimensions;
  images: ProductImage[];
  variants: ProductVariant[];
  categories: ProductCategory[];
  tags: string[];
  inventory: ProductInventory;
  seoSettings: ProductSEO;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'in';
}

export interface ProductImage {
  id: string;
  url: string;
  altText?: string;
  position: number;
}

export interface ProductVariant {
  id: string;
  name: string;
  price: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  inventory: ProductInventory;
  options: VariantOption[];
}

export interface VariantOption {
  name: string;
  value: string;
}

export interface ProductCategory {
  id: string;
  name: string;
  nameAr?: string;
  nameFr?: string;
  slug: string;
  description?: string;
  parentId?: string;
  image?: string;
  position: number;
}

export interface ProductInventory {
  quantity: number;
  trackQuantity: boolean;
  allowBackorder: boolean;
  lowStockThreshold?: number;
}

export interface ProductSEO {
  title?: string;
  description?: string;
  keywords?: string[];
}

// Order Types
export interface Order {
  id: string;
  productPageId: string;
  orderNumber: string;
  customerId?: string;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  total: number;
  currency: 'DZD';
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: PaymentMethod;
  shippingAddress: Address;
  billingAddress?: Address;
  shippingMethod: ShippingMethod;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  variantId?: string;
  name: string;
  price: number;
  quantity: number;
  total: number;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partially_refunded';

export type PaymentMethod = 
  | 'cod'
  | 'cib'
  | 'baridimob'
  | 'bank_transfer';

// Address Types (Algerian-specific)
export interface Address {
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  wilaya: string;
  daira: string;
  commune: string;
  postalCode: string;
  phone: string;
}

export interface ShippingZone {
  id: string;
  name: string;
  wilayas: string[];
  methods: ShippingMethod[];
}

export interface ShippingMethod {
  id: string;
  name: string;
  description?: string;
  price: number;
  estimatedDays: number;
  isActive: boolean;
}

// Analytics Types
export interface Analytics {
  productPageId: string;
  period: AnalyticsPeriod;
  metrics: AnalyticsMetrics;
  topProducts: ProductAnalytics[];
  salesByDay: DailySales[];
  trafficSources: TrafficSource[];
}

export type AnalyticsPeriod = '7d' | '30d' | '90d' | '1y';

export interface AnalyticsMetrics {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  conversionRate: number;
  totalVisitors: number;
  returningCustomers: number;
}

export interface ProductAnalytics {
  productId: string;
  name: string;
  sales: number;
  orders: number;
  views: number;
}

export interface DailySales {
  date: string;
  sales: number;
  orders: number;
  visitors: number;
}

export interface TrafficSource {
  source: string;
  visitors: number;
  percentage: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface CreateProductPageDto {
  name: string;
  description?: string;
  subdomain: string;
  theme: Partial<ProductPageTheme>;
  settings: Partial<ProductPageSettings>;
}

export interface UpdateProductPageDto extends Partial<CreateProductPageDto> {
  isActive?: boolean;
  customDomain?: string;
}

export interface CreateProductDto {
  name: string;
  nameAr?: string;
  nameFr?: string;
  description: string;
  descriptionAr?: string;
  descriptionFr?: string;
  price: number;
  compareAtPrice?: number;
  sku?: string;
  weight?: number;
  dimensions?: ProductDimensions;
  categoryIds: string[];
  tags: string[];
  inventory: ProductInventory;
  seoSettings?: ProductSEO;
}

export interface UpdateProductDto extends Partial<CreateProductDto> {
  isActive?: boolean;
}

// Authentication Types
export interface AuthenticatedUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'admin' | 'vendor';
}
