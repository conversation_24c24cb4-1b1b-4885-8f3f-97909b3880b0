import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SupabaseService } from './supabase.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private supabaseService: SupabaseService,
    private usersService: UsersService,
  ) {}

  async validateUser(token: string) {
    try {
      const supabaseUser = await this.supabaseService.verifyToken(token);

      if (!supabaseUser) {
        throw new UnauthorizedException('Invalid token');
      }

      // Get or create user in our database
      let user = await this.usersService.findById(supabaseUser.id);

      if (!user) {
        user = await this.usersService.create({
          id: supabaseUser.id,
          email: supabaseUser.email!,
          firstName: supabaseUser.user_metadata?.firstName,
          lastName: supabaseUser.user_metadata?.lastName,
          phoneNumber: supabaseUser.user_metadata?.phoneNumber,
        });
      }

      return user;
    } catch (error) {
      console.error('Authentication error:', error);

      // Check if it's a network/timeout error
      if (error.message?.includes('timeout') || error.message?.includes('fetch failed')) {
        throw new UnauthorizedException('Authentication service temporarily unavailable');
      }

      throw new UnauthorizedException('Authentication failed');
    }
  }

  async generateToken(user: any) {
    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
    };
  }
}
