import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ProductPagesService } from './product-pages.service';
import { CreateProductPageDto } from './dto/create-product-page.dto';
import { UpdateProductPageDto } from './dto/update-product-page.dto';
import { Request as ExpressRequest } from 'express';
import { AuthenticatedUser } from '@sharyou/types';

@ApiTags('Product Pages')
@Controller('product-pages')
export class ProductPagesController {
  constructor(private readonly productPagesService: ProductPagesService) {}

  @Post()
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new product page' })
  @ApiResponse({ status: 201, description: 'Product page created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Slug or domain already taken' })
  create(@Request() req: ExpressRequest, @Body() createProductPageDto: CreateProductPageDto) {
    return this.productPagesService.create((req.user as AuthenticatedUser).id, createProductPageDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all product pages' })
  @ApiResponse({ status: 200, description: 'Product pages retrieved successfully' })
  findAll() {
    return this.productPagesService.findAll();
  }

  @Get('by-slug/:slug')
  @ApiOperation({ summary: 'Get product page by slug (public endpoint)' })
  @ApiResponse({ status: 200, description: 'Product page retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Product page not found' })
  findBySlug(@Param('slug') slug: string) {
    return this.productPagesService.findBySlug(slug);
  }

  @Get('by-domain')
  @ApiOperation({ summary: 'Get product page by custom domain (public endpoint)' })
  @ApiQuery({ name: 'domain', description: 'Custom domain to search for' })
  @ApiResponse({ status: 200, description: 'Product page retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Product page not found' })
  findByDomain(@Query('domain') domain: string) {
    return this.productPagesService.findByDomain(domain);
  }

  @Get('my-pages')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user product pages' })
  @ApiResponse({ status: 200, description: 'Product pages retrieved successfully' })
  findMyPages(@Request() req: ExpressRequest) {
    return this.productPagesService.findByUserId((req.user as AuthenticatedUser).id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get product page by ID' })
  @ApiResponse({ status: 200, description: 'Product page retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Product page not found' })
  findOne(@Param('id') id: string) {
    return this.productPagesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update product page' })
  @ApiResponse({ status: 200, description: 'Product page updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not the owner' })
  @ApiResponse({ status: 404, description: 'Product page not found' })
  @ApiResponse({ status: 409, description: 'Slug or domain already taken' })
  update(
    @Param('id') id: string,
    @Request() req: ExpressRequest,
    @Body() updateProductPageDto: UpdateProductPageDto,
  ) {
    return this.productPagesService.update(id, (req.user as AuthenticatedUser).id, updateProductPageDto);
  }

  @Delete(':id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete product page' })
  @ApiResponse({ status: 200, description: 'Product page deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not the owner' })
  @ApiResponse({ status: 404, description: 'Product page not found' })
  remove(@Param('id') id: string, @Request() req: ExpressRequest) {
    return this.productPagesService.remove(id, (req.user as AuthenticatedUser).id);
  }
}
