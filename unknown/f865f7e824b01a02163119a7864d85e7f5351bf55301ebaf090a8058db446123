#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Sharyou Platform Setup Verification\n');

// Check if required files exist
const requiredFiles = [
  'apps/backend/.env',
  'apps/frontend/.env.local',
  'apps/backend/prisma/schema.prisma',
  'apps/backend/package.json',
  'apps/frontend/package.json',
  'packages/types/package.json',
  'packages/utils/package.json',
];

let allFilesExist = true;

console.log('📁 Checking required files...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

// Check environment variables
console.log('\n🔧 Checking environment variables...');

// Backend env check
if (fs.existsSync('apps/backend/.env')) {
  const backendEnv = fs.readFileSync('apps/backend/.env', 'utf8');
  const requiredBackendVars = [
    'DATABASE_URL',
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET'
  ];
  
  requiredBackendVars.forEach(varName => {
    if (backendEnv.includes(varName)) {
      const value = backendEnv.match(new RegExp(`${varName}=(.+)`))?.[1];
      if (value && !value.includes('your-') && !value.includes('change-this')) {
        console.log(`✅ Backend ${varName} - configured`);
      } else {
        console.log(`⚠️  Backend ${varName} - needs configuration`);
      }
    } else {
      console.log(`❌ Backend ${varName} - missing`);
    }
  });
}

// Frontend env check
if (fs.existsSync('apps/frontend/.env.local')) {
  const frontendEnv = fs.readFileSync('apps/frontend/.env.local', 'utf8');
  const requiredFrontendVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_API_URL'
  ];
  
  requiredFrontendVars.forEach(varName => {
    if (frontendEnv.includes(varName)) {
      const value = frontendEnv.match(new RegExp(`${varName}=(.+)`))?.[1];
      if (value && !value.includes('your-')) {
        console.log(`✅ Frontend ${varName} - configured`);
      } else {
        console.log(`⚠️  Frontend ${varName} - needs configuration`);
      }
    } else {
      console.log(`❌ Frontend ${varName} - missing`);
    }
  });
}

// Check node_modules
console.log('\n📦 Checking dependencies...');
const nodeModulesPaths = [
  'node_modules',
  'apps/backend/node_modules',
  'apps/frontend/node_modules',
];

nodeModulesPaths.forEach(modulePath => {
  if (fs.existsSync(modulePath)) {
    console.log(`✅ ${modulePath}`);
  } else {
    console.log(`❌ ${modulePath} - run npm install`);
  }
});

// Summary
console.log('\n📋 Setup Summary:');
if (allFilesExist) {
  console.log('✅ All required files are present');
} else {
  console.log('❌ Some required files are missing');
}

console.log('\n🚀 Next Steps:');
console.log('1. Configure your Supabase credentials in environment files');
console.log('2. Run: cd apps/backend && npm run db:generate && npm run db:push');
console.log('3. Run: npm run dev (from project root)');
console.log('4. Visit: http://localhost:3000');

console.log('\n📚 For detailed setup instructions, see: setup.md');
