'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';
import {
  ShoppingBagIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  EyeIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

export default function DashboardPage() {
  const { user } = useAuth();

  // Mock data - will be replaced with real API calls
  const stats = [
    {
      name: 'إجمالي المتاجر',
      value: '2',
      icon: ShoppingBagIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'إجمالي المبيعات',
      value: '45,230 د.ج',
      icon: CurrencyDollarIcon,
      color: 'bg-green-500',
    },
    {
      name: 'الطلبات هذا الشهر',
      value: '12',
      icon: ChartBarIcon,
      color: 'bg-purple-500',
    },
    {
      name: 'الزوار هذا الشهر',
      value: '1,234',
      icon: EyeIcon,
      color: 'bg-orange-500',
    },
  ];

  const recentStores = [
    {
      id: '1',
      name: 'متجر الإلكترونيات',
      slug: 'electronics-store',
      status: 'منشور',
      lastUpdated: '2024-01-15',
    },
    {
      id: '2',
      name: 'متجر الأزياء',
      slug: 'fashion-store',
      status: 'مسودة',
      lastUpdated: '2024-01-10',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          مرحباً، {user?.user_metadata?.firstName || 'صديقي'}! 👋
        </h1>
        <p className="text-gray-600">
          إليك نظرة سريعة على أداء متاجرك الإلكترونية
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.name}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-md ${stat.color}`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Stores */}
        <Card>
          <CardHeader>
            <CardTitle>متاجري الأخيرة</CardTitle>
            <CardDescription>
              المتاجر التي تم إنشاؤها أو تحديثها مؤخراً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentStores.map((store) => (
                <div key={store.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900">{store.name}</h3>
                    <p className="text-sm text-gray-500">/{store.slug}</p>
                    <p className="text-xs text-gray-400">آخر تحديث: {store.lastUpdated}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      store.status === 'منشور' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {store.status}
                    </span>
                    <Link href={`/dashboard/stores/${store.id}`}>
                      <Button variant="outline" size="sm">
                        إدارة
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
              
              <Link href="/dashboard/stores">
                <Button variant="ghost" className="w-full">
                  عرض جميع المتاجر
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات سريعة</CardTitle>
            <CardDescription>
              الإجراءات الأكثر استخداماً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Link href="/dashboard/stores/new">
                <Button className="w-full justify-start" size="lg">
                  <PlusIcon className="w-5 h-5 ml-2" />
                  إنشاء متجر جديد
                </Button>
              </Link>
              
              <Link href="/dashboard/products/new">
                <Button variant="outline" className="w-full justify-start" size="lg">
                  <ShoppingBagIcon className="w-5 h-5 ml-2" />
                  إضافة منتج جديد
                </Button>
              </Link>
              
              <Link href="/dashboard/analytics">
                <Button variant="outline" className="w-full justify-start" size="lg">
                  <ChartBarIcon className="w-5 h-5 ml-2" />
                  عرض التحليلات
                </Button>
              </Link>
              
              <Link href="/dashboard/settings">
                <Button variant="ghost" className="w-full justify-start" size="lg">
                  إعدادات الحساب
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle>البدء مع شاريو</CardTitle>
          <CardDescription>
            خطوات بسيطة لإطلاق متجرك الإلكتروني
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-medium mb-2">أنشئ متجرك</h3>
              <p className="text-sm text-gray-600">اختر اسماً وتصميماً لمتجرك</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 font-bold">2</span>
              </div>
              <h3 className="font-medium mb-2">أضف منتجاتك</h3>
              <p className="text-sm text-gray-600">ارفع صور منتجاتك وأضف الأوصاف</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-purple-600 font-bold">3</span>
              </div>
              <h3 className="font-medium mb-2">انشر وابدأ البيع</h3>
              <p className="text-sm text-gray-600">شارك رابط متجرك واستقبل الطلبات</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
