import{createContext as t,useContext as n,use<PERSON>emo as i}from"react";import{useOnUnmount as c}from'../../hooks/use-on-unmount.js';import{PopoverMachine as p}from'./popover-machine.js';const a=t(null);function u(r){let o=n(a);if(o===null){let e=new Error(`<${r} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,u),e}return o}function f({id:r,__demoMode:o=!1}){let e=i(()=>p.new({id:r,__demoMode:o}),[]);return c(()=>e.dispose()),e}export{a as PopoverContext,f as usePopoverMachine,u as usePopoverMachineContext};
