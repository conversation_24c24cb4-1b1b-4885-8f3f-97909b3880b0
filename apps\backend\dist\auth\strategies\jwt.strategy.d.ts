import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../supabase.service';
import { UsersService } from '../../users/users.service';
import { AuthenticatedUser } from '@sharyou/types';
interface JwtPayload {
    sub: string;
    email: string;
    role: string;
}
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    private supabaseService;
    private usersService;
    constructor(configService: ConfigService, supabaseService: SupabaseService, usersService: UsersService);
    validate(req: any, payload: JwtPayload): Promise<AuthenticatedUser>;
}
export {};
