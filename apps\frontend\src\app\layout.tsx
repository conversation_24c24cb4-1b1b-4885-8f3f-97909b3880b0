import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers/Providers";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Sharyou - منصة التجارة الإلكترونية الجزائرية",
  description: "أنشئ متجرك الإلكتروني في دقائق مع شاريو - المنصة الجزائرية الرائدة للتجارة الإلكترونية",
  keywords: "تجارة إلكترونية, متجر إلكتروني, الجزائر, شاريو, بيع أونلاين",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.variable} font-sans antialiased`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
