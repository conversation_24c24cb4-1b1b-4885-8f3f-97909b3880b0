import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const authApi = {
  validateToken: (token: string) => api.post('/auth/validate', { token }),
  getProfile: () => api.get('/auth/profile'),
};

export const usersApi = {
  getProfile: () => api.get('/users/me'),
  updateProfile: (data: any) => api.patch('/users/me', data),
  getProductPages: () => api.get('/users/me/product-pages'),
};

export const productPagesApi = {
  create: (data: any) => api.post('/product-pages', data),
  getAll: () => api.get('/product-pages'),
  getBySlug: (slug: string) => api.get(`/product-pages/by-slug/${slug}`),
  getByDomain: (domain: string) => api.get(`/product-pages/by-domain?domain=${domain}`),
  getMyPages: () => api.get('/product-pages/my-pages'),
  getById: (id: string) => api.get(`/product-pages/${id}`),
  update: (id: string, data: any) => api.patch(`/product-pages/${id}`, data),
  delete: (id: string) => api.delete(`/product-pages/${id}`),
};

export const productsApi = {
  create: (data: any) => api.post('/products', data),
  getAll: (productPageId?: string) => api.get('/products', { params: { productPageId } }),
  getByProductPage: (productPageId: string, isActive?: boolean) => 
    api.get(`/products/by-product-page/${productPageId}`, { params: { isActive } }),
  search: (productPageId: string, query: string) => 
    api.get(`/products/search/${productPageId}`, { params: { q: query } }),
  getById: (id: string) => api.get(`/products/${id}`),
  update: (id: string, data: any) => api.patch(`/products/${id}`, data),
  delete: (id: string) => api.delete(`/products/${id}`),
};

export const ordersApi = {
  create: (data: any) => api.post('/orders', data),
  getAll: (productPageId?: string) => api.get('/orders', { params: { productPageId } }),
  getMyOrders: () => api.get('/orders/my-orders'),
  getByProductPage: (productPageId: string) => api.get(`/orders/by-product-page/${productPageId}`),
  getById: (id: string) => api.get(`/orders/${id}`),
  update: (id: string, data: any) => api.patch(`/orders/${id}`, data),
};

export const analyticsApi = {
  getDashboard: (productPageId: string) => api.get(`/analytics/dashboard/${productPageId}`),
  getRevenueChart: (productPageId: string, days?: number) => 
    api.get(`/analytics/revenue-chart/${productPageId}`, { params: { days } }),
  getOrderStatus: (productPageId: string) => api.get(`/analytics/order-status/${productPageId}`),
  recordPageView: (productPageId: string) => api.post(`/analytics/page-view/${productPageId}`),
  recordVisitor: (productPageId: string) => api.post(`/analytics/visitor/${productPageId}`),
};
