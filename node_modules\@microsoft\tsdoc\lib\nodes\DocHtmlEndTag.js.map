{"version": 3, "file": "DocHtmlEndTag.js", "sourceRoot": "", "sources": ["../../src/nodes/DocHtmlEndTag.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,OAAO,EAAE,WAAW,EAA0D,MAAM,WAAW,CAAC;AAEzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAqBxD;;GAEG;AACH;IAAmC,iCAAO;IAYxC;;;OAGG;IACH,uBAAmB,UAAqE;QACtF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,2BAA2B;gBACpD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;YACH,KAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,eAAe;gBACxC,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBACvC,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;oBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,uBAAuB;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,2BAA2B;gBACpD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;QAC/B,CAAC;;IACH,CAAC;IAGD,sBAAW,+BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,UAAU,CAAC;QAChC,CAAC;;;OAAA;IAKD,sBAAW,+BAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrD,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAED;;OAEG;IACI,kCAAU,GAAjB;QACE,qGAAqG;QACrG,IAAM,aAAa,GAAkB,IAAI,aAAa,EAAE,CAAC;QACzD,IAAM,OAAO,GAAiB,IAAI,YAAY,EAAE,CAAC;QACjD,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,aAAa,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,gBAAgB;IACN,uCAAe,GAAzB;QACE,OAAO;YACL,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,wBAAwB;SAC9B,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AApFD,CAAmC,OAAO,GAoFzC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNode, DocNodeKind, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport { StringBuilder } from '../emitters/StringBuilder';\r\nimport { TSDocEmitter } from '../emitters/TSDocEmitter';\r\n\r\n/**\r\n * Constructor parameters for {@link DocHtmlEndTag}.\r\n */\r\nexport interface IDocHtmlEndTagParameters extends IDocNodeParameters {\r\n  name: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocHtmlEndTag}.\r\n */\r\nexport interface IDocHtmlEndTagParsedParameters extends IDocNodeParsedParameters {\r\n  openingDelimiterExcerpt: TokenSequence;\r\n\r\n  nameExcerpt: TokenSequence;\r\n  spacingAfterNameExcerpt?: TokenSequence;\r\n\r\n  closingDelimiterExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents an HTML end tag.  Example: `</a>`\r\n */\r\nexport class DocHtmlEndTag extends DocNode {\r\n  // The \"</\" delimiter and padding\r\n  private readonly _openingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  // The element name\r\n  private _name: string | undefined;\r\n  private readonly _nameExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  // The  \">\" delimiter and padding\r\n  private readonly _closingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocHtmlEndTagParameters | IDocHtmlEndTagParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._openingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlEndTag_OpeningDelimiter,\r\n        content: parameters.openingDelimiterExcerpt\r\n      });\r\n      this._nameExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlEndTag_Name,\r\n        content: parameters.nameExcerpt\r\n      });\r\n\r\n      if (parameters.spacingAfterNameExcerpt) {\r\n        this._spacingAfterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterNameExcerpt\r\n        });\r\n      }\r\n\r\n      this._closingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlEndTag_ClosingDelimiter,\r\n        content: parameters.closingDelimiterExcerpt\r\n      });\r\n    } else {\r\n      this._name = parameters.name;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.HtmlEndTag;\r\n  }\r\n\r\n  /**\r\n   * The HTML element name.\r\n   */\r\n  public get name(): string {\r\n    if (this._name === undefined) {\r\n      this._name = this._nameExcerpt!.content.toString();\r\n    }\r\n    return this._name;\r\n  }\r\n\r\n  /**\r\n   * Generates the HTML for this tag.\r\n   */\r\n  public emitAsHtml(): string {\r\n    // NOTE: Here we're assuming that the TSDoc representation for a tag is also a valid HTML expression.\r\n    const stringBuilder: StringBuilder = new StringBuilder();\r\n    const emitter: TSDocEmitter = new TSDocEmitter();\r\n    emitter.renderHtmlTag(stringBuilder, this);\r\n    return stringBuilder.toString();\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._openingDelimiterExcerpt,\r\n      this._nameExcerpt,\r\n      this._spacingAfterNameExcerpt,\r\n      this._closingDelimiterExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}