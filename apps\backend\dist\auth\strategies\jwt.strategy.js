"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const supabase_service_1 = require("../supabase.service");
const users_service_1 = require("../../users/users.service");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    configService;
    supabaseService;
    usersService;
    constructor(configService, supabaseService, usersService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET') || 'YOUR_SECRET_KEY',
            passReqToCallback: true,
        });
        this.configService = configService;
        this.supabaseService = supabaseService;
        this.usersService = usersService;
    }
    async validate(req, payload) {
        try {
            const token = passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken()(req);
            if (!token) {
                throw new common_1.UnauthorizedException('No token provided');
            }
            const supabaseUser = await this.supabaseService.verifyToken(token);
            if (!supabaseUser) {
                throw new common_1.UnauthorizedException('Invalid token');
            }
            let user = await this.usersService.findById(supabaseUser.id);
            if (!user) {
                user = await this.usersService.create({
                    id: supabaseUser.id,
                    email: supabaseUser.email,
                    firstName: supabaseUser.user_metadata?.firstName,
                    lastName: supabaseUser.user_metadata?.lastName,
                    phoneNumber: supabaseUser.user_metadata?.phoneNumber,
                });
            }
            return {
                id: user.id,
                email: user.email,
                firstName: user.firstName || undefined,
                lastName: user.lastName || undefined,
                role: user.role,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        supabase_service_1.SupabaseService,
        users_service_1.UsersService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map