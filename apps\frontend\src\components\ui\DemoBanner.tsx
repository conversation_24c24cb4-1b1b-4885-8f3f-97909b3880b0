'use client';

import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

export function DemoBanner() {
  const [isVisible, setIsVisible] = useState(true);

  // Only show if Supabase is not configured
  const isDemo = !process.env.NEXT_PUBLIC_SUPABASE_URL || 
                 process.env.NEXT_PUBLIC_SUPABASE_URL.includes('your-project');

  if (!isDemo || !isVisible) {
    return null;
  }

  return (
    <div className="bg-yellow-50 border-b border-yellow-200">
      <div className="max-w-7xl mx-auto py-3 px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between flex-wrap">
          <div className="w-0 flex-1 flex items-center">
            <span className="flex p-2 rounded-lg bg-yellow-400">
              <ExclamationTriangleIcon className="h-6 w-6 text-yellow-800" aria-hidden="true" />
            </span>
            <p className="mr-3 font-medium text-yellow-800">
              <span className="md:hidden">وضع التجربة نشط</span>
              <span className="hidden md:inline">
                أنت في وضع التجربة. لتفعيل جميع المميزات، يرجى إعداد Supabase.
              </span>
            </p>
          </div>
          <div className="order-3 mt-2 flex-shrink-0 w-full sm:order-2 sm:mt-0 sm:w-auto">
            <a
              href="/setup"
              className="flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-yellow-800 bg-yellow-100 hover:bg-yellow-200"
            >
              دليل الإعداد
            </a>
          </div>
          <div className="order-2 flex-shrink-0 sm:order-3 sm:mr-3">
            <button
              type="button"
              className="-ml-1 flex p-2 rounded-md hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-600 sm:-mr-2"
              onClick={() => setIsVisible(false)}
            >
              <span className="sr-only">إغلاق</span>
              <XMarkIcon className="h-6 w-6 text-yellow-800" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
