{"version": 3, "file": "DocBlockTag.js", "sourceRoot": "", "sources": ["../../src/nodes/DocBlockTag.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,qCAAyG;AACzG,uDAAsD;AAEtD,2CAAuD;AAiBvD;;GAEG;AACH;IAAiC,+BAAO;IAKtC;;;OAGG;IACH,qBAAmB,UAAiE;QAClF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,2BAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACtD,KAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;QACnC,KAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE9D,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,eAAe,GAAG,IAAI,uBAAU,CAAC;gBACpC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,QAAQ;gBACjC,OAAO,EAAE,UAAU,CAAC,cAAc;aACnC,CAAC,CAAC;QACL,CAAC;;IACH,CAAC;IAGD,sBAAW,6BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,QAAQ,CAAC;QAC9B,CAAC;;;OAAA;IAMD,sBAAW,gCAAO;QAJlB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;;;OAAA;IAMD,sBAAW,6CAAoB;QAJ/B;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;;;OAAA;IAED,gBAAgB;IACN,qCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAChC,CAAC;IAEM,sCAAgB,GAAvB;QACE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CACb,iGAAiG,CAClG,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;IACtC,CAAC;IACH,kBAAC;AAAD,CAAC,AA3DD,CAAiC,iBAAO,GA2DvC;AA3DY,kCAAW", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICEN<PERSON> in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport { StringChecks } from '../parser/StringChecks';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocBlockTag}.\r\n */\r\nexport interface IDocBlockTagParameters extends IDocNodeParameters {\r\n  tagName: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocBlockTag}.\r\n */\r\nexport interface IDocBlockTagParsedParameters extends IDocNodeParsedParameters {\r\n  tagName: string;\r\n  tagNameExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a TSDoc block tag such as `@param` or `@public`.\r\n */\r\nexport class DocBlockTag extends DocNode {\r\n  private readonly _tagName: string;\r\n  private readonly _tagNameWithUpperCase: string;\r\n  private readonly _tagNameExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocBlockTagParameters | IDocBlockTagParsedParameters) {\r\n    super(parameters);\r\n\r\n    StringChecks.validateTSDocTagName(parameters.tagName);\r\n    this._tagName = parameters.tagName;\r\n    this._tagNameWithUpperCase = parameters.tagName.toUpperCase();\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._tagNameExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.BlockTag,\r\n        content: parameters.tagNameExcerpt\r\n      });\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.BlockTag;\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tag name.  TSDoc tag names start with an at-sign (`@`) followed\r\n   * by ASCII letters using \"camelCase\" capitalization.\r\n   */\r\n  public get tagName(): string {\r\n    return this._tagName;\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tag name in all capitals, which is used for performing\r\n   * case-insensitive comparisons or lookups.\r\n   */\r\n  public get tagNameWithUpperCase(): string {\r\n    return this._tagNameWithUpperCase;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._tagNameExcerpt];\r\n  }\r\n\r\n  public getTokenSequence(): TokenSequence {\r\n    if (!this._tagNameExcerpt) {\r\n      throw new Error(\r\n        'DocBlockTag.getTokenSequence() failed because this object did not originate from a parsed input'\r\n      );\r\n    }\r\n    return this._tagNameExcerpt.content;\r\n  }\r\n}\r\n"]}