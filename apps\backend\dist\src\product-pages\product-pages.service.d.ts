import { PrismaService } from '../common/prisma/prisma.service';
import { CreateProductPageDto } from './dto/create-product-page.dto';
import { UpdateProductPageDto } from './dto/update-product-page.dto';
export declare class ProductPagesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(userId: string, createProductPageDto: CreateProductPageDto): Promise<any>;
    findAll(): Promise<any>;
    findByUserId(userId: string): Promise<any>;
    findOne(id: string): Promise<any>;
    findBySlug(slug: string): Promise<any>;
    findByDomain(domain: string): Promise<any>;
    update(id: string, userId: string, updateProductPageDto: UpdateProductPageDto): Promise<any>;
    remove(id: string, userId: string): Promise<any>;
    checkOwnership(productPageId: string, userId: string): Promise<boolean>;
}
