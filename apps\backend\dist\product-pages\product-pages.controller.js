"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductPagesController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const product_pages_service_1 = require("./product-pages.service");
const create_product_page_dto_1 = require("./dto/create-product-page.dto");
const update_product_page_dto_1 = require("./dto/update-product-page.dto");
let ProductPagesController = class ProductPagesController {
    productPagesService;
    constructor(productPagesService) {
        this.productPagesService = productPagesService;
    }
    create(req, createProductPageDto) {
        return this.productPagesService.create(req.user.id, createProductPageDto);
    }
    findAll() {
        return this.productPagesService.findAll();
    }
    findBySlug(slug) {
        return this.productPagesService.findBySlug(slug);
    }
    findByDomain(domain) {
        return this.productPagesService.findByDomain(domain);
    }
    findMyPages(req) {
        return this.productPagesService.findByUserId(req.user.id);
    }
    findOne(id) {
        return this.productPagesService.findOne(id);
    }
    update(id, req, updateProductPageDto) {
        return this.productPagesService.update(id, req.user.id, updateProductPageDto);
    }
    remove(id, req) {
        return this.productPagesService.remove(id, req.user.id);
    }
};
exports.ProductPagesController = ProductPagesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new product page' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Product page created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Slug or domain already taken' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_product_page_dto_1.CreateProductPageDto]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all product pages' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product pages retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-slug/:slug'),
    (0, swagger_1.ApiOperation)({ summary: 'Get product page by slug (public endpoint)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product page retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product page not found' }),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "findBySlug", null);
__decorate([
    (0, common_1.Get)('by-domain'),
    (0, swagger_1.ApiOperation)({ summary: 'Get product page by custom domain (public endpoint)' }),
    (0, swagger_1.ApiQuery)({ name: 'domain', description: 'Custom domain to search for' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product page retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product page not found' }),
    __param(0, (0, common_1.Query)('domain')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "findByDomain", null);
__decorate([
    (0, common_1.Get)('my-pages'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user product pages' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product pages retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "findMyPages", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get product page by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product page retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product page not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update product page' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product page updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not the owner' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product page not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Slug or domain already taken' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, update_product_page_dto_1.UpdateProductPageDto]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete product page' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Product page deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not the owner' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Product page not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ProductPagesController.prototype, "remove", null);
exports.ProductPagesController = ProductPagesController = __decorate([
    (0, swagger_1.ApiTags)('Product Pages'),
    (0, common_1.Controller)('product-pages'),
    __metadata("design:paramtypes", [product_pages_service_1.ProductPagesService])
], ProductPagesController);
//# sourceMappingURL=product-pages.controller.js.map