(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{1053:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(4568),s=a(7620),l=a(3928);let n=s.forwardRef((e,t)=>{let{className:a,label:s,error:n,helperText:i,type:o,...c}=e;return(0,r.jsxs)("div",{className:"space-y-1",children:[s&&(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:s}),(0,r.jsx)("input",{type:o,className:(0,l.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus-visible:ring-red-500",a),ref:t,...c}),n&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:n}),i&&!n&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:i})]})});n.displayName="Input"},2921:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var r=a(4568),s=a(7620),l=a(3928);let n=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm",a),...n,children:s})});n.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-gray-500",a),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",a),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"},2942:(e,t,a)=>{"use strict";var r=a(2418);a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},3015:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(4568),s=a(7620),l=a(7261),n=a.n(l),i=a(2942),o=a(9344),c=a(9080),d=a(1053),u=a(2921);function m(){let[e,t]=(0,s.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",phoneNumber:""}),[a,l]=(0,s.useState)(!1),[m,h]=(0,s.useState)(""),{signUp:x}=(0,o.A)(),p=(0,i.useRouter)(),f=a=>{t({...e,[a.target.name]:a.target.value})},g=async t=>{if(t.preventDefault(),l(!0),h(""),e.password!==e.confirmPassword){h("كلمات المرور غير متطابقة"),l(!1);return}if(e.password.length<6){h("كلمة المرور يجب أن تكون 6 أحرف على الأقل"),l(!1);return}try{await x(e.email,e.password,{firstName:e.firstName,lastName:e.lastName,phoneNumber:e.phoneNumber}),p.push("/auth/verify-email")}catch(e){h(e instanceof Error?e.message:"فشل في إنشاء الحساب")}finally{l(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(n(),{href:"/",className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"ش"})}),(0,r.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"شاريو"})]})}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"text-center",children:[(0,r.jsx)(u.ZB,{className:"text-2xl font-bold",children:"إنشاء حساب جديد"}),(0,r.jsx)(u.BT,{children:"أنشئ حسابك المجاني وابدأ في بناء متجرك الإلكتروني"})]}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[m&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:m}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(d.p,{label:"الاسم الأول",name:"firstName",value:e.firstName,onChange:f,required:!0,placeholder:"أحمد"}),(0,r.jsx)(d.p,{label:"اسم العائلة",name:"lastName",value:e.lastName,onChange:f,required:!0,placeholder:"بن علي"})]}),(0,r.jsx)(d.p,{label:"البريد الإلكتروني",type:"email",name:"email",value:e.email,onChange:f,required:!0,placeholder:"<EMAIL>"}),(0,r.jsx)(d.p,{label:"رقم الهاتف",type:"tel",name:"phoneNumber",value:e.phoneNumber,onChange:f,placeholder:"+213555123456",helperText:"اختياري - يمكنك إضافته لاحقاً"}),(0,r.jsx)(d.p,{label:"كلمة المرور",type:"password",name:"password",value:e.password,onChange:f,required:!0,placeholder:"••••••••",helperText:"6 أحرف على الأقل"}),(0,r.jsx)(d.p,{label:"تأكيد كلمة المرور",type:"password",name:"confirmPassword",value:e.confirmPassword,onChange:f,required:!0,placeholder:"••••••••"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"mr-2 block text-sm text-gray-900",children:["أوافق على"," ",(0,r.jsx)(n(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"شروط الاستخدام"})," ","و"," ",(0,r.jsx)(n(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"سياسة الخصوصية"})]})]}),(0,r.jsx)(c.$,{type:"submit",loading:a,className:"w-full",size:"lg",children:"إنشاء الحساب"})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,r.jsx)(n(),{href:"/auth/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"تسجيل الدخول"})]})})]})]})]})]})})}},3928:(e,t,a)=>{"use strict";a.d(t,{cn:()=>l});var r=a(2987),s=a(607);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}},4635:(e,t,a)=>{Promise.resolve().then(a.bind(a,3015))},9080:(e,t,a)=>{"use strict";a.d(t,{$:()=>n});var r=a(4568),s=a(7620),l=a(3928);let n=s.forwardRef((e,t)=>{let{className:a,variant:s="primary",size:n="md",loading:i,children:o,disabled:c,...d}=e;return(0,r.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[n],a),ref:t,disabled:c||i,...d,children:[i&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});n.displayName="Button"},9344:(e,t,a)=>{"use strict";a.d(t,{O:()=>m,A:()=>h});var r=a(4568),s=a(7620);let l=(0,a(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),n=async()=>{let{error:e}=await l.auth.signOut();return{error:e}},i=async()=>{let{data:{user:e},error:t}=await l.auth.getUser();return{user:e,error:t}},o=a(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let c={validateToken:e=>o.post("/auth/validate",{token:e})};var d=a(5317);let u=(0,s.createContext)(void 0);function m(e){let{children:t}=e,[a,o]=(0,s.useState)(null),[m,h]=(0,s.useState)(!0),x=async()=>{try{let{user:e}=await i();if(o(e),e){let{data:{session:e}}=await l.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await c.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),o(null),localStorage.removeItem("auth_token")}};(0,s.useEffect)(()=>{x().finally(()=>h(!1));let{data:{subscription:e}}=l.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){o(t.user);try{let e=await c.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(o(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let p=async(e,t)=>{try{var a;let{data:r,error:s}=await l.auth.signInWithPassword({email:e,password:t});if(s)throw s;if(null==(a=r.session)?void 0:a.access_token){let e=await c.validateToken(r.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الدخول"),e}},f=async(e,t,a)=>{try{let{error:r}=await l.auth.signUp({email:e,password:t,options:{data:a}});if(r)throw r;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(t){let e=t instanceof Error?t.message:"فشل في إنشاء الحساب";throw d.Ay.error(e),t}},g=async()=>{try{await n(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الخروج";throw d.Ay.error(e),t}};return(0,r.jsx)(u.Provider,{value:{user:a,loading:m,signIn:p,signUp:f,signOut:g,refreshUser:x},children:t})}function h(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[286,661,587,315,358],()=>t(4635)),_N_E=e.O()}]);