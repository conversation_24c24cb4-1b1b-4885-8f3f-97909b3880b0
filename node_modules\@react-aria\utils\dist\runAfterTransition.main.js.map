{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAED,uEAAuE;AACvE,6EAA6E;AAC7E,iFAAiF;AACjF,mFAAmF;AACnF,mFAAmF;AACnF,wCAAwC;AACxC,IAAI,6CAAuB,IAAI;AAE/B,wEAAwE;AACxE,IAAI,4CAAsB,IAAI;AAE9B,SAAS;IACP,IAAI,OAAO,WAAW,aACpB;IAGF,SAAS,kBAAkB,KAAY;QACrC,OAAO,kBAAkB;IAC3B;IAEA,IAAI,oBAAoB,CAAC;QACvB,IAAI,CAAC,kBAAkB,MAAM,CAAC,EAAE,MAAM,EACpC;QAEF,+DAA+D;QAC/D,IAAI,cAAc,2CAAqB,GAAG,CAAC,EAAE,MAAM;QACnD,IAAI,CAAC,aAAa;YAChB,cAAc,IAAI;YAClB,2CAAqB,GAAG,CAAC,EAAE,MAAM,EAAE;YAEnC,+FAA+F;YAC/F,yGAAyG;YACzG,mGAAmG;YACnG,EAAE,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,iBAAiB;gBAC7D,MAAM;YACR;QACF;QAEA,YAAY,GAAG,CAAC,EAAE,YAAY;IAChC;IAEA,IAAI,kBAAkB,CAAC;QACrB,IAAI,CAAC,kBAAkB,MAAM,CAAC,EAAE,MAAM,EACpC;QAEF,yDAAyD;QACzD,IAAI,aAAa,2CAAqB,GAAG,CAAC,EAAE,MAAM;QAClD,IAAI,CAAC,YACH;QAGF,WAAW,MAAM,CAAC,EAAE,YAAY;QAEhC,2GAA2G;QAC3G,IAAI,WAAW,IAAI,KAAK,GAAG;YACzB,EAAE,MAAM,CAAC,mBAAmB,CAAC,oBAAoB;YACjD,2CAAqB,MAAM,CAAC,EAAE,MAAM;QACtC;QAEA,kEAAkE;QAClE,IAAI,2CAAqB,IAAI,KAAK,GAAG;YACnC,KAAK,IAAI,MAAM,0CACb;YAGF,0CAAoB,KAAK;QAC3B;IACF;IAEA,SAAS,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;IAChD,SAAS,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;AAClD;AAEA,IAAI,OAAO,aAAa;IACtB,IAAI,SAAS,UAAU,KAAK,WAC1B;SAEA,SAAS,gBAAgB,CAAC,oBAAoB;;AAIlD;;;;CAIC,GACD,SAAS;IACP,KAAK,MAAM,CAAC,YAAY,IAAI,2CAC1B,sFAAsF;IACtF,kFAAkF;IAClF,IAAI,iBAAiB,eAAe,CAAC,YAAY,WAAW,EAC1D,2CAAqB,MAAM,CAAC;AAGlC;AAEO,SAAS,0CAAmB,EAAc;IAC/C,4EAA4E;IAC5E,sBAAsB;QACpB;QACA,gEAAgE;QAChE,+EAA+E;QAC/E,IAAI,2CAAqB,IAAI,KAAK,GAChC;aAEA,0CAAoB,GAAG,CAAC;IAE5B;AACF", "sources": ["packages/@react-aria/utils/src/runAfterTransition.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet transitionsByElement = new Map<EventTarget, Set<string>>();\n\n// A list of callbacks to call once there are no transitioning elements.\nlet transitionCallbacks = new Set<() => void>();\n\nfunction setupGlobalEvents() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  function isTransitionEvent(event: Event): event is TransitionEvent {\n    return 'propertyName' in event;\n  }\n\n  let onTransitionStart = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Add the transitioning property to the list for this element.\n    let transitions = transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      transitionsByElement.set(e.target, transitions);\n\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n\n    transitions.add(e.propertyName);\n  };\n\n  let onTransitionEnd = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Remove property from list of transitioning properties.\n    let properties = transitionsByElement.get(e.target);\n    if (!properties) {\n      return;\n    }\n\n    properties.delete(e.propertyName);\n\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      transitionsByElement.delete(e.target);\n    }\n\n    // If no transitioning elements, call all of the queued callbacks.\n    if (transitionsByElement.size === 0) {\n      for (let cb of transitionCallbacks) {\n        cb();\n      }\n\n      transitionCallbacks.clear();\n    }\n  };\n\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\n\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') {\n    setupGlobalEvents();\n  } else {\n    document.addEventListener('DOMContentLoaded', setupGlobalEvents);\n  }\n}\n\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction cleanupDetachedElements() {\n  for (const [eventTarget] of transitionsByElement) {\n    // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n    // the explicit instanceof check, since it may be different in different contexts.\n    if ('isConnected' in eventTarget && !eventTarget.isConnected) {\n      transitionsByElement.delete(eventTarget);\n    }\n  }\n}\n\nexport function runAfterTransition(fn: () => void): void {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if (transitionsByElement.size === 0) {\n      fn();\n    } else {\n      transitionCallbacks.add(fn);\n    }\n  });\n}\n"], "names": [], "version": 3, "file": "runAfterTransition.main.js.map"}