import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { Request as ExpressRequest } from 'express';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    findAll(): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    getProfile(req: ExpressRequest): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getUserProductPages(req: ExpressRequest): Promise<{
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        published: boolean;
    }[]>;
    updateProfile(req: ExpressRequest, updateUserDto: UpdateUserDto): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    findOne(id: string): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: string): Promise<{
        id: string;
        email: string;
        firstName: string | null;
        lastName: string | null;
        phoneNumber: string | null;
        role: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
}
