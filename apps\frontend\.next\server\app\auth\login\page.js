(()=>{var e={};e.id=859,e.ids=[859],e.modules={2984:(e,t,r)=>{"use strict";var s=r(860);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9958:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(24332),i=r(48819),a=r(67851),n=r.n(a),l=r(97540),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,37349)),"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,20685)),"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,9699))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37349:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SHARYOU\\\\apps\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\auth\\login\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63171:(e,t,r)=>{Promise.resolve().then(r.bind(r,95599))},74065:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(13486),i=r(60159),a=r.n(i),n=r(26518);let l=a().forwardRef(({className:e,label:t,error:r,helperText:i,type:a,...l},o)=>(0,s.jsxs)("div",{className:"space-y-1",children:[t&&(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:t}),(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",e),ref:o,...l}),r&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:r}),i&&!r&&(0,s.jsx)("p",{className:"text-sm text-gray-500",children:i})]}));l.displayName="Input"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80203:(e,t,r)=>{Promise.resolve().then(r.bind(r,37349))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95599:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(13486),i=r(60159),a=r(49989),n=r.n(a),l=r(2984),o=r(2518),d=r(26973),c=r(74065),u=r(73115);function p(){let[e,t]=(0,i.useState)(""),[r,a]=(0,i.useState)(""),[p,x]=(0,i.useState)(!1),[m,h]=(0,i.useState)(""),{signIn:f}=(0,o.A)(),b=(0,l.useRouter)(),g=async t=>{t.preventDefault(),x(!0),h("");try{await f(e,r),b.push("/dashboard")}catch(e){h(e.message||"فشل في تسجيل الدخول")}finally{x(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(n(),{href:"/",className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-2xl",children:"ش"})}),(0,s.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"شاريو"})]})}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsxs)(u.aR,{className:"text-center",children:[(0,s.jsx)(u.ZB,{className:"text-2xl font-bold",children:"تسجيل الدخول"}),(0,s.jsx)(u.BT,{children:"ادخل إلى حسابك للوصول إلى لوحة التحكم"})]}),(0,s.jsxs)(u.Wu,{children:[(0,s.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[m&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:m}),(0,s.jsx)(c.p,{label:"البريد الإلكتروني",type:"email",value:e,onChange:e=>t(e.target.value),required:!0,placeholder:"<EMAIL>"}),(0,s.jsx)(c.p,{label:"كلمة المرور",type:"password",value:r,onChange:e=>a(e.target.value),required:!0,placeholder:"••••••••"}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"mr-2 block text-sm text-gray-900",children:"تذكرني"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(n(),{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"نسيت كلمة المرور؟"})})]}),(0,s.jsx)(d.$,{type:"submit",loading:p,className:"w-full",size:"lg",children:"تسجيل الدخول"})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["ليس لديك حساب؟"," ",(0,s.jsx)(n(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"إنشاء حساب جديد"})]})})]})]})]})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,667,661,884],()=>r(9958));module.exports=s})();