{"title": "TSDoc Configuration", "description": "Describes the TSDoc configuration for a TypeScript project", "type": "object", "properties": {"$schema": {"description": "Part of the JSON Schema standard, this optional keyword declares the URL of the schema that the file conforms to. Editors may download the schema and use it to perform syntax highlighting.", "type": "string"}, "extends": {"description": "Optionally specifies one or more JSON config files that will be combined with this file.  This provides a way for standard settings to be shared across multiple projects.  Important: The \"extends\" paths are resolved using NodeJS module resolution, so a path to a local file MUST be prefixed with \"./\".", "type": "array", "items": {"type": "string"}}, "noStandardTags": {"description": "By default, the config file loader will predefine all of the standardized TSDoc tags.  To disable this and start with a completely empty configuration, set \"noStandardTags\"=true.", "type": "boolean"}, "tagDefinitions": {"description": "Additional tags to support when parsing documentation comments with TSDoc.", "type": "array", "items": {"$ref": "#/definitions/tsdocTagDefinition"}}, "supportedHtmlElements": {"description": "The HTML element names that are supported in this configuration. Used in conjunction with the \"reportUnsupportedHtmlElements\" setting.", "type": "array", "items": {"type": "string", "pattern": "^[a-zA-Z0-9-]+$"}}, "reportUnsupportedHtmlElements": {"description": "Whether an error should be reported when an unsupported HTML element is encountered in a doc comment. Defaults to \"true\" if the \"supportedHtmlElements\" field is present in this file, \"false\" if not.", "type": "boolean"}, "supportForTags": {"description": "A collection of key/value pairs.  The key is a TSDoc tag name (e.g. \"@myTag\") that must be defined in this configuration.  The value is a boolean indicating whether the tag is supported.  The TSDoc parser may report warnings when unsupported tags are encountered.  If \"supportForTags\" is specified for at least one tag, then the \"reportUnsupportedTags\" validation check is enabled by default.", "type": "object", "patternProperties": {"@[a-zA-Z][a-zA-Z0-9]*$": {"type": "boolean"}}}}, "required": ["$schema"], "additionalProperties": false, "definitions": {"tsdocTagDefinition": {"description": "Configuration for a custom supported TSDoc tag.", "type": "object", "properties": {"tagName": {"description": "Name of the custom tag. TSDoc tag names start with an at-sign (@) followed by ASCII letters using camelCase capitalization.", "type": "string"}, "syntaxKind": {"description": "Syntax kind of the custom tag. \"inline\" means that this tag can appear inside other documentation sections (example: {@link}). \"block\" means that this tag starts a new documentation section (example: @remarks). \"modifier\" means that this tag's presence indicates an aspect of the associated API item (example: @internal).", "type": "string", "enum": ["inline", "block", "modifier"]}, "allowMultiple": {"description": "If true, then this tag may appear multiple times in a doc comment. By default, a tag may only appear once.", "type": "boolean"}}, "required": ["tagName", "syntaxKind"], "additionalProperties": false}}}