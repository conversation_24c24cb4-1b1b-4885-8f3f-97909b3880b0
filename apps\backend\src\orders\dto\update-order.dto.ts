import { IsOptional, IsEnum, IsString, ValidateNested } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AddressDto } from '../../common/dto/address.dto';
import { CustomerInfoDto } from '../../common/dto/customer-info.dto';

export class UpdateOrderDto {
  @ApiPropertyOptional({ description: 'Order status' })
  @IsOptional()
  @IsEnum(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
  status?: string;

  @ApiPropertyOptional({ description: 'Payment status' })
  @IsOptional()
  @IsEnum(['pending', 'paid', 'failed', 'refunded', 'partially_refunded'])
  paymentStatus?: string;

  @ApiPropertyOptional({ description: 'Order notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({ type: AddressDto, description: 'Shipping address' })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  shippingAddress?: AddressDto;

  @ApiPropertyOptional({ type: AddressDto, description: 'Billing address' })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  billingAddress?: AddressDto;

  @ApiPropertyOptional({ type: CustomerInfoDto, description: 'Customer information' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerInfoDto)
  customerInfo?: CustomerInfoDto;
}
