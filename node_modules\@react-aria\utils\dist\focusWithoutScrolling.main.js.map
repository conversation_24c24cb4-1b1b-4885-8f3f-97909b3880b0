{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,SAAS,0CAAsB,OAAyB;IAC7D,IAAI,+CACF,QAAQ,KAAK,CAAC;QAAC,eAAe;IAAI;SAC7B;QACL,IAAI,qBAAqB,4CAAsB;QAC/C,QAAQ,KAAK;QACb,4CAAsB;IACxB;AACF;AAEA,IAAI,oDAA8C;AAClD,SAAS;IACP,IAAI,qDAA+B,MAAM;QACvC,oDAA8B;QAC9B,IAAI;YACF,IAAI,YAAY,SAAS,aAAa,CAAC;YACvC,UAAU,KAAK,CAAC;gBACd,IAAI,iBAAgB;oBAClB,oDAA8B;oBAC9B,OAAO;gBACT;YACF;QACF,EAAE,OAAM;QACN,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAEA,SAAS,4CAAsB,OAAyB;IACtD,IAAI,SAAS,QAAQ,UAAU;IAC/B,IAAI,qBAA0C,EAAE;IAChD,IAAI,uBAAuB,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAEhF,MAAO,kBAAkB,eAAe,WAAW,qBAAsB;QACvE,IACE,OAAO,YAAY,GAAG,OAAO,YAAY,IACzC,OAAO,WAAW,GAAG,OAAO,WAAW,EAEvC,mBAAmB,IAAI,CAAC;YACtB,SAAS;YACT,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;QAC/B;QAEF,SAAS,OAAO,UAAU;IAC5B;IAEA,IAAI,gCAAgC,aAClC,mBAAmB,IAAI,CAAC;QACtB,SAAS;QACT,WAAW,qBAAqB,SAAS;QACzC,YAAY,qBAAqB,UAAU;IAC7C;IAGF,OAAO;AACT;AAEA,SAAS,4CAAsB,kBAAuC;IACpE,KAAK,IAAI,WAAC,OAAO,aAAE,SAAS,cAAE,UAAU,EAAC,IAAI,mBAAoB;QAC/D,QAAQ,SAAS,GAAG;QACpB,QAAQ,UAAU,GAAG;IACvB;AACF", "sources": ["packages/@react-aria/utils/src/focusWithoutScrolling.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\n\n// This is a polyfill for element.focus({preventScroll: true});\n// Currently necessary for Safari and old Edge:\n// https://caniuse.com/#feat=mdn-api_htmlelement_focus_preventscroll_option\n// See https://bugs.webkit.org/show_bug.cgi?id=178583\n//\n\n// Original licensing for the following methods can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/calvellido/focus-options-polyfill\n\ninterface ScrollableElement {\n  element: HTMLElement,\n  scrollTop: number,\n  scrollLeft: number\n}\n\nexport function focusWithoutScrolling(element: FocusableElement): void {\n  if (supportsPreventScroll()) {\n    element.focus({preventScroll: true});\n  } else {\n    let scrollableElements = getScrollableElements(element);\n    element.focus();\n    restoreScrollPosition(scrollableElements);\n  }\n}\n\nlet supportsPreventScrollCached: boolean | null = null;\nfunction supportsPreventScroll() {\n  if (supportsPreventScrollCached == null) {\n    supportsPreventScrollCached = false;\n    try {\n      let focusElem = document.createElement('div');\n      focusElem.focus({\n        get preventScroll() {\n          supportsPreventScrollCached = true;\n          return true;\n        }\n      });\n    } catch {\n      // Ignore\n    }\n  }\n\n  return supportsPreventScrollCached;\n}\n\nfunction getScrollableElements(element: FocusableElement): ScrollableElement[] {\n  let parent = element.parentNode;\n  let scrollableElements: ScrollableElement[] = [];\n  let rootScrollingElement = document.scrollingElement || document.documentElement;\n\n  while (parent instanceof HTMLElement && parent !== rootScrollingElement) {\n    if (\n      parent.offsetHeight < parent.scrollHeight ||\n      parent.offsetWidth < parent.scrollWidth\n    ) {\n      scrollableElements.push({\n        element: parent,\n        scrollTop: parent.scrollTop,\n        scrollLeft: parent.scrollLeft\n      });\n    }\n    parent = parent.parentNode;\n  }\n\n  if (rootScrollingElement instanceof HTMLElement) {\n    scrollableElements.push({\n      element: rootScrollingElement,\n      scrollTop: rootScrollingElement.scrollTop,\n      scrollLeft: rootScrollingElement.scrollLeft\n    });\n  }\n\n  return scrollableElements;\n}\n\nfunction restoreScrollPosition(scrollableElements: ScrollableElement[]) {\n  for (let {element, scrollTop, scrollLeft} of scrollableElements) {\n    element.scrollTop = scrollTop;\n    element.scrollLeft = scrollLeft;\n  }\n}\n"], "names": [], "version": 3, "file": "focusWithoutScrolling.main.js.map"}