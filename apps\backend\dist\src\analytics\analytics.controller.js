"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const analytics_service_1 = require("./analytics.service");
let AnalyticsController = class AnalyticsController {
    analyticsService;
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    getDashboardMetrics(productPageId, req) {
        return this.analyticsService.getDashboardMetrics(productPageId, req.user.id);
    }
    getRevenueChart(productPageId, req, days) {
        const daysNumber = days ? parseInt(days, 10) : 30;
        return this.analyticsService.getRevenueChart(productPageId, req.user.id, daysNumber);
    }
    getOrderStatusDistribution(productPageId, req) {
        return this.analyticsService.getOrderStatusDistribution(productPageId, req.user.id);
    }
    recordPageView(productPageId) {
        return this.analyticsService.recordPageView(productPageId);
    }
    recordVisitor(productPageId) {
        return this.analyticsService.recordVisitor(productPageId);
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('dashboard/:productPageId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get dashboard metrics for a product page' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Dashboard metrics retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not the product page owner' }),
    __param(0, (0, common_1.Param)('productPageId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getDashboardMetrics", null);
__decorate([
    (0, common_1.Get)('revenue-chart/:productPageId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get revenue chart data for a product page' }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, description: 'Number of days to include (default: 30)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Revenue chart data retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not the product page owner' }),
    __param(0, (0, common_1.Param)('productPageId')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getRevenueChart", null);
__decorate([
    (0, common_1.Get)('order-status/:productPageId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get order status distribution for a product page' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Order status distribution retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - not the product page owner' }),
    __param(0, (0, common_1.Param)('productPageId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getOrderStatusDistribution", null);
__decorate([
    (0, common_1.Post)('page-view/:productPageId'),
    (0, swagger_1.ApiOperation)({ summary: 'Record a page view (public endpoint)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Page view recorded successfully' }),
    __param(0, (0, common_1.Param)('productPageId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "recordPageView", null);
__decorate([
    (0, common_1.Post)('visitor/:productPageId'),
    (0, swagger_1.ApiOperation)({ summary: 'Record a unique visitor (public endpoint)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Visitor recorded successfully' }),
    __param(0, (0, common_1.Param)('productPageId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "recordVisitor", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics'),
    (0, common_1.Controller)('analytics'),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map