import { ConfigService } from '@nestjs/config';
import { SupabaseClient } from '@supabase/supabase-js';
export declare class SupabaseService {
    private configService;
    private supabase;
    constructor(configService: ConfigService);
    getClient(): SupabaseClient;
    verifyToken(token: string): Promise<import("@supabase/supabase-js").AuthUser>;
    uploadFile(bucket: string, path: string, file: Buffer, contentType: string): Promise<{
        id: string;
        path: string;
        fullPath: string;
    }>;
    deleteFile(bucket: string, path: string): Promise<boolean>;
    getPublicUrl(bucket: string, path: string): string;
}
