{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAIM,SAAS,0CAAiB,IAAa,EAAE,gBAA0B;IACxE,MAAM,gBAA2B,EAAE;IAEnC,MAAO,QAAQ,SAAS,SAAS,eAAe,CAAE;QAChD,IAAI,CAAA,GAAA,yCAAW,EAAE,MAAM,mBACrB,cAAc,IAAI,CAAC;QAErB,OAAO,KAAK,aAAa;IAC3B;IAEA,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/getScrollParents.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParents(node: Element, checkForOverflow?: boolean): Element[] {\n  const scrollParents: Element[] = [];\n\n  while (node && node !== document.documentElement) {\n    if (isScrollable(node, checkForOverflow)) {\n      scrollParents.push(node);\n    }\n    node = node.parentElement as Element;\n  }\n\n  return scrollParents;\n}\n"], "names": [], "version": 3, "file": "getScrollParents.module.js.map"}