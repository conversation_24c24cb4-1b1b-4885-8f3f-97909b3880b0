{"version": 3, "file": "DocNodeTransforms.test.js", "sourceRoot": "", "sources": ["../../src/__tests__/DocNodeTransforms.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAsB,WAAW,EAAE,MAAM,IAAI,CAAC;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAmC,WAAW,EAAE,MAAM,UAAU,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AAEpE,IAAI,CAAC,iCAAiC,EAAE;IACtC,IAAM,MAAM,GAAW;QACrB,KAAK;QACL,IAAI;QACJ,yBAAyB;QACzB,+BAA+B;QAC/B,4BAA4B;QAC5B,eAAe;QACf,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,IAAM,WAAW,GAAgB,IAAI,WAAW,EAAE,CAAC;IACnD,IAAM,aAAa,GAAkB,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IACrE,IAAM,SAAS,GAAY,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5E,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACtD,IAAM,SAAS,GAAiB,SAAyB,CAAC;IAC1D,IAAM,oBAAoB,GAAiB,iBAAiB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;IAC9F,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;AACjF,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { type ParserContext, TSDocParser } from '..';\r\nimport { TestHelpers } from '../parser/__tests__/TestHelpers';\r\nimport { type DocParagraph, type DocNode, DocNodeKind } from '../nodes';\r\nimport { DocNodeTransforms } from '../transforms/DocNodeTransforms';\r\n\r\ntest('01 trimSpacesInParagraphNodes()', () => {\r\n  const buffer: string = [\r\n    '/**',\r\n    ' *',\r\n    ' *    This \\t is    the',\r\n    ' * first   {@mylink}sentence.',\r\n    ' *         This is another',\r\n    ' *sentence.  ',\r\n    ' */'\r\n  ].join('\\n');\r\n\r\n  const tsdocParser: TSDocParser = new TSDocParser();\r\n  const parserContext: ParserContext = tsdocParser.parseString(buffer);\r\n  const firstNode: DocNode = parserContext.docComment.summarySection.nodes[0];\r\n  expect(firstNode.kind).toEqual(DocNodeKind.Paragraph);\r\n  const paragraph: DocParagraph = firstNode as DocParagraph;\r\n  const transformedParagraph: DocParagraph = DocNodeTransforms.trimSpacesInParagraph(paragraph);\r\n  expect(TestHelpers.getDocNodeSnapshot(transformedParagraph)).toMatchSnapshot();\r\n});\r\n"]}