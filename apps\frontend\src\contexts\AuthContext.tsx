'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase, getCurrentUser, signOut as supabaseSignOut } from '@/lib/supabase';
import { authApi } from '@/lib/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, metadata?: any) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshUser = async () => {
    try {
      const { user: currentUser } = await getCurrentUser();
      setUser(currentUser);
      
      if (currentUser) {
        // Get the session to extract the JWT token
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.access_token) {
          // Validate token with our backend and sync user data
          try {
            const response = await authApi.validateToken(session.access_token);
            localStorage.setItem('auth_token', response.data.access_token);
          } catch (error) {
            console.error('Failed to validate token with backend:', error);
          }
        }
      } else {
        localStorage.removeItem('auth_token');
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
      setUser(null);
      localStorage.removeItem('auth_token');
    }
  };

  useEffect(() => {
    // Get initial session
    refreshUser().finally(() => setLoading(false));

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setUser(session.user);
          try {
            const response = await authApi.validateToken(session.access_token);
            localStorage.setItem('auth_token', response.data.access_token);
          } catch (error) {
            console.error('Failed to validate token with backend:', error);
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          localStorage.removeItem('auth_token');
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data.session?.access_token) {
        const response = await authApi.validateToken(data.session.access_token);
        localStorage.setItem('auth_token', response.data.access_token);
      }

      toast.success('تم تسجيل الدخول بنجاح');
    } catch (error: any) {
      toast.error(error.message || 'فشل في تسجيل الدخول');
      throw error;
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) throw error;

      toast.success('تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني');
    } catch (error: any) {
      toast.error(error.message || 'فشل في إنشاء الحساب');
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await supabaseSignOut();
      localStorage.removeItem('auth_token');
      toast.success('تم تسجيل الخروج بنجاح');
    } catch (error: any) {
      toast.error(error.message || 'فشل في تسجيل الخروج');
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
