"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductPagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../common/prisma/prisma.service");
let ProductPagesService = class ProductPagesService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(userId, createProductPageDto) {
        const existingSlug = await this.prisma.productPage.findUnique({
            where: { slug: createProductPageDto.slug },
        });
        if (existingSlug) {
            throw new common_1.ConflictException('Slug is already taken');
        }
        if (createProductPageDto.customDomain) {
            const existingDomain = await this.prisma.productPage.findUnique({
                where: { customDomain: createProductPageDto.customDomain },
            });
            if (existingDomain) {
                throw new common_1.ConflictException('Custom domain is already taken');
            }
        }
        return this.prisma.productPage.create({
            data: {
                ...createProductPageDto,
                userId,
                templateData: createProductPageDto.templateData || {},
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
    }
    async findAll() {
        return this.prisma.productPage.findMany({
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                _count: {
                    select: {
                        products: true,
                        orders: true,
                    },
                },
            },
        });
    }
    async findByUserId(userId) {
        return this.prisma.productPage.findMany({
            where: { userId },
            include: {
                _count: {
                    select: {
                        products: true,
                        orders: true,
                    },
                },
            },
        });
    }
    async findOne(id) {
        const productPage = await this.prisma.productPage.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                _count: {
                    select: {
                        products: true,
                        orders: true,
                    },
                },
            },
        });
        if (!productPage) {
            throw new common_1.NotFoundException(`Product page with ID ${id} not found`);
        }
        return productPage;
    }
    async findBySlug(slug) {
        const productPage = await this.prisma.productPage.findUnique({
            where: { slug },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                products: {
                    where: { isActive: true },
                    include: {
                        images: true,
                        variants: true,
                    },
                },
                categories: {
                    include: {
                        _count: {
                            select: {
                                productCategories: true,
                            },
                        },
                    },
                },
            },
        });
        if (!productPage) {
            throw new common_1.NotFoundException(`Product page with slug ${slug} not found`);
        }
        return productPage;
    }
    async findByDomain(domain) {
        const productPage = await this.prisma.productPage.findUnique({
            where: { customDomain: domain },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                products: {
                    where: { isActive: true },
                    include: {
                        images: true,
                        variants: true,
                    },
                },
                categories: {
                    include: {
                        _count: {
                            select: {
                                productCategories: true,
                            },
                        },
                    },
                },
            },
        });
        if (!productPage) {
            throw new common_1.NotFoundException(`Product page with domain ${domain} not found`);
        }
        return productPage;
    }
    async update(id, userId, updateProductPageDto) {
        const existingProductPage = await this.prisma.productPage.findUnique({
            where: { id },
        });
        if (!existingProductPage) {
            throw new common_1.NotFoundException(`Product page with ID ${id} not found`);
        }
        if (existingProductPage.userId !== userId) {
            throw new common_1.ForbiddenException('You can only update your own product pages');
        }
        if (updateProductPageDto.slug && updateProductPageDto.slug !== existingProductPage.slug) {
            const existingSlug = await this.prisma.productPage.findUnique({
                where: { slug: updateProductPageDto.slug },
            });
            if (existingSlug) {
                throw new common_1.ConflictException('Slug is already taken');
            }
        }
        if (updateProductPageDto.customDomain && updateProductPageDto.customDomain !== existingProductPage.customDomain) {
            const existingDomain = await this.prisma.productPage.findUnique({
                where: { customDomain: updateProductPageDto.customDomain },
            });
            if (existingDomain) {
                throw new common_1.ConflictException('Custom domain is already taken');
            }
        }
        return this.prisma.productPage.update({
            where: { id },
            data: updateProductPageDto,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
    }
    async remove(id, userId) {
        const existingProductPage = await this.prisma.productPage.findUnique({
            where: { id },
        });
        if (!existingProductPage) {
            throw new common_1.NotFoundException(`Product page with ID ${id} not found`);
        }
        if (existingProductPage.userId !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own product pages');
        }
        return this.prisma.productPage.delete({
            where: { id },
        });
    }
    async checkOwnership(productPageId, userId) {
        const productPage = await this.prisma.productPage.findUnique({
            where: { id: productPageId },
            select: { userId: true },
        });
        return productPage?.userId === userId;
    }
};
exports.ProductPagesService = ProductPagesService;
exports.ProductPagesService = ProductPagesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ProductPagesService);
//# sourceMappingURL=product-pages.service.js.map