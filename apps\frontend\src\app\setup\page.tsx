import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CodeBracketIcon,
  CogIcon
} from '@heroicons/react/24/outline';

export default function SetupPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            إعداد منصة شاريو
          </h1>
          <p className="text-lg text-gray-600">
            اتبع هذه الخطوات لإعداد منصة شاريو بالكامل
          </p>
        </div>

        <div className="space-y-8">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ExclamationTriangleIcon className="w-6 h-6 text-yellow-500 ml-2" />
                الحالة الحالية
              </CardTitle>
              <CardDescription>
                أنت حالياً في وضع التجربة. بعض المميزات محدودة.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      وضع التجربة نشط
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        لتفعيل جميع المميزات مثل التسجيل الحقيقي، قاعدة البيانات، والمدفوعات، 
                        يجب إعداد Supabase.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 1: Supabase Setup */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold ml-3">
                  1
                </span>
                إنشاء مشروع Supabase
              </CardTitle>
              <CardDescription>
                إنشاء قاعدة بيانات مجانية وخدمات المصادقة
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5 ml-2" />
                  <div>
                    <p className="font-medium">اذهب إلى Supabase</p>
                    <p className="text-sm text-gray-600">قم بزيارة supabase.com وأنشئ حساب مجاني</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5 ml-2" />
                  <div>
                    <p className="font-medium">أنشئ مشروع جديد</p>
                    <p className="text-sm text-gray-600">اختر اسم "Sharyou" ومنطقة أوروبا الغربية</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="w-5 h-5 text-green-500 mt-0.5 ml-2" />
                  <div>
                    <p className="font-medium">احصل على المفاتيح</p>
                    <p className="text-sm text-gray-600">من الإعدادات → API، انسخ URL والمفاتيح</p>
                  </div>
                </div>
              </div>
              
              <a 
                href="https://supabase.com" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <Button className="w-full">
                  فتح Supabase
                </Button>
              </a>
            </CardContent>
          </Card>

          {/* Step 2: Environment Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-bold ml-3">
                  2
                </span>
                تكوين متغيرات البيئة
              </CardTitle>
              <CardDescription>
                إضافة مفاتيح Supabase إلى ملفات التكوين
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-medium mb-2">Frontend (.env.local):</h4>
                <pre className="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">
{`NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key`}
                </pre>
              </div>
              
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-medium mb-2">Backend (.env):</h4>
                <pre className="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">
{`DATABASE_URL="your-supabase-connection-string"
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"`}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Step 3: Database Setup */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-bold ml-3">
                  3
                </span>
                إعداد قاعدة البيانات
              </CardTitle>
              <CardDescription>
                تشغيل أوامر إنشاء الجداول والبيانات التجريبية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-medium mb-2">الأوامر المطلوبة:</h4>
                <pre className="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">
{`cd apps/backend
npm run db:generate
npm run db:push
npm run db:seed`}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Step 4: Launch */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-sm font-bold ml-3">
                  4
                </span>
                تشغيل المنصة
              </CardTitle>
              <CardDescription>
                بدء تشغيل الخادم والواجهة الأمامية
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-md p-4">
                <h4 className="font-medium mb-2">تشغيل كلا الخادمين:</h4>
                <pre className="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">
{`npm run dev`}
                </pre>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-md">
                  <h5 className="font-medium text-blue-900">الواجهة الأمامية</h5>
                  <p className="text-sm text-blue-700">http://localhost:3000</p>
                </div>
                <div className="bg-green-50 p-4 rounded-md">
                  <h5 className="font-medium text-green-900">API الخلفية</h5>
                  <p className="text-sm text-green-700">http://localhost:3001/api</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Help Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CogIcon className="w-6 h-6 text-gray-500 ml-2" />
                تحتاج مساعدة؟
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-gray-600">
                  إذا واجهت أي مشاكل في الإعداد، تحقق من:
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                  <li>صحة متغيرات البيئة</li>
                  <li>اتصال الإنترنت</li>
                  <li>أن المنافذ 3000 و 3001 متاحة</li>
                  <li>تثبيت جميع التبعيات</li>
                </ul>
                
                <div className="mt-4 p-4 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-800">
                    <strong>ملاحظة:</strong> يمكنك استخدام المنصة في وضع التجربة حتى لو لم تكمل الإعداد.
                    ستتمكن من رؤية جميع الواجهات والتصميم.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
}
