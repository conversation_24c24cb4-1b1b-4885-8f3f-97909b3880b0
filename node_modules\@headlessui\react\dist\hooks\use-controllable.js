import{useRef as o,useState as f}from"react";import{useEvent as a}from'./use-event.js';function T(l,r,c){let[i,s]=f(c),e=l!==void 0,t=o(e),u=o(!1),d=o(!1);return e&&!t.current&&!u.current?(u.current=!0,t.current=e,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!e&&t.current&&!d.current&&(d.current=!0,t.current=e,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[e?l:i,a(n=>(e||s(n),r==null?void 0:r(n)))]}export{T as useControllable};
