{"version": 3, "file": "DocHtmlAttribute.js", "sourceRoot": "", "sources": ["../../src/nodes/DocHtmlAttribute.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,OAAO,EAAE,WAAW,EAA0D,MAAM,WAAW,CAAC;AAEzG,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AA2BvD;;;;GAIG;AACH;IAAsC,oCAAO;IAqB3C;;;OAGG;IACH,0BAAmB,UAA2E;QAC5F,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,kBAAkB;gBAC3C,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBACvC,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;oBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,uBAAuB;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,cAAc,GAAG,IAAI,UAAU,CAAC;gBACnC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,oBAAoB;gBAC7C,OAAO,EAAE,UAAU,CAAC,aAAa;aAClC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,yBAAyB,EAAE,CAAC;gBACzC,KAAI,CAAC,0BAA0B,GAAG,IAAI,UAAU,CAAC;oBAC/C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,yBAAyB;iBAC9C,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAAC;gBAClC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,mBAAmB;gBAC5C,OAAO,EAAE,UAAU,CAAC,YAAY;aACjC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,wBAAwB,EAAE,CAAC;gBACxC,KAAI,CAAC,yBAAyB,GAAG,IAAI,UAAU,CAAC;oBAC9C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,wBAAwB;iBAC7C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;YAC7B,KAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,gBAAgB,CAAC;YAErD,KAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,kBAAkB,CAAC;YAEzD,KAAI,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC;YAC/B,KAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC;QACzD,CAAC;;IACH,CAAC;IAGD,sBAAW,kCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,aAAa,CAAC;QACnC,CAAC;;;OAAA;IAKD,sBAAW,kCAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrD,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAMD,sBAAW,8CAAgB;QAJ3B;;;WAGG;aACH;YACE,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBACzC,IAAI,IAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;oBAChD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC5E,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,CAAC;;;OAAA;IAMD,sBAAW,gDAAkB;QAJ7B;;;WAGG;aACH;YACE,IAAI,IAAI,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;gBAC3C,IAAI,IAAI,CAAC,0BAA0B,KAAK,SAAS,EAAE,CAAC;oBAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAChF,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,CAAC;;;OAAA;IAKD,sBAAW,mCAAK;QAHhB;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACvD,CAAC;YACD,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAMD,sBAAW,+CAAiB;QAJ5B;;;WAGG;aACH;YACE,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBAC1C,IAAI,IAAI,CAAC,yBAAyB,KAAK,SAAS,EAAE,CAAC;oBACjD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC9E,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,CAAC;;;OAAA;IAED,gBAAgB;IACN,0CAAe,GAAzB;QACE,OAAO;YACL,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,0BAA0B;YAC/B,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,yBAAyB;SAC/B,CAAC;IACJ,CAAC;IACH,uBAAC;AAAD,CAAC,AAzJD,CAAsC,OAAO,GAyJ5C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNode, DocNodeKind, type IDocNodeParameters, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocHtmlAttribute}.\r\n */\r\nexport interface IDocHtmlAttributeParameters extends IDocNodeParameters {\r\n  name: string;\r\n  spacingAfterName?: string;\r\n  spacingAfterEquals?: string;\r\n  value: string;\r\n  spacingAfterValue?: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocHtmlAttribute}.\r\n */\r\nexport interface IDocHtmlAttributeParsedParameters extends IDocNodeParsedParameters {\r\n  nameExcerpt: TokenSequence;\r\n  spacingAfterNameExcerpt?: TokenSequence;\r\n\r\n  equalsExcerpt: TokenSequence;\r\n  spacingAfterEqualsExcerpt?: TokenSequence;\r\n\r\n  valueExcerpt: TokenSequence;\r\n  spacingAfterValueExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents an HTML attribute inside a DocHtmlStartTag or DocHtmlEndTag.\r\n *\r\n * Example: `href=\"#\"` inside `<a href=\"#\" />`\r\n */\r\nexport class DocHtmlAttribute extends DocNode {\r\n  // The attribute name\r\n  private _name: string | undefined;\r\n  private readonly _nameExcerpt: DocExcerpt | undefined;\r\n\r\n  private _spacingAfterName: string | undefined;\r\n  private readonly _spacingAfterNameExcerpt: DocExcerpt | undefined;\r\n\r\n  // The \"=\" delimiter\r\n  private readonly _equalsExcerpt: DocExcerpt | undefined;\r\n\r\n  private _spacingAfterEquals: string | undefined;\r\n  private readonly _spacingAfterEqualsExcerpt: DocExcerpt | undefined;\r\n\r\n  // The attribute value including quotation marks\r\n  private _value: string | undefined;\r\n  private readonly _valueExcerpt: DocExcerpt | undefined;\r\n\r\n  private _spacingAfterValue: string | undefined;\r\n  private readonly _spacingAfterValueExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocHtmlAttributeParameters | IDocHtmlAttributeParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._nameExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlAttribute_Name,\r\n        content: parameters.nameExcerpt\r\n      });\r\n      if (parameters.spacingAfterNameExcerpt) {\r\n        this._spacingAfterNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterNameExcerpt\r\n        });\r\n      }\r\n\r\n      this._equalsExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlAttribute_Equals,\r\n        content: parameters.equalsExcerpt\r\n      });\r\n      if (parameters.spacingAfterEqualsExcerpt) {\r\n        this._spacingAfterEqualsExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterEqualsExcerpt\r\n        });\r\n      }\r\n\r\n      this._valueExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.HtmlAttribute_Value,\r\n        content: parameters.valueExcerpt\r\n      });\r\n      if (parameters.spacingAfterValueExcerpt) {\r\n        this._spacingAfterValueExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterValueExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._name = parameters.name;\r\n      this._spacingAfterName = parameters.spacingAfterName;\r\n\r\n      this._spacingAfterEquals = parameters.spacingAfterEquals;\r\n\r\n      this._value = parameters.value;\r\n      this._spacingAfterValue = parameters.spacingAfterValue;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.HtmlAttribute;\r\n  }\r\n\r\n  /**\r\n   * The HTML attribute name.\r\n   */\r\n  public get name(): string {\r\n    if (this._name === undefined) {\r\n      this._name = this._nameExcerpt!.content.toString();\r\n    }\r\n    return this._name;\r\n  }\r\n\r\n  /**\r\n   * Explicit whitespace that a renderer should insert after the HTML attribute name.\r\n   * If undefined, then the renderer can use a formatting rule to generate appropriate spacing.\r\n   */\r\n  public get spacingAfterName(): string | undefined {\r\n    if (this._spacingAfterName === undefined) {\r\n      if (this._spacingAfterNameExcerpt !== undefined) {\r\n        this._spacingAfterName = this._spacingAfterNameExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._spacingAfterName;\r\n  }\r\n\r\n  /**\r\n   * Explicit whitespace that a renderer should insert after the \"=\".\r\n   * If undefined, then the renderer can use a formatting rule to generate appropriate spacing.\r\n   */\r\n  public get spacingAfterEquals(): string | undefined {\r\n    if (this._spacingAfterEquals === undefined) {\r\n      if (this._spacingAfterEqualsExcerpt !== undefined) {\r\n        this._spacingAfterEquals = this._spacingAfterEqualsExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._spacingAfterEquals;\r\n  }\r\n\r\n  /**\r\n   * The HTML attribute value.\r\n   */\r\n  public get value(): string {\r\n    if (this._value === undefined) {\r\n      this._value = this._valueExcerpt!.content.toString();\r\n    }\r\n    return this._value;\r\n  }\r\n\r\n  /**\r\n   * Explicit whitespace that a renderer should insert after the HTML attribute name.\r\n   * If undefined, then the renderer can use a formatting rule to generate appropriate spacing.\r\n   */\r\n  public get spacingAfterValue(): string | undefined {\r\n    if (this._spacingAfterValue === undefined) {\r\n      if (this._spacingAfterValueExcerpt !== undefined) {\r\n        this._spacingAfterValue = this._spacingAfterValueExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._spacingAfterValue;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._nameExcerpt,\r\n      this._spacingAfterNameExcerpt,\r\n      this._equalsExcerpt,\r\n      this._spacingAfterEqualsExcerpt,\r\n      this._valueExcerpt,\r\n      this._spacingAfterValueExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}