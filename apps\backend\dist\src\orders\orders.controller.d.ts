import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    create(req: any, createOrderDto: CreateOrderDto): Promise<any>;
    findAll(productPageId?: string): Promise<any>;
    findMyOrders(req: any): Promise<any>;
    findByProductPage(productPageId: string, req: any): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, req: any, updateOrderDto: UpdateOrderDto): Promise<any>;
}
