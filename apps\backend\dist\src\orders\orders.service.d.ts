import { PrismaService } from '../common/prisma/prisma.service';
import { ProductPagesService } from '../product-pages/product-pages.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
export declare class OrdersService {
    private prisma;
    private productPagesService;
    constructor(prisma: PrismaService, productPagesService: ProductPagesService);
    create(userId: string | null, createOrderDto: CreateOrderDto): Promise<any>;
    findAll(productPageId?: string): Promise<any>;
    findByProductPage(productPageId: string, userId: string): Promise<any>;
    findOne(id: string): Promise<any>;
    update(id: string, userId: string, updateOrderDto: UpdateOrderDto): Promise<any>;
    findByUser(userId: string): Promise<any>;
}
