import { PrismaService } from '../common/prisma/prisma.service';
import { CreateProductPageDto } from './dto/create-product-page.dto';
import { UpdateProductPageDto } from './dto/update-product-page.dto';
export declare class ProductPagesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(userId: string, createProductPageDto: CreateProductPageDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        };
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    }>;
    findAll(): Promise<({
        user: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        };
        _count: {
            orders: number;
            products: number;
        };
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    })[]>;
    findByUserId(userId: string): Promise<({
        _count: {
            orders: number;
            products: number;
        };
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    })[]>;
    findOne(id: string): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        };
        _count: {
            orders: number;
            products: number;
        };
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    }>;
    findBySlug(slug: string): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        };
        products: ({
            images: {
                id: string;
                createdAt: Date;
                productId: string;
                url: string;
                altText: string | null;
                position: number;
            }[];
            variants: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                isActive: boolean;
                price: import("@prisma/client/runtime/library").Decimal;
                sku: string | null;
                barcode: string | null;
                weight: number | null;
                stock: number;
                productId: string;
            }[];
        } & {
            description: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            tags: string[];
            isActive: boolean;
            productPageId: string;
            nameAr: string | null;
            nameFr: string | null;
            descriptionAr: string | null;
            descriptionFr: string | null;
            price: import("@prisma/client/runtime/library").Decimal;
            compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            imageUrl: string | null;
            stock: number;
        })[];
        categories: ({
            _count: {
                productCategories: number;
            };
        } & {
            description: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            slug: string;
            productPageId: string;
            nameAr: string | null;
            nameFr: string | null;
            imageUrl: string | null;
            position: number;
            parentId: string | null;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    }>;
    findByDomain(domain: string): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        };
        products: ({
            images: {
                id: string;
                createdAt: Date;
                productId: string;
                url: string;
                altText: string | null;
                position: number;
            }[];
            variants: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                isActive: boolean;
                price: import("@prisma/client/runtime/library").Decimal;
                sku: string | null;
                barcode: string | null;
                weight: number | null;
                stock: number;
                productId: string;
            }[];
        } & {
            description: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            tags: string[];
            isActive: boolean;
            productPageId: string;
            nameAr: string | null;
            nameFr: string | null;
            descriptionAr: string | null;
            descriptionFr: string | null;
            price: import("@prisma/client/runtime/library").Decimal;
            compareAtPrice: import("@prisma/client/runtime/library").Decimal | null;
            sku: string | null;
            barcode: string | null;
            weight: number | null;
            imageUrl: string | null;
            stock: number;
        })[];
        categories: ({
            _count: {
                productCategories: number;
            };
        } & {
            description: string | null;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            slug: string;
            productPageId: string;
            nameAr: string | null;
            nameFr: string | null;
            imageUrl: string | null;
            position: number;
            parentId: string | null;
        })[];
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    }>;
    update(id: string, userId: string, updateProductPageDto: UpdateProductPageDto): Promise<{
        user: {
            id: string;
            email: string;
            firstName: string | null;
            lastName: string | null;
        };
    } & {
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    }>;
    remove(id: string, userId: string): Promise<{
        description: string | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        userId: string;
        slug: string;
        customDomain: string | null;
        logoUrl: string | null;
        templateData: import("@prisma/client/runtime/library").JsonValue;
        published: boolean;
    }>;
    checkOwnership(productPageId: string, userId: string): Promise<boolean>;
}
