"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../common/prisma/prisma.service");
const product_pages_service_1 = require("../product-pages/product-pages.service");
let AnalyticsService = class AnalyticsService {
    prisma;
    productPagesService;
    constructor(prisma, productPagesService) {
        this.prisma = prisma;
        this.productPagesService = productPagesService;
    }
    async getDashboardMetrics(productPageId, userId) {
        const hasAccess = await this.productPagesService.checkOwnership(productPageId, userId);
        if (!hasAccess) {
            throw new common_1.ForbiddenException('You can only view analytics for your own product pages');
        }
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const [totalProducts, totalOrders, totalRevenue, ordersLast30Days, ordersLast7Days, revenueLast30Days, revenueLast7Days, topProducts, recentOrders,] = await Promise.all([
            this.prisma.product.count({
                where: { productPageId, isActive: true },
            }),
            this.prisma.order.count({
                where: { productPageId },
            }),
            this.prisma.order.aggregate({
                where: {
                    productPageId,
                    status: { in: ['confirmed', 'processing', 'shipped', 'delivered'] },
                },
                _sum: { totalAmount: true },
            }),
            this.prisma.order.count({
                where: {
                    productPageId,
                    createdAt: { gte: thirtyDaysAgo },
                },
            }),
            this.prisma.order.count({
                where: {
                    productPageId,
                    createdAt: { gte: sevenDaysAgo },
                },
            }),
            this.prisma.order.aggregate({
                where: {
                    productPageId,
                    createdAt: { gte: thirtyDaysAgo },
                    status: { in: ['confirmed', 'processing', 'shipped', 'delivered'] },
                },
                _sum: { totalAmount: true },
            }),
            this.prisma.order.aggregate({
                where: {
                    productPageId,
                    createdAt: { gte: sevenDaysAgo },
                    status: { in: ['confirmed', 'processing', 'shipped', 'delivered'] },
                },
                _sum: { totalAmount: true },
            }),
            this.prisma.orderItem.groupBy({
                by: ['productId'],
                where: {
                    order: { productPageId },
                },
                _sum: { quantity: true },
                _count: { productId: true },
                orderBy: { _sum: { quantity: 'desc' } },
                take: 5,
            }),
            this.prisma.order.findMany({
                where: { productPageId },
                include: {
                    items: {
                        include: {
                            product: {
                                select: {
                                    id: true,
                                    name: true,
                                    imageUrl: true,
                                },
                            },
                        },
                    },
                    buyer: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                take: 10,
            }),
        ]);
        const topProductsWithDetails = await Promise.all(topProducts.map(async (item) => {
            const product = await this.prisma.product.findUnique({
                where: { id: item.productId },
                select: {
                    id: true,
                    name: true,
                    imageUrl: true,
                    price: true,
                },
            });
            return {
                ...product,
                totalSold: item._sum.quantity || 0,
                orderCount: item._count.productId,
            };
        }));
        return {
            overview: {
                totalProducts,
                totalOrders,
                totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
                ordersLast30Days,
                ordersLast7Days,
                revenueLast30Days: Number(revenueLast30Days._sum.totalAmount || 0),
                revenueLast7Days: Number(revenueLast7Days._sum.totalAmount || 0),
            },
            topProducts: topProductsWithDetails,
            recentOrders,
        };
    }
    async getRevenueChart(productPageId, userId, days = 30) {
        const hasAccess = await this.productPagesService.checkOwnership(productPageId, userId);
        if (!hasAccess) {
            throw new common_1.ForbiddenException('You can only view analytics for your own product pages');
        }
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const orders = await this.prisma.order.findMany({
            where: {
                productPageId,
                createdAt: { gte: startDate },
                status: { in: ['confirmed', 'processing', 'shipped', 'delivered'] },
            },
            select: {
                createdAt: true,
                totalAmount: true,
            },
        });
        const revenueByDate = orders.reduce((acc, order) => {
            const date = order.createdAt.toISOString().split('T')[0];
            if (!acc[date]) {
                acc[date] = 0;
            }
            acc[date] += Number(order.totalAmount);
            return acc;
        }, {});
        const chartData = [];
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            chartData.push({
                date: dateStr,
                revenue: revenueByDate[dateStr] || 0,
            });
        }
        return chartData;
    }
    async getOrderStatusDistribution(productPageId, userId) {
        const hasAccess = await this.productPagesService.checkOwnership(productPageId, userId);
        if (!hasAccess) {
            throw new common_1.ForbiddenException('You can only view analytics for your own product pages');
        }
        const statusCounts = await this.prisma.order.groupBy({
            by: ['status'],
            where: { productPageId },
            _count: { status: true },
        });
        return statusCounts.map(item => ({
            status: item.status,
            count: item._count.status,
        }));
    }
    async recordPageView(productPageId) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        await this.prisma.analyticMetric.upsert({
            where: {
                productPageId_date: {
                    productPageId,
                    date: today,
                },
            },
            update: {
                pageViews: { increment: 1 },
            },
            create: {
                productPageId,
                date: today,
                pageViews: 1,
                visitors: 1,
            },
        });
    }
    async recordVisitor(productPageId) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        await this.prisma.analyticMetric.upsert({
            where: {
                productPageId_date: {
                    productPageId,
                    date: today,
                },
            },
            update: {
                visitors: { increment: 1 },
            },
            create: {
                productPageId,
                date: today,
                visitors: 1,
                pageViews: 1,
            },
        });
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        product_pages_service_1.ProductPagesService])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map