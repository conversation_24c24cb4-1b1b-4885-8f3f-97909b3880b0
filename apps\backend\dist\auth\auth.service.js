"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const supabase_service_1 = require("./supabase.service");
const users_service_1 = require("../users/users.service");
let AuthService = class AuthService {
    jwtService;
    supabaseService;
    usersService;
    constructor(jwtService, supabaseService, usersService) {
        this.jwtService = jwtService;
        this.supabaseService = supabaseService;
        this.usersService = usersService;
    }
    async validateUser(token) {
        try {
            const supabaseUser = await this.supabaseService.verifyToken(token);
            if (!supabaseUser) {
                throw new common_1.UnauthorizedException('Invalid token');
            }
            let user = await this.usersService.findById(supabaseUser.id);
            if (!user) {
                user = await this.usersService.create({
                    id: supabaseUser.id,
                    email: supabaseUser.email,
                    firstName: supabaseUser.user_metadata?.firstName,
                    lastName: supabaseUser.user_metadata?.lastName,
                    phoneNumber: supabaseUser.user_metadata?.phoneNumber,
                });
            }
            return user;
        }
        catch (error) {
            console.error('Authentication error:', error);
            if (error.message?.includes('timeout') || error.message?.includes('fetch failed')) {
                throw new common_1.UnauthorizedException('Authentication service temporarily unavailable');
            }
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
    async generateToken(user) {
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role,
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
            },
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        supabase_service_1.SupabaseService,
        users_service_1.UsersService])
], AuthService);
//# sourceMappingURL=auth.service.js.map