{"name": "sharyou", "version": "1.0.0", "description": "Algerian E-commerce Platform - Multi-tenant SaaS for creating custom product pages", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "npm run dev --workspace=apps/frontend", "dev:backend": "npm run start:dev --workspace=apps/backend", "build": "npm run build --workspaces", "build:frontend": "npm run build --workspace=apps/frontend", "build:backend": "npm run build --workspace=apps/backend", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "type-check": "npm run type-check --workspaces", "clean": "rimraf apps/*/dist apps/*/.next packages/*/dist node_modules/.cache", "db:generate": "npm run db:generate --workspace=apps/backend", "db:push": "npm run db:push --workspace=apps/backend", "db:migrate": "npm run db:migrate --workspace=apps/backend", "db:studio": "npm run db:studio --workspace=apps/backend", "setup": "npm run db:generate && npm run db:push", "test-setup": "node test-setup.js"}, "devDependencies": {"@types/node": "^20.10.5", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ecommerce", "algeria", "multi-tenant", "saas", "nextjs", "<PERSON><PERSON><PERSON>", "supabase"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@nestjs/config": "^4.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.9.0", "@sharyou/types": "^1.0.0", "@sharyou/utils": "^1.0.0", "@supabase/supabase-js": "^2.50.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prisma": "^6.9.0"}}