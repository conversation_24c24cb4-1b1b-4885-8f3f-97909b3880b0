(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{765:(e,t,s)=>{Promise.resolve().then(s.bind(s,3963))},1053:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(4568),a=s(7620),l=s(3928);let i=a.forwardRef((e,t)=>{let{className:s,label:a,error:i,helperText:n,type:o,...c}=e;return(0,r.jsxs)("div",{className:"space-y-1",children:[a&&(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:a}),(0,r.jsx)("input",{type:o,className:(0,l.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i&&"border-red-500 focus-visible:ring-red-500",s),ref:t,...c}),i&&(0,r.jsx)("p",{className:"text-sm text-red-600",children:i}),n&&!i&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:n})]})});i.displayName="Input"},2921:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var r=s(4568),a=s(7620),l=s(3928);let i=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm",s),...i,children:a})});i.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});n.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-gray-500",s),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},2942:(e,t,s)=>{"use strict";var r=s(2418);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},3928:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var r=s(2987),a=s(607);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},3963:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(4568),a=s(7620),l=s(7261),i=s.n(l),n=s(2942),o=s(9344),c=s(9080),d=s(1053),u=s(2921);function m(){let[e,t]=(0,a.useState)(""),[s,l]=(0,a.useState)(""),[m,h]=(0,a.useState)(!1),[x,f]=(0,a.useState)(""),{signIn:p}=(0,o.A)(),g=(0,n.useRouter)(),b=async t=>{t.preventDefault(),h(!0),f("");try{await p(e,s),g.push("/dashboard")}catch(e){f(e.message||"فشل في تسجيل الدخول")}finally{h(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"ش"})}),(0,r.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"شاريو"})]})}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{className:"text-center",children:[(0,r.jsx)(u.ZB,{className:"text-2xl font-bold",children:"تسجيل الدخول"}),(0,r.jsx)(u.BT,{children:"ادخل إلى حسابك للوصول إلى لوحة التحكم"})]}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[x&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:x}),(0,r.jsx)(d.p,{label:"البريد الإلكتروني",type:"email",value:e,onChange:e=>t(e.target.value),required:!0,placeholder:"<EMAIL>"}),(0,r.jsx)(d.p,{label:"كلمة المرور",type:"password",value:s,onChange:e=>l(e.target.value),required:!0,placeholder:"••••••••"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"remember-me",className:"mr-2 block text-sm text-gray-900",children:"تذكرني"})]}),(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)(i(),{href:"/auth/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"نسيت كلمة المرور؟"})})]}),(0,r.jsx)(c.$,{type:"submit",loading:m,className:"w-full",size:"lg",children:"تسجيل الدخول"})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["ليس لديك حساب؟"," ",(0,r.jsx)(i(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"إنشاء حساب جديد"})]})})]})]})]})]})})}},9080:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var r=s(4568),a=s(7620),l=s(3928);let i=a.forwardRef((e,t)=>{let{className:s,variant:a="primary",size:i="md",loading:n,children:o,disabled:c,...d}=e;return(0,r.jsxs)("button",{className:(0,l.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-blue-600 text-white hover:bg-blue-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-transparent hover:bg-gray-50",ghost:"hover:bg-gray-100",danger:"bg-red-600 text-white hover:bg-red-700"}[a],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-11 px-8"}[i],s),ref:t,disabled:c||n,...d,children:[n&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});i.displayName="Button"},9344:(e,t,s)=>{"use strict";s.d(t,{O:()=>m,A:()=>h});var r=s(4568),a=s(7620);let l=(0,s(6154).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),i=async()=>{let{error:e}=await l.auth.signOut();return{error:e}},n=async()=>{let{data:{user:e},error:t}=await l.auth.getUser();return{user:e,error:t}},o=s(9329).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)});let c={validateToken:e=>o.post("/auth/validate",{token:e})};var d=s(5317);let u=(0,a.createContext)(void 0);function m(e){let{children:t}=e,[s,o]=(0,a.useState)(null),[m,h]=(0,a.useState)(!0),x=async()=>{try{let{user:e}=await n();if(o(e),e){let{data:{session:e}}=await l.auth.getSession();if(null==e?void 0:e.access_token)try{let t=await c.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),o(null),localStorage.removeItem("auth_token")}};(0,a.useEffect)(()=>{x().finally(()=>h(!1));let{data:{subscription:e}}=l.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_IN"===e&&(null==t?void 0:t.user)){o(t.user);try{let e=await c.validateToken(t.access_token);localStorage.setItem("auth_token",e.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else"SIGNED_OUT"===e&&(o(null),localStorage.removeItem("auth_token"))});return()=>e.unsubscribe()},[]);let f=async(e,t)=>{try{var s;let{data:r,error:a}=await l.auth.signInWithPassword({email:e,password:t});if(a)throw a;if(null==(s=r.session)?void 0:s.access_token){let e=await c.validateToken(r.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}d.Ay.success("تم تسجيل الدخول بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الدخول"),e}},p=async(e,t,s)=>{try{let{data:r,error:a}=await l.auth.signUp({email:e,password:t,options:{data:s}});if(a)throw a;d.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(e){throw d.Ay.error(e.message||"فشل في إنشاء الحساب"),e}},g=async()=>{try{await i(),localStorage.removeItem("auth_token"),d.Ay.success("تم تسجيل الخروج بنجاح")}catch(e){throw d.Ay.error(e.message||"فشل في تسجيل الخروج"),e}};return(0,r.jsx)(u.Provider,{value:{user:s,loading:m,signIn:f,signUp:p,signOut:g,refreshUser:x},children:t})}function h(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[286,661,587,315,358],()=>t(765)),_N_E=e.O()}]);