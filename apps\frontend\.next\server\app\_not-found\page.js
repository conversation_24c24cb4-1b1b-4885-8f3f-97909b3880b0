(()=>{var e={};e.id=492,e.ids=[492],e.modules={1241:()=>{},2518:(e,t,r)=>{"use strict";r.d(t,{O:()=>p,A:()=>h});var s=r(13486),o=r(60159);let i=(0,r(95681).createBrowserClient)("https://zsayabqzmqrcbpaiwigx.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpzYXlhYnF6bXFyY2JwYWl3aWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4NDYyMzgsImV4cCI6MjA2NTQyMjIzOH0._0GNy1QotOibNRqPEMUPSjkupaORkiMoD9So9aBDTvE"),n=async()=>{let{error:e}=await i.auth.signOut();return{error:e}},a=async()=>{let{data:{user:e},error:t}=await i.auth.getUser();return{user:e,error:t}},u=r(93153).A.create({baseURL:"http://localhost:3003/api",headers:{"Content-Type":"application/json"}});u.interceptors.request.use(e=>{let t=localStorage.getItem("auth_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),u.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(localStorage.removeItem("auth_token"),window.location.href="/auth/login"),Promise.reject(e)));let l={validateToken:e=>u.post("/auth/validate",{token:e})};var c=r(85499);let d=(0,o.createContext)(void 0);function p({children:e}){let[t,r]=(0,o.useState)(null),[u,p]=(0,o.useState)(!0),h=async()=>{try{let{user:e}=await a();if(r(e),e){let{data:{session:e}}=await i.auth.getSession();if(e?.access_token)try{let t=await l.validateToken(e.access_token);localStorage.setItem("auth_token",t.data.access_token)}catch(e){console.error("Failed to validate token with backend:",e)}}else localStorage.removeItem("auth_token")}catch(e){console.error("Error refreshing user:",e),r(null),localStorage.removeItem("auth_token")}},m=async(e,t)=>{try{let{data:r,error:s}=await i.auth.signInWithPassword({email:e,password:t});if(s)throw s;if(r.session?.access_token){let e=await l.validateToken(r.session.access_token);localStorage.setItem("auth_token",e.data.access_token)}c.Ay.success("تم تسجيل الدخول بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الدخول";throw c.Ay.error(e),t}},v=async(e,t,r)=>{try{let{error:s}=await i.auth.signUp({email:e,password:t,options:{data:r}});if(s)throw s;c.Ay.success("تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني")}catch(t){let e=t instanceof Error?t.message:"فشل في إنشاء الحساب";throw c.Ay.error(e),t}},f=async()=>{try{await n(),localStorage.removeItem("auth_token"),c.Ay.success("تم تسجيل الخروج بنجاح")}catch(t){let e=t instanceof Error?t.message:"فشل في تسجيل الخروج";throw c.Ay.error(e),t}};return(0,s.jsx)(d.Provider,{value:{user:t,loading:u,signIn:m,signUp:v,signOut:f,refreshUser:h},children:e})}function h(){let e=(0,o.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:(e,t,r)=>{Promise.resolve().then(r.bind(r,49937))},4946:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69355,23)),Promise.resolve().then(r.t.bind(r,54439,23)),Promise.resolve().then(r.t.bind(r,67851,23)),Promise.resolve().then(r.t.bind(r,94730,23)),Promise.resolve().then(r.t.bind(r,19774,23)),Promise.resolve().then(r.t.bind(r,53170,23)),Promise.resolve().then(r.t.bind(r,20968,23)),Promise.resolve().then(r.t.bind(r,78298,23))},9312:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(24332),o=r(48819),i=r(67851),n=r.n(i),a=r(97540),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,20685)),"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14276:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20685:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>a});var s=r(38828),o=r(93390),i=r.n(o);r(14276);var n=r(52079);let a={title:"Sharyou - منصة التجارة الإلكترونية الجزائرية",description:"أنشئ متجرك الإلكتروني في دقائق مع شاريو - المنصة الجزائرية الرائدة للتجارة الإلكترونية",keywords:"تجارة إلكترونية, متجر إلكتروني, الجزائر, شاريو, بيع أونلاين"};function u({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",children:(0,s.jsx)("body",{className:`${i().variable} font-sans antialiased`,children:(0,s.jsx)(n.Providers,{children:e})})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},49937:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>l});var s=r(13486),o=r(10803),i=r(84364),n=r(85499),a=r(2518),u=r(60159);function l({children:e}){let[t]=(0,u.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return(0,s.jsx)(i.Ht,{client:t,children:(0,s.jsxs)(a.O,{children:[e,(0,s.jsx)(n.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:4e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})}},51802:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30385,23)),Promise.resolve().then(r.t.bind(r,33737,23)),Promise.resolve().then(r.t.bind(r,86081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,35856,23)),Promise.resolve().then(r.t.bind(r,55492,23)),Promise.resolve().then(r.t.bind(r,89082,23)),Promise.resolve().then(r.t.bind(r,45812,23))},52079:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\SHARYOU\\apps\\frontend\\src\\components\\providers\\Providers.tsx","Providers")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},76029:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=76029,e.exports=t},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86013:(e,t,r)=>{Promise.resolve().then(r.bind(r,52079))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,667],()=>r(9312));module.exports=s})();